import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { prerenderPlugin } from "./vite-prerender-plugin";
import removeConsole from "vite-plugin-remove-console";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  // 从环境变量获取代理目标，如果没有则使用默认值
  const proxyTarget = env.REACT_APP_PROXY_TARGET || 'http://**************:48081';

  return {
    base: env.REACT_APP_ENV === 'international' ? '/' : './',
    plugins: [
      react(),
      prerenderPlugin(),
      // 只在生产环境移除 console.log
      ...(mode === 'production' || env.REACT_APP_ENV === 'production' || env.REACT_APP_ENV === 'international'
        ? [removeConsole({
            includes: ['log','error'], // 要移除的 console 方法
          })]
        : [])
    ],
    optimizeDeps: {
      exclude: ["bippy/dist/jsx-dev-runtime", "bippy/dist/jsx-runtime"],
    },
    // 定义全局常量，确保环境变量在构建时被正确替换
    define: {
      'import.meta.env.REACT_APP_ENV': JSON.stringify(env.REACT_APP_ENV),
      'import.meta.env.REACT_APP_API_BASE': JSON.stringify(env.REACT_APP_API_BASE),
      'import.meta.env.REACT_APP_DEFAULT_EMAIL': JSON.stringify(env.REACT_APP_DEFAULT_EMAIL),
      'import.meta.env.REACT_APP_FEEDBACK_PROJECT_ID': JSON.stringify(env.REACT_APP_FEEDBACK_PROJECT_ID),
      'import.meta.env.REACT_APP_IS_XAI': JSON.stringify(env.REACT_APP_IS_XAI),
      'import.meta.env.REACT_APP_SHOW_DEBUG': JSON.stringify(env.REACT_APP_SHOW_DEBUG),
      'import.meta.env.REACT_APP_DOMAIN': JSON.stringify(env.REACT_APP_DOMAIN),
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          // 确保资源文件名包含hash，便于缓存
          assetFileNames: 'assets/[name]-[hash][extname]',
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js'
        }
      }
    },
    server: {
      port: 3000, // 设置你想要的端口号
      proxy: {
        '/dev-api': {
          target: proxyTarget,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev-api/, '')
        }
      }
    },
    // 预览模式的代理配置
    preview: {
      port: 4173,
      host: '0.0.0.0', // 监听所有接口，允许外部访问
      // 配置 SPA 回退，确保所有路由都能正确回退到 index.html
      open: false,
      // 添加中间件来处理 SPA 路由
      middlewareMode: false,
      proxy: {
        '/dev-api': {
          target: proxyTarget,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev-api/, '')
        }
      }
    },
  }
});
