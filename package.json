{"name": "react-vite-tailwind", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "dev:development": "vite --mode development --host 0.0.0.0", "dev:test": "vite --mode test --host 0.0.0.0", "dev:production": "vite --mode production --host 0.0.0.0", "dev:international": "vite --mode international --host 0.0.0.0", "build": "tsc -b && vite build --mode production --outDir dist && cross-env PRERENDER_MODE=production npm run prerender", "build:devs": "vite --mode test --host 0.0.0.0 && tsc -b && vite build --mode development --outDir dist && cross-env PRERENDER_MODE=development npm run prerender", "build:tests": "tsc -b && vite build --mode test --outDir dist && cross-env PRERENDER_MODE=test npm run prerender", "build:prods": "tsc -b && vite build --mode production --outDir dist && cross-env PRERENDER_MODE=production npm run prerender", "build:internationals": "tsc -b && vite build --mode international --outDir dist_international && npm run copy:novax-html:international && cross-env PRERENDER_MODE=international npm run prerender:international", "copy:novax-html": "node scripts/copy-novax-html.js", "copy:novax-html:international": "node scripts/copy-novax-html.js --target dist_international", "prerender:international": "cross-env DIST_DIR=dist_international node scripts/prerender.js", "build:spa": "tsc -b && vite build --mode production --outDir dist", "build:test": "concurrently --kill-others --success 1 \"npm run dev:test\" \"npm run build:tests\"", "build:dev": "concurrently --kill-others --success 1 \"npm run dev:development\" \"npm run build:devs\"", "build:prod": "concurrently --kill-others --success 1 \"npm run dev:production\" \"npm run build:prods\"", "build:international": "concurrently --kill-others --success 1 \"npm run dev:international\" \"npm run build:internationals\"", "prerender": "node scripts/prerender.js", "prerender:verify": "node scripts/verify-prerender.js", "nginx:verify": "node scripts/verify-nginx-prerender.js", "nginx:test": "node scripts/test-prerender.js", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write", "preview": "vite preview"}, "dependencies": {"@types/react-katex": "^3.0.4", "@types/react-syntax-highlighter": "^15.5.13", "ahooks": "^3.8.5", "antd": "^5.26.1", "axios": "^1.10.0", "i18next": "^25.3.0", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "katex": "^0.16.22", "mermaid": "^11.7.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.3", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.2", "react-syntax-highlighter": "^15.6.1", "recharts": "^3.1.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwindcss-animate": "^1.0.7", "vite-plugin-remove-console": "^2.2.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/js": "^9.27.0", "@tailwindcss/typography": "^0.5.16", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.15", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.5.0", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "bippy": "^0.3.17", "concurrently": "^9.2.0", "cross-env": "^10.0.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "express": "^5.1.0", "globals": "^15.15.0", "http-proxy-middleware": "^3.0.5", "jsdom": "^26.1.0", "node-fetch": "^3.3.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "puppeteer": "^24.15.0", "serve": "^14.2.4", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-html": "^3.2.2", "vite-plugin-windicss": "^1.9.4"}}