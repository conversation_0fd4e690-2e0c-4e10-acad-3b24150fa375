<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaX AI - Your Research Inspiration Engine</title>
    <meta name="description" content="Stuck in research bottlenecks? NovaX AI generates insightful research ideas and implementation frameworks based on your research background. Explore now and ignite innovation!">
    <meta name="keywords" content="AI research tool, research inspiration engine, research innovation platform, scientific research assistant, academic research AI, research idea generator">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="NovaX AI - Your Research Inspiration Engine">
    <meta property="og:description" content="Stuck in research bottlenecks? NovaX AI generates insightful research ideas and implementation frameworks based on your research background. Explore now and ignite innovation!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://medxy.ai/novax">
    <meta property="og:image" content="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="NovaX AI">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="NovaX AI - Your Research Inspiration Engine">
    <meta name="twitter:description" content="Stuck in research bottlenecks? NovaX AI generates insightful research ideas and implementation frameworks based on your research background. Explore now and ignite innovation!">
    <meta name="twitter:image" content="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png">
    <meta name="twitter:site" content="@novaxai">
    <meta name="twitter:creator" content="@novaxai">

    <!-- Favicon -->
    <link rel="icon" href="https://www.medxy.ai/images/favicon.ico" sizes="any">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Geist+Mono:wght@100..900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- Highlight.js for code syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    
    <!-- Custom CSS Variables and Styles -->
    <style>
        :root {
            --background: oklch(0.9741 0 129.63);
            --foreground: oklch(0.2277 0.0034 67.65);
            --card: oklch(98.46% 0.002 247.84);
            --card-foreground: oklch(0.145 0 0);
            --popover: oklch(0.9924 0 0);
            --popover-foreground: oklch(0.145 0 0);
            --primary: oklch(0.205 0 0);
            --primary-foreground: oklch(0.985 0 0);
            --secondary: oklch(54.65% 0.246 262.87);
            --secondary-foreground: oklch(0.205 0 0);
            --muted: oklch(0.93 0 0);
            --muted-foreground: oklch(0.556 0 0);
            --accent: oklch(0.1149 0 0 / 6%);
            --accent-foreground: oklch(0.145 0 0);
            --destructive: oklch(0.577 0.245 27.325);
            --destructive-foreground: oklch(0.577 0.245 27.325);
            --border: oklch(0.1149 0 0 / 8%);
            --input: oklch(0.922 0 0);
            --ring: oklch(0.708 0 0);
            --radius: 0.625rem;
        }

        .dark {
            --background: oklch(0.185 0.005 285.823);
            --foreground: oklch(0.985 0 0);
            --card: oklch(0.2 0.005 285.823);
            --card-foreground: oklch(0.985 0 0);
            --popover: oklch(0.2267 0.0051 264.48);
            --popover-foreground: oklch(0.985 0 0);
            --primary: oklch(0.985 0 0);
            --primary-foreground: oklch(0.205 0 0);
            --secondary: oklch(54.65% 0.246 262.87);
            --secondary-foreground: oklch(0.985 0 0);
            --muted: oklch(0.31 0 0);
            --muted-foreground: oklch(0.708 0 0);
            --accent: oklch(0.274 0.006 286.033);
            --accent-foreground: oklch(98.46% 0.002 247.84);
            --destructive: oklch(0.396 0.141 25.723);
            --destructive-foreground: oklch(0.637 0.237 25.331);
            --border: oklch(0.9911 0 0 / 6%);
            --input: oklch(0.28 0 0);
            --ring: oklch(0.439 0 0);
        }

        * {
            border-color: var(--border);
        }

        body {
            background-color: var(--background);
            color: var(--foreground);
            font-family: 'Geist', ui-sans-serif, system-ui, sans-serif;
            font-feature-settings: 'palt';
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .bg-background { background-color: var(--background); }
        .text-foreground { color: var(--foreground); }
        .text-primary { color: var(--primary); }
        .text-secondary { color: var(--secondary); }
        .text-muted-foreground { color: var(--muted-foreground); }
        .bg-primary { background-color: var(--primary); }
        .bg-secondary { background-color: var(--secondary); }
        .bg-accent { background-color: var(--accent); }
        .border-border { border-color: var(--border); }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .animate-fade-in-delay {
            animation: fadeIn 0.8s ease-out 0.2s both;
        }

        /* Flickering Grid Effect */
        .flickering-grid {
            background-image: 
                radial-gradient(circle at 1px 1px, var(--secondary) 1px, transparent 0);
            background-size: 20px 20px;
            animation: flicker 3s ease-in-out infinite;
        }

        @keyframes flicker {
            0%, 100% { opacity: 0.1; }
            50% { opacity: 0.3; }
        }

        /* Gradient backgrounds */
        .bg-gradient-fade {
            background: linear-gradient(to right, 
                transparent, 
                transparent, 
                var(--background)
            );
        }

        /* Navbar Animation */
        #navbar-container {
            transition: width 0.3s cubic-bezier(0.25, 0.1, 0.25, 1),
                       max-width 0.3s cubic-bezier(0.25, 0.1, 0.25, 1),
                       padding 0.3s ease;
        }

        #navbar {
            transition: top 0.3s ease, margin 0.3s ease;
        }

        /* Chat Input Styles */
        .chat-input-card {
            transition: box-shadow 0.2s ease, border-color 0.2s ease;
            overflow: visible;
            position: relative;
        }

        .chat-input-card:focus-within {
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        }

        /* Ensure dropdowns appear above other content */
        .dropdown-menu {
            z-index: 1000;
        }

        /* Medical AI Case Replay Mobile Optimization */
        .medical-ai-case {
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .medical-ai-case {
                font-size: 12px;
            }
            .aspect-video{
                aspect-ratio: unset !important;
            }
            .medical-ai-case #chatContainer {
                padding: 0.5rem;
            }

            .medical-ai-case .message-content {
                font-size: 0.75rem;
                line-height: 1.4;
            }

            .medical-ai-case .message-content pre {
                font-size: 0.7rem;
                overflow-x: auto;
                white-space: pre-wrap;
                word-break: break-word;
            }

            .medical-ai-case .message-content code {
                font-size: 0.7rem;
                word-break: break-word;
            }

            /* Touch-friendly buttons */
            .medical-ai-case button {
                min-height: 44px;
                min-width: 44px;
                touch-action: manipulation;
            }

            /* Prevent zoom on double tap */
            .medical-ai-case {
                touch-action: manipulation;
            }
        }

        /* Selector button text truncation */
        .selector-button span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }

        /* Textarea auto-resize */
        textarea {
            transition: height 0.1s ease;
        }

        /* Submit button styles */
        .submit-button {
            transition: all 0.2s ease;
        }

        .submit-button:not(:disabled):hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Research Proposals Quick Selection Styles */
        .research-proposals-container {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Proposal Container Styles */
        .proposal-collapsed {
            transition: all 0.3s ease;
        }

        .proposal-expanded {
            transition: all 0.3s ease;
        }

        #expand-icon {
            transition: transform 0.3s ease;
        }

        .proposal-expanded.show #expand-icon {
            transform: rotate(180deg);
        }

        /* Proposal List Styles */
        .proposal-option {
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }

        .proposal-option:last-child {
            border-bottom: none;
        }

        .proposal-option.hidden {
            display: none;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        .proposal-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background: white;
            transition: all 0.2s ease;
            gap: 12px;
        }

        .proposal-option:hover .proposal-item {
            background: #f9fafb;
        }

        .proposal-text {
            font-size: 14px;
            color: #374151;
            line-height: 1.5;
            flex: 1;
        }

        .proposal-arrow {
            width: 16px;
            height: 16px;
            color: #d1d5db;
            flex-shrink: 0;
            transition: all 0.2s ease;
        }

        .proposal-option:hover .proposal-arrow {
            color: #9ca3af;
            transform: translateX(2px);
        }

        /* Category Tags Styles */
        .category-tags-container {
            margin-bottom: 16px;
        }

        .category-tag {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .category-tag:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .category-tag.active {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .category-tag svg {
            width: 16px;
            height: 16px;
        }

        /* Proposal List Container */
        .proposal-list-container {
            transition: all 0.3s ease;
        }

        .close-proposals {
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .close-proposals:hover {
            background: #f3f4f6;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .research-proposals-container {
                margin-top: 1rem;
                padding: 0 1rem;
            }

            .proposal-card {
                padding: 12px;
            }

            .proposal-text {
                font-size: 13px;
            }

            /* Mobile category tags optimization */
            .category-tags-container {
                margin-bottom: 12px;
            }

            .category-tags-container .flex {
                gap: 6px;
                justify-content: center;
            }

            .category-tag {
                padding: 6px 10px;
                font-size: 11px;
                border-radius: 14px;
                flex-shrink: 0;
                white-space: nowrap;
            }

            .category-tag svg {
                width: 12px;
                height: 12px;
                flex-shrink: 0;
            }

            /* Proposal list mobile optimization */
            .proposal-list-container {
                margin-top: 8px;
                border-radius: 8px;
            }

            .proposal-list-container .px-4 {
                padding-left: 12px;
                padding-right: 12px;
            }

            .proposal-list-container .py-3 {
                padding-top: 8px;
                padding-bottom: 8px;
            }

            /* Mobile proposal options */
            .proposal-option {
                padding: 8px 12px;
                margin: 0;
            }

            .proposal-text {
                font-size: 12px;
                line-height: 1.4;
            }

            .proposal-arrow {
                width: 12px;
                height: 12px;
            }

            /* Mobile header optimization */
            .proposal-list-container h3 {
                font-size: 14px;
            }

            .proposal-list-container svg {
                width: 16px;
                height: 16px;
            }
        }

        /* Extra small screens */
        @media (max-width: 480px) {
            .category-tags-container .flex {
                gap: 4px;
                overflow-x: auto;
                padding: 0 4px;
                -webkit-overflow-scrolling: touch;
            }

            .category-tag {
                padding: 5px 8px;
                font-size: 10px;
                gap: 3px;
                min-width: fit-content;
            }

            .category-tag svg {
                width: 10px;
                height: 10px;
            }

            /* Extra small proposal list */
            .proposal-list-container .px-4 {
                padding-left: 8px;
                padding-right: 8px;
            }

            .proposal-list-container .py-3 {
                padding-top: 6px;
                padding-bottom: 6px;
            }

            .proposal-option {
                padding: 6px 8px;
            }

            .proposal-text {
                font-size: 11px;
                line-height: 1.3;
            }

            .proposal-list-container h3 {
                font-size: 13px;
            }

            .proposal-list-container svg {
                width: 14px;
                height: 14px;
            }

            .proposal-arrow {
                width: 10px;
                height: 10px;
            }
        }

        /* Dropdown Styles */
        .dropdown-menu {
            animation: fadeInUp 0.15s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(4px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Selector button styles */
        .selector-button {
            transition: all 0.2s ease;
        }

        .selector-button:hover {
            background-color: var(--accent);
            border-color: var(--border);
        }

        /* Option hover effects */
        .dropdown-option {
            transition: background-color 0.15s ease;
        }

        .dropdown-option:hover {
            background-color: var(--accent);
        }

        /* FAQ Accordion Styles */
        .faq-item {
            transition: all 0.2s ease;
        }

        .faq-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .faq-trigger {
            transition: background-color 0.2s ease;
        }

        .faq-icon {
            transition: transform 0.3s ease;
        }

        .faq-content {
            transition: all 0.3s ease;
        }

        /* FAQ Mobile Responsive Improvements */
        @media (max-width: 640px) {
            .faq-item {
                border-radius: 0.5rem !important;
                margin-bottom: 0.5rem !important;
            }

            .faq-trigger {
                padding: 0.75rem 0.875rem !important;
            }

            .faq-content {
                padding: 0 0.875rem 0.875rem 0.875rem !important;
            }
        }

        /* About section animations */
        .about-feature-tag {
            transition: all 0.2s ease;
        }

        .about-feature-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Button styles */
        .btn-primary {
            background-color: var(--primary);
            color: var(--primary-foreground);
            padding: 0.75rem 1.5rem;
            border-radius: 9999px;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 120px;
            white-space: nowrap;
            text-align: center;
        }

        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--secondary);
            color: var(--secondary-foreground);
            padding: 0.5rem 1.5rem;
            border-radius: 9999px;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 120px;
            white-space: nowrap;
            text-align: center;
        }

        .btn-secondary:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        /* Modal Styles */
        .modal-overlay {
            backdrop-filter: blur(8px);
            animation: modalFadeIn 0.2s ease-out;
        }

        .modal-container {
            animation: modalSlideIn 0.3s ease-out;
            max-height: calc(100vh - 2rem);
        }

        .modal-overlay.closing {
            animation: modalFadeOut 0.2s ease-in;
        }

        .modal-container.closing {
            animation: modalSlideOut 0.2s ease-in;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalFadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalSlideOut {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.95) translateY(-10px);
            }
        }

        /* Form Styles */
        .modal-container input:focus,
        .modal-container textarea:focus,
        .modal-container select:focus {
            outline: none;
            box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
        }

        .modal-container input[type="checkbox"]:checked {
            background-color: hsl(var(--primary));
            border-color: hsl(var(--primary));
        }

        /* Scroll Padding for Anchor Links */
        html {
            scroll-padding-top: 140px;
        }

        /* Mobile scroll padding adjustment */
        @media (max-width: 768px) {
            html {
                scroll-padding-top: 100px;
            }
        }

        @media (max-width: 640px) {
            html {
                scroll-padding-top: 90px;
            }
        }

        @media (max-width: 480px) {
            html {
                scroll-padding-top: 80px;
            }
        }

        /* Section anchor positioning */
        section[id] {
            scroll-margin-top: 140px;
        }

        @media (max-width: 768px) {
            section[id] {
                scroll-margin-top: 100px;
            }
        }

        @media (max-width: 640px) {
            section[id] {
                scroll-margin-top: 90px;
            }
        }

        @media (max-width: 480px) {
            section[id] {
                scroll-margin-top: 80px;
            }
        }

        /* Mobile Menu Styles */
        #mobile-menu {
            animation: mobileMenuSlideDown 0.3s ease-out;
        }

        #mobile-menu.closing {
            animation: mobileMenuSlideUp 0.2s ease-in;
        }

        @keyframes mobileMenuSlideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes mobileMenuSlideUp {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        /* Mobile menu toggle button animation */
        #mobile-menu-toggle svg {
            transition: transform 0.2s ease;
        }

        #mobile-menu-toggle.active svg {
            transform: rotate(90deg);
        }

        /* Mobile-first responsive design */
        @media (max-width: 768px) {
            .modal-container {
                margin: 0.5rem;
                max-height: calc(100vh - 1rem);
                border-radius: 1rem;
            }

            /* Hero section mobile optimization */
            .hero-title {
                font-size: 1.875rem !important;
                line-height: 1.1 !important;
            }

            .hero-subtitle {
                font-size: 1rem !important;
            }

            /* Navbar container mobile optimization */
            #navbar-container {
                padding: 0.75rem !important;
            }

            .navbar-inner {
                padding: 0.75rem 1rem !important;
                height: auto !important;
                min-height: 3rem;
            }

            /* Mobile menu styles */
            #mobile-menu {
                margin-top: 0.5rem;
            }

            /* Use cases grid mobile optimization */
            .use-cases-grid {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }

            /* About section mobile optimization */
            .about-grid {
                grid-template-columns: 1fr !important;
                gap: 1.5rem !important;
            }

            /* About section text sizing */
            #about h2 {
                font-size: 1.5rem !important;
                line-height: 1.2 !important;
            }

            #about h3 {
                font-size: 1.125rem !important;
            }

            #about p {
                font-size: 0.875rem !important;
                line-height: 1.5 !important;
            }

            /* Core values mobile optimization */
            .bg-accent\/50 {
                padding: 1rem !important;
                border-radius: 0.75rem !important;
            }

            /* FAQ mobile optimization */
            .faq-item {
                margin-bottom: 0.75rem !important;
            }

            .faq-trigger {
                padding: 0.875rem 1rem !important;
                font-size: 0.9rem !important;
            }

            .faq-content {
                padding: 0 1rem 1rem 1rem !important;
                font-size: 0.85rem !important;
                line-height: 1.5 !important;
            }

            .faq-icon {
                width: 1rem !important;
                height: 1rem !important;
            }

            /* Chat input mobile optimization */
            .chat-input-card {
                padding: 0.75rem !important;
            }

            .chat-input-card textarea {
                min-height: 60px !important;
                max-height: 120px !important;
            }

            .chat-input-card .selector-button {
                width: 65px !important;
                min-width: 65px !important;
                padding: 0.375rem 0.5rem !important;
                font-size: 0.625rem !important;
                gap: 0.25rem !important;
                overflow: hidden !important;
                white-space: nowrap !important;
            }

            .chat-input-card .selector-button svg {
                width: 0.75rem !important;
                height: 0.75rem !important;
                flex-shrink: 0 !important;
            }

            .chat-input-card .selector-button span {
                font-size: 0.625rem !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
                flex: 1 !important;
                min-width: 0 !important;
            }

            /* Button mobile optimization */
            .btn-primary, .btn-secondary {
                padding: 0.625rem 1.25rem !important;
                font-size: 0.875rem !important;
            }

            /* Footer mobile optimization */
            .footer-grid {
                grid-template-columns: 1fr !important;
                gap: 2rem !important;
                text-align: center;
            }
        }

        @media (max-width: 640px) {
            /* Extra small screens */
            .hero-title {
                font-size: 1.75rem !important;
            }

            .modal-container {
                margin: 0.25rem;
                max-height: calc(100vh - 0.5rem);
            }

            /* Stack form fields on very small screens */
            .form-row {
                grid-template-columns: 1fr !important;
            }

            /* Adjust padding for small screens */
            .section-padding {
                padding: 2rem 1rem !important;
            }
        }

        @media (max-width: 480px) {
            /* Very small screens */
            .hero-title {
                font-size: 1.5rem !important;
            }

            .hero-subtitle {
                font-size: 0.9rem !important;
            }

            /* Ultra compact navigation */
            .nav-link {
                padding: 0.25rem 0.375rem !important;
                font-size: 0.65rem !important;
            }

            /* Smaller buttons */
            .btn-primary, .btn-secondary {
                padding: 0.375rem 0.75rem !important;
                font-size: 0.75rem !important;
            }

            /* Navbar adjustments */
            .navbar-inner {
                padding: 0.375rem 0.75rem !important;
                height: 2.5rem !important;
            }

            /* Logo adjustments */
            .navbar-inner img {
                height: 1rem !important;
            }

            /* FAQ ultra-compact for very small screens */
            .faq-trigger {
                padding: 0.75rem !important;
                font-size: 0.8rem !important;
            }

            .faq-content {
                padding: 0 0.75rem 0.75rem 0.75rem !important;
                font-size: 0.8rem !important;
            }

            .faq-icon {
                width: 0.875rem !important;
                height: 0.875rem !important;
            }

            /* FAQ section title */
            #faq h2 {
                font-size: 1.5rem !important;
            }

            #faq p {
                font-size: 0.8rem !important;
            }

            /* About section ultra-compact */
            #about h2 {
                font-size: 1.25rem !important;
            }

            #about h3 {
                font-size: 1rem !important;
            }

            #about p {
                font-size: 0.8rem !important;
            }

            /* Core values ultra-compact */
            .bg-accent\/50 {
                padding: 0.75rem !important;
            }

            /* Feature badges ultra-compact */
            .about-grid .flex.flex-wrap > div {
                padding: 0.375rem 0.5rem !important;
                font-size: 0.7rem !important;
            }

            /* GitHub button ultra-compact */
            #about a {
                padding: 0.5rem 1rem !important;
                font-size: 0.8rem !important;
            }
        }

        /* Landscape mobile optimization */
        @media (max-height: 500px) and (orientation: landscape) {
            .hero-title {
                font-size: 1.25rem !important;
            }

            .hero-subtitle {
                font-size: 0.85rem !important;
            }

            .section-padding {
                padding: 1rem !important;
            }
        }

        /* Filter Button Styles */
        .filter-btn {
            background: var(--accent);
            color: var(--muted-foreground);
            border: 1px solid var(--border);
        }

        .filter-btn:hover {
            background: var(--accent);
            color: var(--foreground);
        }

        .filter-btn.active {
            background: var(--primary);
            color: var(--primary-foreground);
            border-color: var(--primary);
        }

        /* iOS-style Period selector styles */
        .period-selector-container {
            background: #e5e7eb;
            border-radius: 50px;
            padding: 4px;
            display: flex;
            gap: 0;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .period-btn {
            background: transparent;
            border: none;
            padding: 8px 20px;
            border-radius: 46px;
            font-weight: 500;
            font-size: 14px;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 100px;
            justify-content: center;
        }

        .period-btn:hover {
            color: #374151;
        }

        .period-btn.active {
            background: white;
            color: #111827;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            font-weight: 600;
        }

        .discount-badge {
            background: #10b981;
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Case Card Styles - Matching original frontend exactly */
        .case-card {
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
            /* Original frontend shadow - exact match */
            box-shadow: 0px 61px 24px -10px rgba(0,0,0,0.01), 0px 34px 20px -8px rgba(0,0,0,0.05), 0px 15px 15px -6px rgba(0,0,0,0.09), 0px 4px 8px -2px rgba(0,0,0,0.10), 0px 0px 0px 1px rgba(0,0,0,0.08);
        }

        .case-card:hover {
            /* Enhanced hover effect matching original */
            transform: translateY(-4px);
            box-shadow: 0px 80px 32px -12px rgba(0,0,0,0.02), 0px 45px 28px -10px rgba(0,0,0,0.08), 0px 20px 20px -8px rgba(0,0,0,0.12), 0px 6px 12px -3px rgba(0,0,0,0.15), 0px 0px 0px 1px rgba(0,0,0,0.10);
        }

        .case-card.hidden {
            display: none;
        }

        /* Case small icon styles - mimicking the style in the image */
        .case-icon-small {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
        }

        .case-icon-content {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .case-icon-content:hover {
            transform: scale(1.1);
        }

        .case-icon-symbol {
            font-size: 12px;
            line-height: 1;
        }

        @keyframes fadeInIcon {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Remove fallback icon styles - now using unified circular icons */

        /* Remove loading state styles - circular icons don't need loading */

        /* Preview image hover effect */
        .case-preview-overlay {
            position: absolute;
            inset: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: flex-end;
            justify-content: flex-start;
            padding: 1rem;
        }

        .case-card:hover .case-preview-overlay {
            opacity: 1;
        }

        .case-preview-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
        }

        .case-preview-arrow {
            width: 1rem;
            height: 1rem;
            transition: transform 0.3s ease;
        }

        .case-preview-link:hover .case-preview-arrow {
            transform: translateX(0.25rem);
        }

        /* Text clamp utilities */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Dark mode adjustments for case cards */
        @media (prefers-color-scheme: dark) {
            .case-card {
                box-shadow: 0px 61px 24px -10px rgba(255,255,255,0.01), 0px 34px 20px -8px rgba(255,255,255,0.05), 0px 15px 15px -6px rgba(255,255,255,0.09), 0px 4px 8px -2px rgba(255,255,255,0.10), 0px 0px 0px 1px rgba(255,255,255,0.08);
            }

            .case-card:hover {
                box-shadow: 0px 80px 32px -12px rgba(255,255,255,0.02), 0px 45px 28px -10px rgba(255,255,255,0.08), 0px 20px 20px -8px rgba(255,255,255,0.12), 0px 6px 12px -3px rgba(255,255,255,0.15), 0px 0px 0px 1px rgba(255,255,255,0.10);
            }
        }

        /* Animation for case cards */
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Stagger animation delay for cards */
        .case-card:nth-child(1) { animation-delay: 0.1s; }
        .case-card:nth-child(2) { animation-delay: 0.2s; }
        .case-card:nth-child(3) { animation-delay: 0.3s; }
        .case-card:nth-child(4) { animation-delay: 0.4s; }
        .case-card:nth-child(5) { animation-delay: 0.5s; }
        .case-card:nth-child(6) { animation-delay: 0.6s; }

        /* Loading state */
        .cases-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border);
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Markdown content styling in messages */
        .message-content h1 { font-size: 1.25rem; font-weight: 700; margin: 0.75rem 0 0.5rem 0; }
        .message-content h2 { font-size: 1.125rem; font-weight: 600; margin: 0.5rem 0 0.375rem 0; }
        .message-content h3 { font-size: 1rem; font-weight: 600; margin: 0.375rem 0 0.25rem 0; }
        .message-content p { margin: 0.5rem 0; line-height: 1.5; }
        .message-content ul, .message-content ol { margin: 0.5rem 0; padding-left: 1.25rem; }
        .message-content li { margin: 0.125rem 0; }
        .message-content strong { font-weight: 600; }
        .message-content em { font-style: italic; }
        .message-content code { background: rgba(0,0,0,0.1); padding: 0.125rem 0.25rem; border-radius: 0.25rem; font-size: 0.875rem; font-family: monospace; }
        .message-content pre { background: rgba(0,0,0,0.05); padding: 0.75rem; border-radius: 0.375rem; overflow-x: auto; margin: 0.5rem 0; }
        .message-content pre code { background: none; padding: 0; }
        .message-content blockquote { border-left: 3px solid rgba(0,0,0,0.2); padding-left: 0.75rem; margin: 0.5rem 0; font-style: italic; opacity: 0.8; }
        .message-content a { color: #3b82f6; text-decoration: underline; }
        .message-content a:hover { color: #1d4ed8; }

        /* Special styling for AI messages with white background */
        .bg-white .message-content code { background: #f3f4f6; }
        .bg-white .message-content pre { background: #f8f9fa; }
        .bg-white .message-content blockquote { border-left-color: #e5e7eb; color: #6b7280; }

        /* Loading Overlay Styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        .loading-text {
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .loading-progress {
            color: #9ca3af;
            font-size: 0.75rem;
            text-align: center;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dark mode loading styles */
        .dark .loading-overlay {
            background: rgba(17, 24, 39, 0.95);
        }

        .dark .loading-text {
            color: #d1d5db;
        }

        .dark .loading-progress {
            color: #6b7280;
        }
    </style>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        background: 'var(--background)',
                        foreground: 'var(--foreground)',
                        primary: 'var(--primary)',
                        'primary-foreground': 'var(--primary-foreground)',
                        secondary: 'var(--secondary)',
                        'secondary-foreground': 'var(--secondary-foreground)',
                        muted: 'var(--muted)',
                        'muted-foreground': 'var(--muted-foreground)',
                        accent: 'var(--accent)',
                        'accent-foreground': 'var(--accent-foreground)',
                        border: 'var(--border)',
                        input: 'var(--input)',
                        ring: 'var(--ring)',
                    },
                    fontFamily: {
                        sans: ['Geist', 'ui-sans-serif', 'system-ui', 'sans-serif'],
                        mono: ['Geist Mono', 'ui-monospace', 'monospace'],
                    },
                    borderRadius: {
                        lg: 'var(--radius)',
                        md: 'calc(var(--radius) - 2px)',
                        sm: 'calc(var(--radius) - 4px)',
                    },
                }
            }
        }
    </script>
</head>
<body class="antialiased font-sans bg-background text-foreground">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Initializing NovaX...</div>
        <div id="loading-progress" class="loading-progress">Loading components...</div>
    </div>

    <!-- Navigation -->
    <header id="navbar" class="sticky top-4 z-50 mx-4 md:mx-0 flex justify-center transition-all duration-300">
        <div id="navbar-container" class="mx-auto max-w-7xl rounded-2xl px-4 lg:px-7 transition-all duration-300 xl:px-0" style="width: 70rem; max-width: 100%;">
            <div class="navbar-inner flex h-10 sm:h-12 lg:h-14 items-center justify-between p-1.5 sm:p-2 lg:p-4 bg-background/90 backdrop-blur-lg rounded-xl sm:rounded-2xl">
                <a href="https://medxy.ai/novax" target="_blank" class="flex items-center gap-1 sm:gap-2 lg:gap-3 flex-shrink-0">
                    <img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" alt="Novax" width="140" height="22" class="h-4 sm:h-5 lg:h-6 w-auto">
                    <span class="hidden sm:inline font-medium">Novax</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-1 lg:space-x-2 xl:space-x-3">
                    <a href="#hero" class="nav-link px-4 py-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors rounded-lg whitespace-nowrap tracking-tight" data-section="hero">Home</a>
                    <a href="#use-cases" class="nav-link px-4 py-2 text-sm font-medium text-primary/60 hover:text-primary transition-colors rounded-lg whitespace-nowrap tracking-tight" data-section="use-cases">Use Cases</a>
                    <a href="#pricing" class="nav-link px-4 py-2 text-sm font-medium text-primary/60 hover:text-primary transition-colors rounded-lg whitespace-nowrap tracking-tight" data-section="pricing">Pricing</a>
                    <a href="#about" class="nav-link px-4 py-2 text-sm font-medium text-primary/60 hover:text-primary transition-colors rounded-lg whitespace-nowrap tracking-tight" data-section="about">About</a>
                    <a href="#faq" class="nav-link px-4 py-2 text-sm font-medium text-primary/60 hover:text-primary transition-colors rounded-lg whitespace-nowrap tracking-tight" data-section="faq">FAQ</a>
                </nav>

                <div class="flex items-center gap-1 sm:gap-2 lg:gap-3">
                    <!-- Desktop buttons -->
                    <div class="hidden md:flex items-center gap-2 lg:gap-3">
                        <div id="get-started-btn" class="btn-secondary cursor-pointer !text-[#fff] text-sm whitespace-nowrap">Get started</div>
                        <button id="theme-toggle" class="p-1.5 lg:p-2 rounded-md hover:bg-accent transition-colors flex-shrink-0">
                            <svg class="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile buttons -->
                    <div class="flex md:hidden items-center gap-1">
                        <button id="theme-toggle-mobile" class="p-2 rounded-md hover:bg-accent transition-colors flex-shrink-0">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </button>
                        <button id="mobile-menu-toggle" class="p-2 rounded-md hover:bg-accent transition-colors flex-shrink-0">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu Dropdown -->
            <div id="mobile-menu" class="md:hidden hidden bg-background/95 backdrop-blur-lg border-t border-border mt-2 rounded-xl overflow-hidden">
                <nav class="flex flex-col text-sm mb-4 border border-border rounded-md">
                    <a href="#hero" class="mobile-nav-link p-2.5 border-b border-border last:border-b-0 text-sm font-medium text-primary hover:text-primary/80 transition-colors" data-section="hero">Home</a>
                    <a href="#use-cases" class="mobile-nav-link p-2.5 border-b border-border last:border-b-0 text-sm font-medium text-muted-foreground hover:text-primary transition-colors" data-section="use-cases">Use Cases</a>
                    <a href="#pricing" class="mobile-nav-link p-2.5 border-b border-border last:border-b-0 text-sm font-medium text-muted-foreground hover:text-primary transition-colors" data-section="pricing">Pricing</a>
                    <a href="#about" class="mobile-nav-link p-2.5 border-b border-border last:border-b-0 text-sm font-medium text-muted-foreground hover:text-primary transition-colors" data-section="about">About</a>
                    <a href="#faq" class="mobile-nav-link p-2.5 border-b border-border last:border-b-0 text-sm font-medium text-muted-foreground hover:text-primary transition-colors" data-section="faq">FAQ</a>
                    <div class="border-t border-border pt-3 mt-3">
                        <a href="/auth" class="block w-full text-center bg-primary text-primary-foreground px-4 py-3 rounded-lg font-medium hover:opacity-90 transition-opacity">Get started</a>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section - Moved to top of page -->
    <section id="hero" class="w-full relative overflow-hidden pt-20 sm:pt-24 md:pt-28">
        <div class="relative flex flex-col items-center w-full px-6">
            <!-- Background Effects -->
            <div class="absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"></div>
                <div class="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"></div>
                <div class="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"></div>
                <div class="flickering-grid h-full w-full"></div>
            </div>

            <div class="absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"></div>
                <div class="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"></div>
                <div class="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"></div>
                <div class="flickering-grid h-full w-full"></div>
            </div>

            <!-- Hero Content -->
            <div class="relative z-10 flex flex-col items-center justify-center gap-8  sm:py-20  w-full max-w-6xl mx-auto">
                <div class="flex flex-col items-center justify-center gap-5  animate-fade-in">
                    <h1 class="hero-title text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center">
                        <span >Ready to Find Your Next Breakthrough?</span>
                    </h1>
                    <p class="hero-subtitle text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight">
                        Tell me your research field and objective,Let's explore the possibilities together.
                    </p>
                </div>

                <!-- Chat Interface -->
                <div class="flex items-center w-full max-w-4xl gap-2 flex-wrap justify-center animate-fade-in-delay">
                    <div class="w-full relative">
                        <div class="relative z-10">
                            <!-- Chat Input Card -->
                            <div class="w-full text-sm flex flex-col justify-between items-start rounded-lg">

                                <div class="chat-input-card w-full p-3 bg-background border border-border rounded-3xl shadow-lg">
                                    <!-- Message Input Area -->
                                    <div class="relative flex flex-col w-full gap-3">
                                        <!-- Textarea Container -->
                                        <div class="flex flex-col">
                                            <textarea
                                                placeholder="Enter your research challenge, area of interest, or a brief of your current work here...
NovaX will design a research plan tailored to your preliminary studies in 10 minutes."
                                                class="w-full bg-transparent border-none shadow-none focus:ring-0 focus:outline-none text-[15px] min-h-[80px] max-h-[180px] overflow-y-auto resize-none placeholder:text-muted-foreground"
                                                rows="3"
                                                style="field-sizing: content;"
                                            ></textarea>
                                        </div>

                                        <!-- Bottom Controls -->
                                        <div class="flex items-center justify-between border-t border-border/50 pt-3 gap-2">
                                            <div class="flex items-center gap-2">
                                                <!-- File Upload Button -->
                                                <button id="file-upload-btn" class="w-8 h-8 flex-shrink-0 flex items-center justify-center text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-accent">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                    </svg>
                                                </button>
                                            </div>

                                            <div class="flex items-center gap-1.5 flex-wrap">
                                                <!-- Model Selector -->
                                                <div class="relative">
                                                    <button id="model-selector" class="selector-button flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-muted-foreground hover:text-foreground bg-accent/50 hover:bg-accent border border-border rounded-lg transition-all min-w-[100px] whitespace-nowrap">
                                                         <svg class="w-3.5 h-3.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                        <span id="selected-model" class="flex-1 text-center">novax-base</span>
                                                        <svg class="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </button>
                                                    <!-- Model Dropdown -->
                                                    <div id="model-dropdown" class="dropdown-menu absolute bottom-full right-0 mb-2 w-48 bg-background border border-border rounded-lg shadow-lg z-50 hidden">
                                                        <div class="p-1">
                                                            <button class="model-option w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-md transition-colors" data-model="claude-3.5-sonnet">
                                                                <div class="font-medium">novax-base</div>
                                                            </button>
                                                            <button class="model-option w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-md transition-colors" data-model="gpt-4">
                                                                <div class="font-medium">novax-pro</div>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Prompt Template Selector -->
                                                <div class="relative">
                                                    <button id="prompt-selector" class="selector-button flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-muted-foreground hover:text-foreground bg-accent/50 hover:bg-accent border border-border rounded-lg transition-all min-w-[80px] whitespace-nowrap">
                                                        <svg class="w-3.5 h-3.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                        <span id="selected-prompt" class="flex-1 text-center">General</span>
                                                        <svg class="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </button>
                                                    <!-- Prompt Dropdown -->
                                                    <div id="prompt-dropdown" class="dropdown-menu absolute bottom-full right-0 mb-2 w-52 bg-background border border-border rounded-lg shadow-lg z-50 hidden">
                                                        <div class="p-1">
                                                            <button class="prompt-option w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-md transition-colors" data-prompt="general">
                                                                <div class="font-medium">General Assistant</div>
                                                                <div class="text-xs text-muted-foreground">General purpose help</div>
                                                            </button>
                                                            <button class="prompt-option w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-md transition-colors" data-prompt="coding">
                                                                <div class="font-medium">Code Assistant</div>
                                                                <div class="text-xs text-muted-foreground">Programming and development</div>
                                                            </button>
                                                            <button class="prompt-option w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-md transition-colors" data-prompt="research">
                                                                <div class="font-medium">Research Assistant</div>
                                                                <div class="text-xs text-muted-foreground">Information gathering and analysis</div>
                                                            </button>
                                                            <button class="prompt-option w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-md transition-colors" data-prompt="writing">
                                                                <div class="font-medium">Writing Assistant</div>
                                                                <div class="text-xs text-muted-foreground">Content creation and editing</div>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Submit Button -->
                                                <button class="submit-button w-8 h-8 flex-shrink-0 rounded-xl bg-primary text-primary-foreground hover:opacity-90 transition-all flex items-center justify-center disabled:opacity-50" disabled>
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Research Proposal Quick Selection Area -->
                        <div class="research-proposals-container w-full mt-6 max-w-4xl mx-auto">
                            <!-- Category Tags (Always Visible) -->
                            <div class="category-tags-container mb-4">
                                <div class="flex flex-wrap gap-2 justify-center">
                                    <button class="category-tag" data-category="general">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v4"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4"></path>
                                        </svg>
                                        General Use
                                    </button>
                                    <button class="category-tag" data-category="design">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                        </svg>
                                        Research Design
                                    </button>
                                    <button class="category-tag" data-category="planning">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                        </svg>
                                        Research Planning
                                    </button>
                                    <button class="category-tag" data-category="modeling">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                        Data Modeling
                                    </button>
                                </div>
                            </div>

                            <!-- Proposal List (Hidden by default) -->
                            <div class="proposal-list-container bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hidden">
                                <!-- Header -->
                                <div class="flex items-center justify-between px-4 py-3 bg-gray-50 border-b border-gray-200">
                                    <div class="flex items-center gap-2">
                                        <svg class="w-5 h-5 text-gray-600" id="current-category-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <!-- Default icon - will be updated dynamically -->
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v4"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4"></path>
                                        </svg>
                                        <h3 class="text-base font-medium text-gray-900" id="current-category-title">General Use</h3>
                                    </div>
                                    <button class="close-proposals text-gray-400 hover:text-gray-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>

                                <!-- Proposal Options List -->
                                <div class="proposal-list">
                                    <!-- General Scenarios Category (default visible) -->
                                    <div class="proposal-option category-general" data-text="I need a prospective study design for cancer research">
                                        <div class="proposal-item">
                                            <span class="proposal-text">I need a prospective study design for cancer research</span>
                                            <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-general" data-text="Design a clinical research protocol for early screening of Alzheimer's disease in elderly patients">
                                        <div class="proposal-item">
                                            <span class="proposal-text">Design a clinical research protocol for early screening of Alzheimer's disease in elderly patients</span>
                                            <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-general" data-text="Design a mindfulness therapy intervention protocol for depression patients">
                                        <div class="proposal-item">
                                            <span class="proposal-text">Design a mindfulness therapy intervention protocol for depression patients</span>
                                            <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <!-- Research Design Category (hidden by default) -->
                                    <div class="proposal-option category-design hidden" data-text="Design a clinical research study on the relationship between digestive system and Alzheimer's disease">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Design a clinical research study on the relationship between digestive system and Alzheimer's disease</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-design hidden" data-text="Clinical research on how high-altitude hypoxic environment affects glaucoma onset">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Clinical research on how high-altitude hypoxic environment affects glaucoma onset</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-design hidden" data-text="Design CRISPR gene editing protocol for cancer cells">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Design CRISPR gene editing protocol for cancer cells</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-design hidden" data-text="Design clinical research to validate circRNA as early diagnostic biomarker for lung cancer">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Design clinical research to validate circRNA as early diagnostic biomarker for lung cancer</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-design hidden" data-text="Develop CRISPR-Cas9 based gene editing therapy protocol for hemophilia treatment">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Develop CRISPR-Cas9 based gene editing therapy protocol for hemophilia treatment</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-design hidden" data-text="I need a prospective study design for cancer research">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">I need a prospective study design for cancer research</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Research Planning Category (hidden by default) -->
                                    <div class="proposal-option category-planning hidden" data-text="Mechanistic study of miR-27a regulating diabetic atherosclerotic vascular calcification through exosome-mediated macrophage-endothelial cell crosstalk">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Mechanistic study of miR-27a regulating diabetic atherosclerotic vascular calcification through exosome-mediated macrophage-endothelial cell crosstalk</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-planning hidden" data-text="How to plan the research pathway from drug development to clinical trials for novel anticancer drugs?">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">How to plan the research pathway from drug development to clinical trials for novel anticancer drugs?</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-planning hidden" data-text="Research on glaucoma and high altitude hypoxia">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Research on glaucoma and high altitude hypoxia</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-planning hidden" data-text="Observational clinical study on Alzheimer's disease and GLP-1RA">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Observational clinical study on Alzheimer's disease and GLP-1RA</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-planning hidden" data-text="Explore the regulatory role of long non-coding RNA in myocardial infarction">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Explore the regulatory role of long non-coding RNA in myocardial infarction</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-planning hidden" data-text="Identify research gaps in immune checkpoint inhibitor resistance mechanisms">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Identify research gaps in immune checkpoint inhibitor resistance mechanisms</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Data Modeling Category (hidden by default) -->
                                    <div class="proposal-option category-modeling hidden" data-text="Investigate dynamic risk factors for acute kidney injury (AKI) in ARDS patients receiving ECMO treatment and build predictive models">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Investigate dynamic risk factors for acute kidney injury (AKI) in ARDS patients receiving ECMO treatment and build predictive models</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-modeling hidden" data-text="Establish cancer multi-omics association networks">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Establish cancer multi-omics association networks</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-modeling hidden" data-text="Use network pharmacology methods to predict action targets of traditional Chinese medicine formulas">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Use network pharmacology methods to predict action targets of traditional Chinese medicine formulas</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="proposal-option category-modeling hidden" data-text="Use metabolomics data to identify disease subtype biomarkers">
                                        <div class="proposal-card">
                                            <div class="proposal-item">
                                                <span class="proposal-text">Use metabolomics data to identify disease subtype biomarkers</span>
                                                <svg class="proposal-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="absolute -bottom-4 inset-x-0 h-6 bg-secondary/20 blur-xl rounded-full -z-10 opacity-70"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="w-full">
        <div class="w-full divide-y divide-border">


            <!-- Use Cases Section -->
            <section id="use-cases" class="flex flex-col items-center justify-center gap-10 pb-10 w-full relative">
                <div class="text-center space-y-4 pt-16">
                    <h2 class="text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
                        See Novax in action
                    </h2>
                    <p class="text-muted-foreground text-center text-balance font-medium">
                        Explore real-world examples of how Novax completes complex tasks autonomously
                    </p>
                </div>

                <!-- Filter Controls -->
                <div class="flex flex-col items-center gap-4 w-full max-w-2xl">
                    <!-- Search Bar -->
                    <div class="relative w-full max-w-md">
                        <input
                            type="text"
                            id="case-search"
                            placeholder="Search cases..."
                            class="w-full px-4 py-2 pl-10 text-sm border border-border rounded-lg bg-background outline-none transition-all"
                        >
                        <svg class="absolute left-3 top-2.5 w-4 h-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>          
                    <!-- Filter Buttons -->
                    <div class="flex items-center justify-center gap-2 flex-wrap">
                        <button class="filter-btn active px-4 py-2 text-sm font-medium rounded-lg transition-all" data-filter="all">
                            all
                        </button>
                        <button class="filter-btn px-4 py-2 text-sm font-medium rounded-lg transition-all" data-filter="novax-base">
                            novax-base
                        </button>
                        <button class="filter-btn px-4 py-2 text-sm font-medium rounded-lg transition-all" data-filter="novax-pro">
                            novax-pro
                        </button>
                    </div>
                </div>

                <!-- Static Cases Container -->
                <div id="cases-container" class="use-cases-grid grid min-[650px]:grid-cols-2 min-[900px]:grid-cols-3 min-[1200px]:grid-cols-4 gap-4 w-full max-w-6xl mx-auto px-6">

                    <!-- novax-base Cases -->
                    <div class="case-card rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col bg-accent" data-category="novax-base">
                        <div class="flex flex-col gap-4 p-4">
                            <div class="flex items-center gap-3">
                                <div class="case-icon-small">
                                    <div class="case-icon-content" style="background-color: #3b82f6;">
                                        <span class="case-icon-symbol">📊</span>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium line-clamp-1">Simple input with follow-up questions</h3>
                            </div>
                            <p class="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                                A simple initial input combined with iterative follow-up questions enables refined user needs modeling and facilitates intelligent, personalized research planning and database-driven insights.
                            </p>
                        </div>
                        <div class="mt-auto">
                            <hr class="border-border dark:border-white/20 m-0">
                            <div class="w-full h-[160px] bg-accent/10">
                                <div class="relative w-full h-full overflow-hidden">
                                    <img
                                        src="https://img.medsci.cn/Random/doctor-signing-contract-V6EFLDM.jpg"
                                        alt="Simple input with follow-up questions"
                                        class="w-full h-full object-cover"
                                        onerror="this.src='https://placehold.co/800x400/f5f5f5/666666?text=Simple+input+with+follow-up+questions'"
                                    >
                                    <div class="case-preview-overlay">
                                        <a href="https://medxy.ai/en/cases/3b4c5d6e7f8a" class="case-preview-link group">
                                            <span>Watch replay</span>
                                            <svg class="case-preview-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="case-card rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col bg-accent" data-category="novax-base">
                        <div class="flex flex-col gap-4 p-4">
                            <div class="flex items-center gap-3">
                                <div class="case-icon-small">
                                    <div class="case-icon-content" style="background-color: #8b5cf6;">
                                        <span class="case-icon-symbol">📚</span>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium line-clamp-1">Research following published literature</h3>
                            </div>
                            <p class="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                                Builds upon established knowledge by systematically analyzing and extending insights from published literature to guide robust, evidence-based research development.
                            </p>
                        </div>
                        <div class="mt-auto">
                            <hr class="border-border dark:border-white/20 m-0">
                            <div class="w-full h-[160px] bg-accent/10">
                                <div class="relative w-full h-full overflow-hidden">
                                    <img
                                        src="https://img.medsci.cn/20221109/1668024406447_4754896.jpeg"
                                        alt="Research following published literature"
                                        class="w-full h-full object-cover"
                                        onerror="this.src='https://placehold.co/800x400/f5f5f5/666666?text=Research+following+published+literature'"
                                    >
                                    <div class="case-preview-overlay">
                                        <a href="https://medxy.ai/en/cases/8tbfdemwy3" class="case-preview-link group">
                                            <span>Watch replay</span>
                                            <svg class="case-preview-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="case-card rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col bg-accent" data-category="novax-base">
                        <div class="flex flex-col gap-4 p-4">
                            <div class="flex items-center gap-3">
                                <div class="case-icon-small">
                                    <div class="case-icon-content" style="background-color: #10b981;">
                                        <span class="case-icon-symbol">🏥</span>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium line-clamp-1">Clinical study with follow-up questions</h3>
                            </div>
                            <p class="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                                Initiates a clinical study framework with adaptive follow-up questions to dynamically refine patient profiling, improve data accuracy, and support personalized medical research.
                            </p>
                        </div>
                        <div class="mt-auto">
                            <hr class="border-border dark:border-white/20 m-0">
                            <div class="w-full h-[160px] bg-accent/10">
                                <div class="relative w-full h-full overflow-hidden">
                                    <img
                                        src="https://img.medsci.cn/Random/health-insurance-PK235DP.jpg"
                                        alt="Clinical study with follow-up questions"
                                        class="w-full h-full object-cover"
                                        onerror="this.src='https://placehold.co/800x400/f5f5f5/666666?text=Clinical+study+with+follow-up+questions'"
                                    >
                                    <div class="case-preview-overlay">
                                        <a href="https://medxy.ai/en/cases/su37k8obxp" class="case-preview-link group">
                                            <span>Watch replay</span>
                                            <svg class="case-preview-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- novax-pro Cases -->
                    <div class="case-card rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col bg-accent" data-category="novax-pro">
                        <div class="flex flex-col gap-4 p-4">
                            <div class="flex items-center gap-3">
                                <div class="case-icon-small">
                                    <div class="case-icon-content" style="background-color: #3b82f6;">
                                        <span class="case-icon-symbol">📝</span>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium line-clamp-1">Based on brief information</h3>
                            </div>
                            <p class="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                                Based on brief information, a concise research plan is formulated to guide subsequent investigation and innovation.
                            </p>
                        </div>
                        <div class="mt-auto">
                            <hr class="border-border dark:border-white/20 m-0">
                            <div class="w-full h-[160px] bg-accent/10">
                                <div class="relative w-full h-full overflow-hidden">
                                    <img
                                        src="https://img.medsci.cn/20241108/1731046030949_8538692.jpg"
                                        alt="Based on brief information"
                                        class="w-full h-full object-cover"
                                        onerror="this.src='https://placehold.co/800x400/f5f5f5/666666?text=Based+on+brief+information'"
                                    >
                                    <div class="case-preview-overlay">
                                        <a href="https://medxy.ai/en/cases/9a4fe1978" class="case-preview-link group">
                                            <span>Watch replay</span>
                                            <svg class="case-preview-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="case-card rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col bg-accent" data-category="novax-pro">
                        <div class="flex flex-col gap-4 p-4">
                            <div class="flex items-center gap-3">
                                <div class="case-icon-small">
                                    <div class="case-icon-content" style="background-color: #8b5cf6;">
                                        <span class="case-icon-symbol">🔄</span>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium line-clamp-1">Research method transfer to user's study area</h3>
                            </div>
                            <p class="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                                Transferring proven research methods to the user's study area to enhance scientific rigor and accelerate discovery.
                            </p>
                        </div>
                        <div class="mt-auto">
                            <hr class="border-border dark:border-white/20 m-0">
                            <div class="w-full h-[160px] bg-accent/10">
                                <div class="relative w-full h-full overflow-hidden">
                                    <img
                                        src="https://img.medsci.cn/20240509/1715223995977_5579292.jpg"
                                        alt="Research method transfer to user's study area"
                                        class="w-full h-full object-cover"
                                        onerror="this.src='https://placehold.co/800x400/f5f5f5/666666?text=Research+method+transfer+to+users+study+area'"
                                    >
                                    <div class="case-preview-overlay">
                                        <a href="https://medxy.ai/en/cases/e6d7e1720" class="case-preview-link group">
                                            <span>Watch replay</span>
                                            <svg class="case-preview-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="case-card rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col bg-accent" data-category="novax-pro">
                        <div class="flex flex-col gap-4 p-4">
                            <div class="flex items-center gap-3">
                                <div class="case-icon-small">
                                    <div class="case-icon-content" style="background-color: #10b981;">
                                        <span class="case-icon-symbol">📊</span>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium line-clamp-1">Based on investigation report</h3>
                            </div>
                            <p class="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                                Based on the investigation report, a strategic research plan is developed to address key findings and guide future studies.
                            </p>
                        </div>
                        <div class="mt-auto">
                            <hr class="border-border dark:border-white/20 m-0">
                            <div class="w-full h-[160px] bg-accent/10">
                                <div class="relative w-full h-full overflow-hidden">
                                    <img
                                        src="https://img.medsci.cn/Random/doctor-signing-contract-V6EFLDM.jpg"
                                        alt="Based on investigation report"
                                        class="w-full h-full object-cover"
                                        onerror="this.src='https://placehold.co/800x400/f5f5f5/666666?text=Based+on+investigation+report'"
                                    >
                                    <div class="case-preview-overlay">
                                        <a href="https://medxy.ai/en/cases/8ce0e14eb" class="case-preview-link group">
                                            <span>Watch replay</span>
                                            <svg class="case-preview-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>



            </section>

            <!-- Pricing Section -->
            <section id="pricing" class="flex flex-col items-center justify-center gap-10 py-16 w-full relative">
                <div class="text-center space-y-4">
                    <h2 class="text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
                        Simple, transparent pricing
                    </h2>
                    <p class="text-muted-foreground text-center text-balance font-medium max-w-2xl mx-auto">
                        Choose the plan that fits your needs
                    </p>
                </div>
                <!-- Subscription Period Selector -->
                <div class="flex items-center justify-center mb-6">
                    <div class="period-selector-container bg-gray-200 rounded-full p-1 flex">
                        <button class="period-btn active" data-period="monthly">
                            Monthly
                        </button>
                        <button class="period-btn" data-period="quarterly">
                            <span>Quarterly</span>
                            <!-- <span class="discount-badge">Save 33%</span> -->
                        </button>
                        <button class="period-btn" data-period="yearly">
                            <span>Yearly</span>
                            <!-- <span class="discount-badge">Save 50%</span> -->
                        </button>
                    </div>
                </div>
                <div class="grid md:grid-cols-3 gap-6 w-full max-w-5xl mx-auto px-6">
                    <!-- Base Free Plan (always visible) -->
                    <div class="pricing-card base-free-card rounded-xl border border-border p-6 bg-background relative flex flex-col h-full" data-app="base" data-plan="free">
                        <div class="flex-1 space-y-4">
                            <div>
                                <h3 class="text-xl font-semibold">Base Free</h3>
                                <p class="text-sm text-muted-foreground">Perfect for getting started</p>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-baseline gap-1">
                                    <span class="text-3xl font-bold">$0</span>
                                    <span class="text-sm text-muted-foreground">/month</span>
                                </div>
                                <div class="h-4"></div>
                            </div>
                            <ul class="space-y-3 text-sm">
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Single research direction design
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Initial innovative idea generation
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Knowledge base
                                </li>
                            </ul>
                        </div>
                        <div class="mt-6">
                            <button class="w-full btn-secondary !text-[#ffffff] whitespace-nowrap subscribe-btn"
                                    data-app="base"
                                    data-plan-key="free"
                                    data-plan-type="Free"
                                    data-price="0.00"
                                    data-period="MONTH"
                                    data-months="1"
                                    data-price-id="free_subscription">
                                Start Free
                            </button>
                        </div>
                    </div>



                    <!-- Pro Monthly -->
                    <div class="pricing-card pro-monthly-card period-card rounded-xl border-2 border-primary p-6 bg-background relative flex flex-col h-full" data-app="pro" data-plan="monthly" data-period="monthly">
                        <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                            <span class="bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium">
                                Most Popular
                            </span>
                        </div>
                        <div class="flex-1 space-y-4">
                            <div>
                                <h3 class="text-xl font-semibold">Pro Monthly</h3>
                                <p class="text-sm text-muted-foreground">Advanced features for professionals</p>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-baseline gap-1">
                                    <span class="text-3xl font-bold">$19</span>
                                    <span class="text-sm text-muted-foreground">/month</span>
                                </div>
                                <div class="h-4"></div>
                            </div>
                            <ul class="space-y-3 text-sm">
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    In-depth proposal evaluation
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Personalized research plan design
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Research implementation planning
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Intelligent target journal matching
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Knowledge base
                                </li>
                            </ul>
                        </div>
                        <div class="mt-6">
                            <button class="w-full btn-primary whitespace-nowrap subscribe-btn"
                                    data-app="pro"
                                    data-plan-key="monthly"
                                    data-plan-type="Monthly Subscription"
                                    data-price="19.00"
                                    data-period="MONTH"
                                    data-months="1"
                                    data-price-id="price_1Rq8dkJZcnPg0hIN3yp8uKRX">
                                Subscribe Monthly
                            </button>
                        </div>
                    </div>

                    <!-- Pro Quarterly -->
                    <div class="pricing-card pro-quarterly-card period-card rounded-xl border-2 border-primary p-6 bg-background relative flex flex-col h-full" data-app="pro" data-plan="quarterly" data-period="quarterly" style="display: none;">
                        <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                            <span class="bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium">
                                Most Popular
                            </span>
                        </div>
                        <div class="flex-1 space-y-4">
                            <div>
                                <h3 class="text-xl font-semibold">Pro Quarterly</h3>
                                <p class="text-sm text-muted-foreground">Advanced features with quarterly billing</p>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-baseline gap-1">
                                    <span class="text-3xl font-bold">$39</span>
                                    <span class="text-sm text-muted-foreground">/quarter</span>
                                </div>
                            </div>
                            <ul class="space-y-3 text-sm">
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    In-depth proposal evaluation
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Personalized research plan design
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Research implementation planning
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Intelligent target journal matching
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Knowledge base
                                </li>
                            </ul>
                        </div>
                        <div class="mt-6">
                            <button class="w-full btn-primary whitespace-nowrap subscribe-btn"
                                    data-app="pro"
                                    data-plan-key="quarterly"
                                    data-plan-type="Quarterly Subscription"
                                    data-price="39.00"
                                    data-period="MONTH"
                                    data-months="3"
                                    data-price-id="price_1Rq8dlJZcnPg0hINxi1G8Rzv">
                                Subscribe Quarterly
                            </button>
                        </div>
                    </div>


                </div>
            </section>

            <!-- Dynamic Intelligence Demo Section -->
            <section class="flex flex-col items-center justify-center gap-10 py-16 w-full relative">
                <div class="text-center space-y-4 max-w-4xl mx-auto px-6">
                    <h2 class="text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
                        Watch Intelligence in Action
                    </h2>
                    <p class="text-muted-foreground text-center text-balance font-medium">
                        See how Novax executes complex workflows with precision and autonomy
                    </p>
                </div>

                <div class="w-full max-w-5xl mx-auto px-6">
                    <!-- Demo Video/Animation Container -->
                    <div class="relative rounded-2xl overflow-hidden bg-background border border-border shadow-2xl">
                        <!-- Video Placeholder -->
                        <div class="aspect-video bg-gradient-to-br max-md:h-[300px]  from-primary/5 via-secondary/5 to-accent/5 flex items-center justify-center relative cursor-pointer hover:bg-gradient-to-br hover:from-primary/10 hover:via-secondary/10 hover:to-accent/10 transition-all" id="videoContainer">
                            <!-- Play Button Overlay -->
                            <div class="absolute inset-0  bg-black/10 flex items-center justify-center group hover:bg-black/20 transition-all">
                                <div class="w-20 h-20 z-50 bg-white/90 rounded-full flex items-center justify-center group-hover:bg-white group-hover:scale-110 transition-all shadow-lg">
                                    <svg class="w-8 h-8 text-primary ml-1" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                </div>
                            </div>

                            <!-- Click hint -->
                            <div class="absolute top-2 right-2 sm:top-4 sm:right-4 bg-blue-500 text-white px-2 py-1 sm:px-3 sm:py-1 rounded-full text-xs font-medium animate-pulse">
                                <span class="hidden sm:inline">Click to Play</span>
                                <span class="sm:hidden">Play</span>
                            </div>

                            <!-- Medical AI Case Preview -->
                            <div class="absolute inset-2 sm:inset-4 bg-background/95 rounded-xl border border-border/50 p-3 sm:p-6 flex flex-col justify-between">
                                <!-- Terminal-like Header -->
                                <div class="flex items-center gap-2 mb-2 sm:mb-4">
                                    <div class="flex gap-1 sm:gap-1.5">
                                        <div class="w-2 h-2 sm:w-3 sm:h-3 bg-red-500 rounded-full"></div>
                                        <div class="w-2 h-2 sm:w-3 sm:h-3 bg-yellow-500 rounded-full"></div>
                                        <div class="w-2 h-2 sm:w-3 sm:h-3 bg-green-500 rounded-full"></div>
                                    </div>
                                    <div class="text-xs sm:text-sm text-muted-foreground font-mono truncate">
                                        <span class="hidden sm:inline">GLP-1RA and Pancreatic Aging Research Design Case</span>
                                        <span class="sm:hidden">GLP-1RA Research Case</span>
                                    </div>
                                </div>

                                <!-- Case Preview Content -->
                                <div class="flex-1 flex flex-col justify-center space-y-2 sm:space-y-4">
                                    <div class="flex items-center gap-2 sm:gap-3">
                                        <svg class="w-4 h-4 sm:w-6 sm:h-6 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                        </svg>
                                        <div class="text-sm sm:text-lg font-semibold text-foreground">
                                            <span class="hidden sm:inline">GLP-1RA and Pancreatic Aging Research</span>
                                            <span class="sm:hidden">GLP-1RA Research</span>
                                        </div>
                                    </div>

                                    <div class="text-xs text-muted-foreground/75 italic">
                                        <span class="hidden sm:inline">View real medical research design cases and understand how AI assists in clinical research planning</span>
                                        <span class="sm:hidden">View real medical research cases</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- About Section - Company introduction and mission statement -->
            <section id="about" class="flex flex-col items-center justify-center gap-6 sm:gap-10 py-8 sm:py-16 w-full relative">
                <!-- Section Header - Main title and company description -->
                <div class="text-center space-y-3 sm:space-y-4 max-w-4xl mx-auto px-4 sm:px-6">
                    <h2 class="text-2xl sm:text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
                        About Novax
                    </h2>
                    <p class="text-muted-foreground text-center text-balance font-medium text-sm sm:text-base md:text-lg">
                        Novax is a next-generation AI assistant developed by the  team, dedicated to making artificial intelligence truly serve everyone
                    </p>
                </div>

                <!-- Main Content Container - Two-column layout for mission and features -->
                <div class="w-full max-w-5xl mx-auto px-4 sm:px-6">
                    <div class="about-grid grid grid-cols-1  gap-4 sm:gap-6 md:gap-12 items-start md:items-center">
                        <!-- Left Column - Mission statement and technical vision -->
                        <div class="space-y-4 sm:space-y-6">
                            <!-- Mission Statement - Company's core beliefs and goals -->
                            <div class="space-y-2 sm:space-y-3">
                                <h3 class="text-lg sm:text-xl md:text-2xl font-semibold text-foreground">Our Mission</h3>
                                <p class="text-muted-foreground leading-relaxed text-sm sm:text-base">
                                    Deeply rooted in the medical research academic field, driven by digital technology, we have built an innovative service system covering the entire lifecycle of clinical research, promoting standardization and outcome transformation by lowering research barriers and improving research quality.
                                </p>
                            </div>

                            <!-- Technical Vision - Open source philosophy and community approach -->
                            <div class="space-y-2 sm:space-y-3">
                                <h3 class="text-lg sm:text-xl md:text-2xl font-semibold text-foreground">Technical Vision</h3>
                                <p class="text-muted-foreground leading-relaxed text-sm sm:text-base">
                                    Reshaping medical research paradigms through digital technology, building a globally leading intelligent research ecosystem.
                                </p>
                            </div>

                            <!-- Feature Tags - Key product characteristics displayed as badges -->
                            <div class="flex flex-wrap gap-2 sm:gap-3">
                                <!-- Open Source Badge -->
                                <div class="flex items-center gap-1.5 sm:gap-2 px-2.5 sm:px-3 py-1.5 sm:py-2 bg-accent rounded-full">
                                    <svg class="w-3 h-3 sm:w-4 sm:h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-xs sm:text-sm font-medium">Tech Empowered</span>
                                </div>
                                <!-- Performance Badge -->
                                <div class="flex items-center gap-1.5 sm:gap-2 px-2.5 sm:px-3 py-1.5 sm:py-2 bg-accent rounded-full">
                                    <svg class="w-3 h-3 sm:w-4 sm:h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span class="text-xs sm:text-sm font-medium">Data Driven</span>
                                </div>
                                <!-- Privacy Badge -->
                                <div class="flex items-center gap-1.5 sm:gap-2 px-2.5 sm:px-3 py-1.5 sm:py-2 bg-accent rounded-full">
                                    <svg class="w-3 h-3 sm:w-4 sm:h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    <span class="text-xs sm:text-sm font-medium">Ecosystem Synergy</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- End About Section -->

            <!-- FAQ Section -->
            <section id="faq" class="flex flex-col items-center justify-center gap-6 sm:gap-10 py-8 sm:py-16 w-full relative bg-accent/20">
                <div class="text-center space-y-3 sm:space-y-4 max-w-4xl mx-auto px-4 sm:px-6">
                    <h2 class="text-2xl sm:text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
                        Frequently Asked Questions
                    </h2>
                    <p class="text-muted-foreground text-center text-balance font-medium text-sm sm:text-base">
                        Quick guide to understanding Novax's features and usage
                    </p>
                </div>

                <div class="w-full max-w-4xl mx-auto px-4 sm:px-6">
                    <div id="faq-container" class="space-y-4">
                        <!-- Loading state -->
                        <div class="flex justify-center items-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                            <span class="ml-2 text-muted-foreground">Loading...</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Footer -->
            <footer class="w-full border-t border-border bg-background">
                <div class="max-w-7xl mx-auto px-6 py-12">
                    <!-- Main Footer Content -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8 lg:gap-12">
                        <!-- Company Info -->
                        <div class="space-y-4">
                            <div class="flex items-center gap-2">
                                <a href="https://medxy.ai/novax" target="_blank" class="flex items-center gap-1 sm:gap-2 lg:gap-3 flex-shrink-0">
                                    <img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" alt="Novax" width="24" height="24" class="w-6 h-6">
                                    <span class="text-lg font-semibold">Novax</span>
                                </a>
                            </div>
                            <p class="text-sm text-muted-foreground max-w-sm">
                                Novax by MedSci – is a generalist AI Agent that acts on your behalf.
                            </p>
                        </div>

                        <!-- Novax Links -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-foreground">Novax</h4>
                            <ul class="space-y-2 text-sm text-muted-foreground">
                                <li><a href="https://news.medxy.ai/about-us/" target="_blank" class="hover:text-foreground transition-colors">About</a></li>
                                <li><button id="contact-modal-trigger" class="hover:text-foreground transition-colors cursor-pointer bg-transparent border-none text-left p-0 text-sm text-muted-foreground">Feedback‌</button></li>
                                <li><a href="https://news.medxy.ai/contact-us/" target="_blank" class="hover:text-foreground transition-colors">Contact-Us</a></li>
                            </ul>
                        </div>

                        <!-- Legal -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-foreground">Legal</h4>
                            <ul class="space-y-2 text-sm text-muted-foreground">
                                <li><a href="https://news.medxy.ai/privacy-policy-2/" target="_blank" class="hover:text-foreground transition-colors">Privacy Policy</a></li>
                                <!-- <li><a href="https://medsci.cn/about/index.do?id=14" target="_blank" class="hover:text-foreground transition-colors">Terms of Service</a></li> -->
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </main>

    <!-- Contact Modal (Exact FeedbackModal Style) -->
    <div id="contact-modal" class="fixed inset-0 bg-black bg-opacity-45 z-50 hidden flex items-center justify-center p-4">
        <div class="modal-container bg-white rounded-lg shadow-2xl w-full max-w-[680px] max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Feedback</h2>
                <button id="contact-modal-close" class="text-gray-400 hover:text-gray-600 transition-colors p-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-6">
                <form id="contact-form" class="mt-4">
                    <!-- Hidden Title Field -->
                    <input type="hidden" id="mgTitle" name="mgTitle" value="feedback">

                    <!-- Content Field -->
                    <div class="mb-6">
                        <label for="mgContent" class="block text-sm font-medium text-gray-900 mb-2">
                            Feedback Content <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <textarea
                                id="mgContent"
                                name="mgContent"
                                required
                                rows="6"
                                maxlength="1000"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all placeholder:text-gray-400 resize-none text-sm"
                                placeholder="Please describe the issue or suggestion in detail. We value every feedback."
                            ></textarea>
                            <div class="absolute bottom-2 right-2 text-xs text-gray-400 bg-white px-1">
                                <span id="char-count">0</span> / 1000
                            </div>
                        </div>
                    </div>

                    <!-- Upload Images Section -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-900 mb-2">Upload Images</label>
                        <div class="space-y-3">
                            <!-- Drag Upload Area -->
                            <div id="upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 cursor-pointer">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-700 mb-2">Upload Images</h3>
                                    <p class="text-sm text-gray-500 mb-4">Supports PNG, JPG, GIF formats, max 10MB per file, up to 3 images</p>
                                    <p class="text-sm text-gray-400">Drag or click to select images</p>
                                </div>
                            </div>

                            <!-- Hidden File Input -->
                            <input type="file" id="file-input" multiple accept=".png,.jpg,.jpeg,.gif" style="display: none;">

                            <!-- Uploaded Images Grid -->
                            <div id="uploaded-images" class="grid grid-cols-2 gap-3" style="display: none;"></div>

                            <!-- Max Images Message -->
                            <div id="max-images-message" class="text-sm text-gray-500" style="display: none;">
                                Maximum 3 images reached
                            </div>

                            <!-- Upload Progress -->
                            <div id="upload-progress" class="mt-4" style="display: none;">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                    <span id="progress-text" class="text-sm text-gray-600">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end gap-3 mt-6">
                        <button
                            type="button"
                            id="contact-cancel"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            id="contact-submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        >
                            <span id="submit-text">Submit Feedback</span>
                            <svg id="submit-spinner" class="w-4 h-4 animate-spin hidden" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Image Preview Modal -->
    <div id="image-preview-modal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[60]" style="display: none;">
        <div class="relative max-w-4xl max-h-4xl p-4">
            <button id="close-preview" class="absolute top-2 right-2 text-white hover:text-gray-300 z-10">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <img id="preview-image" class="max-w-full max-h-96 object-contain rounded-lg" src="" alt="">
        </div>
    </div>

    <!-- JavaScript for Interactivity -->
    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to 'light'
        const currentTheme = localStorage.getItem('theme') || 'light';
        html.classList.toggle('dark', currentTheme === 'dark');

        themeToggle.addEventListener('click', () => {
            const isDark = html.classList.contains('dark');
            html.classList.toggle('dark', !isDark);
            localStorage.setItem('theme', !isDark ? 'dark' : 'light');

            // Update theme toggle icon
            const icon = themeToggle.querySelector('svg');
            if (!isDark) {
                // Switch to moon icon for dark mode
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>';
            } else {
                // Switch to sun icon for light mode
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>';
            }
        });

        // Smooth Scrolling for Navigation Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar Scroll Effect with Width Animation
        let lastScrollY = window.scrollY;
        const navbar = document.getElementById('navbar');
        const navbarContainer = document.getElementById('navbar-container');
        const navbarInner = navbar.querySelector('div > div');

        // Initial state
        let hasScrolled = false;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            const shouldHaveScrolled = currentScrollY > 10;

            if (shouldHaveScrolled !== hasScrolled) {
                hasScrolled = shouldHaveScrolled;

                if (hasScrolled) {
                    // Scrolled state
                    navbar.classList.remove('top-4', 'mx-0');
                    navbar.classList.add('top-6');

                    // Animate width from 70rem to 800px
                    navbarContainer.style.width = '880px';
                    navbarContainer.style.maxWidth = '880px';
                    navbarContainer.classList.add('px-2');
                    navbarContainer.classList.remove('px-7');

                    // Enhance backdrop blur and add border on scroll
                    navbarInner.classList.remove('bg-background/90');
                    navbarInner.classList.add('bg-background/95', 'border', 'border-border', 'shadow-lg');
                } else {
                    // Initial state
                    navbar.classList.add('top-4', 'mx-0');
                    navbar.classList.remove('top-6');

                    // Animate width back to 70rem
                    navbarContainer.style.width = '70rem';
                    navbarContainer.style.maxWidth = '100%';
                    navbarContainer.classList.remove('px-2');
                    navbarContainer.classList.add('px-7');

                    // Restore initial state - remove border
                    navbarInner.classList.remove('bg-background/95', 'border', 'border-border', 'shadow-lg');
                    navbarInner.classList.add('bg-background/90');
                }
            }

            lastScrollY = currentScrollY;

            // Update active navigation item
            updateActiveNavItem();
        });

        // Active Navigation Item Logic
        function updateActiveNavItem() {
            const sections = ['hero', 'use-cases', 'pricing', 'about', 'faq'];
            const navLinks = document.querySelectorAll('.nav-link');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

            // Dynamic offset based on screen size
            let offset = 170; // Default desktop offset
            if (window.innerWidth <= 480) {
                offset = 110; // Small mobile screens
            } else if (window.innerWidth <= 640) {
                offset = 120; // Small-medium mobile screens
            } else if (window.innerWidth <= 768) {
                offset = 130; // Medium mobile screens
            }

            for (const section of sections) {
                const element = document.getElementById(section);
                if (element) {
                    const rect = element.getBoundingClientRect();
                    if (rect.top <= offset && rect.bottom >= offset) {
                        // Update desktop nav links
                        navLinks.forEach(link => {
                            const linkSection = link.getAttribute('data-section');
                            if (linkSection === section) {
                                // Active state
                                link.classList.remove('text-muted-foreground', 'hover:bg-accent');
                                link.classList.add('text-primary', 'bg-primary/10', 'hover:bg-primary/15');
                            } else {
                                // Inactive state
                                link.classList.remove('text-primary', 'bg-primary/10', 'hover:bg-primary/15');
                                link.classList.add('text-muted-foreground', 'hover:bg-accent');
                            }
                        });

                        // Update mobile nav links
                        mobileNavLinks.forEach(link => {
                            const linkSection = link.getAttribute('data-section');
                            if (linkSection === section) {
                                // Active state
                                link.classList.remove('text-muted-foreground', 'hover:bg-accent');
                                link.classList.add('text-primary', 'bg-primary/10', 'hover:bg-primary/15');
                            } else {
                                // Inactive state
                                link.classList.remove('text-primary', 'bg-primary/10', 'hover:bg-primary/15');
                                link.classList.add('text-muted-foreground', 'hover:bg-accent');
                            }
                        });
                        break;
                    }
                }
            }
        }

        // Enhanced Flickering Grid Animation
        function enhanceFlickeringGrid() {
            const grids = document.querySelectorAll('.flickering-grid');
            grids.forEach(grid => {
                // Add random flicker intervals
                setInterval(() => {
                    if (Math.random() > 0.7) {
                        grid.style.opacity = Math.random() * 0.4 + 0.1;
                        setTimeout(() => {
                            grid.style.opacity = '';
                        }, 100 + Math.random() * 200);
                    }
                }, 500 + Math.random() * 1000);
            });
        }

        // Chat Input Effects and File Upload Management
        const chatTextarea = document.querySelector('textarea[placeholder*="Enter your research"]');
        const submitButton = document.querySelector('.submit-button');
        const chatInputCard = document.querySelector('.chat-input-card');
        const fileUploadButton = document.getElementById('file-upload-btn');

        console.log('Elements found:', {
            chatTextarea: !!chatTextarea,
            submitButton: !!submitButton,
            chatInputCard: !!chatInputCard,
            fileUploadButton: !!fileUploadButton
        });

        // File upload state management (完全按照 React 组件逻辑)
        let uploadedFiles = [];
        let uploadData = [];
        let isUploading = false;
        let isDragOver = false;
        let dragCounter = 0;
        const maxFiles = 2; // 对应 React 中的 maxFilesLimit
        const maxFileSize = 50 * 1024 * 1024; // 50MB
        const allowedFileTypes = ['pdf', 'doc', 'docx', 'txt', 'md', 'xlsx', 'xls', 'pptx', 'ppt', 'csv', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];

        // Create hidden file input (对应 React 中的 fileInputRef)
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.multiple = true;
        fileInput.accept = allowedFileTypes.map(type => `.${type}`).join(',');
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // File validation function (对应 React 中的 validateFiles)
        function validateFiles(files) {
            const validFiles = [];
            const errors = [];

            // Check total file count
            if (uploadedFiles.length + files.length > maxFiles) {
                errors.push(`Maximum ${maxFiles} files allowed`);
                return { validFiles, errors };
            }

            for (const file of files) {
                // Check file size
                if (file.size > maxFileSize) {
                    errors.push(`File ${file.name} exceeds size limit (50MB)`);
                    continue;
                }

                // Check file type
                const fileExtension = file.name.split('.').pop().toLowerCase();
                if (!allowedFileTypes.includes(fileExtension)) {
                    errors.push(`Unsupported file type: ${file.name}`);
                    continue;
                }

                validFiles.push(file);
            }

            return { validFiles, errors };
        }

        // File upload using real API (对应 React 中的 difyApi.uploadFile)
        async function uploadFile(file) {
            const apiBase = getApiBase();
            const userInfo = JSON.parse(getUserInfo());
            const user = userInfo.userName || 'nologin';
            const currentApp = getCurrentApp();
            const appDetail = await getAppDetail(currentApp)
            const formData = new FormData();
            formData.append('file', file);
            formData.append('user', user);
            formData.append('appId', appDetail.dAppUuid);

            try {
                const response = await fetch(`${apiBase}/ai-base/v1/files/upload?locale=en`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
                        Authorization: `Bearer ${getUserToken()}`
                    }
                });

                const result = await response.json();
                console.log('File upload response:', result);

                if (result.code !== 0) {
                    throw new Error(result.msg || 'Upload failed');
                }

                return {
                    id: result.data.id,
                    name: file.name,
                    size: file.size,
                    transfer_method: 'local_file',
                    upload_file_id: result.data.id,
                    type: getFileTypeByName(file.name)
                };
            } catch (error) {
                console.error('File upload failed:', error);
                throw error;
            }
        }

        // Get file type by name (对应 React 中的 getFileTypeByName)
        function getFileTypeByName(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();
            if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) return 'image';
            if (['pdf', 'doc', 'docx', 'txt', 'md'].includes(extension)) return 'document';
            if (['xlsx', 'xls', 'csv'].includes(extension)) return 'spreadsheet';
            if (['pptx', 'ppt'].includes(extension)) return 'presentation';
            return 'file';
        }

        // Handle file upload (对应 React 中的 handleFilesUploaded)
        async function handleFilesUploaded(files) {
            // 检查登录状态
            if (!checkUserLogin()) {
                sessionStorage.setItem('redirectUrl', window.location.href);
                window.open('https://medxy.ai/en/login', '_blank')
                return;
            }

            const { validFiles, errors } = validateFiles(files);

            if (errors.length > 0) {
                errors.forEach(error => showMessage(error, 'error'));
                return;
            }

            if (validFiles.length === 0) return;

            isUploading = true;
            updateUploadButtonState();

            const successFiles = [];
            const successUploads = [];

            for (const file of validFiles) {
                try {
                    const uploadResult = await uploadFile(file);
                    successFiles.push(file);
                    successUploads.push(uploadResult);
                } catch (error) {
                    showMessage(`Upload failed: ${file.name}`, 'error');
                }
            }

            // Update state
            uploadedFiles.push(...successFiles);
            uploadData.push(...successUploads);

            isUploading = false;
            updateUploadButtonState();
            updateFileDisplay();
            updateSubmitButtonState();
        }

        // Remove file function (对应 React 中的 removeFile)
        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            uploadData.splice(index, 1);
            updateFileDisplay();
            updateSubmitButtonState();
        }

        // Show message function
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white ${
                type === 'error' ? 'bg-red-500' : type === 'info' ? 'bg-blue-500' : 'bg-green-500'
            }`;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        console.log('Checking condition:', { chatTextarea: !!chatTextarea, submitButton: !!submitButton });

        if (chatTextarea && submitButton) {
            console.log('✅ Both elements found, initializing event listeners...');
            // Auto-resize textarea
            function autoResize() {
                chatTextarea.style.height = 'auto';
                chatTextarea.style.height = Math.min(chatTextarea.scrollHeight, 200) + 'px';
            }

            // Update file display (对应 React 中的文件显示逻辑)
            function updateFileDisplay() {
                let fileDisplayContainer = document.querySelector('.uploaded-files-container');

                if (uploadedFiles.length > 0 && !fileDisplayContainer) {
                    // Create file display container (插入到 textarea 下方)
                    fileDisplayContainer = document.createElement('div');
                    fileDisplayContainer.className = 'uploaded-files-container px-2 pb-2';
                    fileDisplayContainer.innerHTML = `
                        <div class="flex flex-wrap gap-2">
                            <!-- Files will be inserted here -->
                        </div>
                    `;

                    // Insert before bottom controls
                    const bottomControls = chatInputCard.querySelector('.flex.items-center.justify-between');
                    bottomControls.parentNode.insertBefore(fileDisplayContainer, bottomControls);
                }

                if (fileDisplayContainer) {
                    const filesContainer = fileDisplayContainer.querySelector('.flex');
                    filesContainer.innerHTML = '';

                    uploadedFiles.forEach((file, index) => {
                        const fileElement = document.createElement('div');
                        fileElement.className = 'relative group';
                        fileElement.innerHTML = `
                            <div class="flex items-center justify-center w-11 h-11 bg-gray-50 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer">
                                <div class="text-xs font-medium text-gray-600">${getFileIcon(file.name)}</div>
                                <button
                                    onclick="removeFile(${index})"
                                    class="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors flex items-center justify-center opacity-100 md:opacity-0 md:group-hover:opacity-100"
                                >
                                    ✕
                                </button>
                            </div>
                        `;
                        filesContainer.appendChild(fileElement);
                    });

                    if (uploadedFiles.length === 0) {
                        fileDisplayContainer.remove();
                    }
                }
            }

            // Get file icon based on extension
            function getFileIcon(fileName) {
                const extension = fileName.split('.').pop().toLowerCase();
                const iconMap = {
                    'pdf': '📄',
                    'doc': '📝', 'docx': '📝',
                    'txt': '📄', 'md': '📄',
                    'xlsx': '📊', 'xls': '📊',
                    'pptx': '📊', 'ppt': '📊',
                    'csv': '📊',
                    'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'webp': '🖼️', 'svg': '🖼️'
                };
                return iconMap[extension] || '📎';
            }

            // Update upload button state
            function updateUploadButtonState() {
                if (fileUploadButton) {
                    fileUploadButton.disabled = isUploading || uploadedFiles.length >= maxFiles;
                    fileUploadButton.style.opacity = (isUploading || uploadedFiles.length >= maxFiles) ? '0.5' : '1';
                }
            }

            // Update submit button state (对应 React 中的提交按钮逻辑)
            function updateSubmitButtonState() {
                const hasContent = chatTextarea.value.trim().length > 0;
                const hasFiles = uploadedFiles.length > 0;
                const canSubmit = hasContent || hasFiles;
                console.log('hasContent:', hasContent, 'hasFiles:', hasFiles, 'canSubmit:', canSubmit);
                submitButton.disabled = !canSubmit;
                submitButton.classList.toggle('opacity-50', !canSubmit);
            }

            // Make functions available globally
            window.removeFile = removeFile;
            window.updateSubmitButtonState = updateSubmitButtonState;

            chatTextarea.addEventListener('input', () => {
                autoResize();
                updateSubmitButtonState();

                // Track user input (对应 React 中的用户输入跟踪)
                const newValue = chatTextarea.value;
                const currentSceneTitle = suggestedQuestions.find(s => s.id === selectedSuggestId)?.title || '';
                if (currentSceneTitle && newValue.startsWith(currentSceneTitle + ' ')) {
                    userInput = newValue.substring(currentSceneTitle.length + 1);
                } else {
                    userInput = newValue;
                }
            });

            // Focus effects
            chatTextarea.addEventListener('focus', () => {
                if (chatInputCard) {
                    chatInputCard.classList.add('ring-2', 'ring-primary/20');
                }
            });

            chatTextarea.addEventListener('blur', () => {
                if (chatInputCard) {
                    chatInputCard.classList.remove('ring-2', 'ring-primary/20');
                }
            });

            // Handle Enter key (对应 React 中的 handleKeyDown)
            chatTextarea.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (chatTextarea.value.trim() || uploadedFiles.length > 0) {
                        handleChatSubmit();
                    }
                }
                // Shift+Enter 自然换行，不需要特殊处理
            });

            // Submit handler
            submitButton.addEventListener('click', handleChatSubmit);

            // File upload button handler (对应 React 中的文件上传按钮点击)
            if (fileUploadButton) {
                fileUploadButton.addEventListener('click', () => {
                    if (!checkUserLogin()) {
                        sessionStorage.setItem('redirectUrl', window.location.href);
                        window.open('https://medxy.ai/en/login', '_blank')
                        return;
                    }
                    if (!isUploading && uploadedFiles.length < maxFiles) {
                        fileInput.click();
                    }
                });
            }

            // File input change handler
            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    handleFilesUploaded(files);
                }
                // Reset input value to allow selecting the same file again
                e.target.value = '';
            });

            // Drag and drop functionality (对应 React 中的拖拽功能，但不改变样式)
            function handleDragEnter(e) {
                e.preventDefault();
                e.stopPropagation();
                dragCounter++;
                if (dragCounter === 1) {
                    isDragOver = true;
                    // 只添加非常微妙的视觉反馈，不改变输入框样式
                    chatInputCard.style.boxShadow = '0 0 0 1px rgba(59, 130, 246, 0.2)';
                }
            }

            function handleDragOver(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            function handleDragLeave(e) {
                e.preventDefault();
                e.stopPropagation();
                dragCounter--;
                if (dragCounter === 0) {
                    isDragOver = false;
                    // 移除视觉反馈
                    chatInputCard.style.boxShadow = '';
                }
            }

            function handleDrop(e) {
                e.preventDefault();
                e.stopPropagation();
                isDragOver = false;
                dragCounter = 0;
                // 移除视觉反馈
                chatInputCard.style.boxShadow = '';

                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    handleFilesUploaded(files);
                }
            }

            // Add drag and drop event listeners to the chat input card
            chatInputCard.addEventListener('dragenter', handleDragEnter);
            chatInputCard.addEventListener('dragover', handleDragOver);
            chatInputCard.addEventListener('dragleave', handleDragLeave);
            chatInputCard.addEventListener('drop', handleDrop);

            // Initialize button states
            updateSubmitButtonState();
            updateUploadButtonState();





            // Drag and drop functionality (对应 React 中的拖拽功能，但不改变样式)
            function handleDragEnter(e) {
                e.preventDefault();
                e.stopPropagation();
                dragCounter++;
                if (dragCounter === 1) {
                    isDragOver = true;
                    // 只添加非常微妙的视觉反馈，不改变输入框样式
                    chatInputCard.style.boxShadow = '0 0 0 1px rgba(59, 130, 246, 0.2)';
                }
            }

            function handleDragOver(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            function handleDragLeave(e) {
                e.preventDefault();
                e.stopPropagation();
                dragCounter--;
                if (dragCounter === 0) {
                    isDragOver = false;
                    // 移除视觉反馈
                    chatInputCard.style.boxShadow = '';
                }
            }

            function handleDrop(e) {
                e.preventDefault();
                e.stopPropagation();
                isDragOver = false;
                dragCounter = 0;
                // 移除视觉反馈
                chatInputCard.style.boxShadow = '';

                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    handleFilesUploaded(files);
                }
            }

            // Add drag and drop event listeners to the chat input card
            chatInputCard.addEventListener('dragenter', handleDragEnter);
            chatInputCard.addEventListener('dragover', handleDragOver);
            chatInputCard.addEventListener('dragleave', handleDragLeave);
            chatInputCard.addEventListener('drop', handleDrop);

            // Initialize button states
            updateSubmitButtonState();
            updateUploadButtonState();

            async function handleChatSubmit() {
                const message = chatTextarea.value.trim();
                const hasFiles = uploadedFiles.length > 0;

                // 对应 React 中的提交验证逻辑
                if (!message && !hasFiles) return;

                // 检查登录状态
                if (!checkUserLogin()) {
                    sessionStorage.setItem('redirectUrl', window.location.href);
                    window.open('https://medxy.ai/en/login', '_blank')
                    return;
                }

                const currentLanguage = getCurrentLanguage();
                const currentApp = getCurrentApp();

                // 构建提交数据 (对应 React 中的 chatData)
                const chatData = {
                    query: message,
                    appNameEn: currentApp,
                    appUuid: await getAppDetail(currentApp),
                    isNewConversation: true,
                    files: hasFiles ? uploadData : undefined,
                    timestamp: Date.now()
                };

                // 使用sessionStorage存储数据，标记为新对话 (完全对应 React 逻辑)
                sessionStorage.setItem('chatInitialData', JSON.stringify(chatData));

                // 直接跳转到新对话页面（使用"new"标识符）- 完全对应 React 逻辑
                const targetUrl = `/${currentLanguage}/${currentApp}/new`;

                console.log('Submitting chat data:', chatData);
                console.log('Redirecting to:', targetUrl);

                // 跳转到 ChatDetail 页面
                window.location.href = targetUrl;
            }
        }

        // Scene/Prompt Template Management (对应 React 中的场景功能)
        let suggestedQuestions = [];
        let selectedSuggestId = 'general';
        let userInput = '';
        let showSceneMenu = false;
        let isLoadingConfig = false;

        // Mock scene data (对应 React 中从 API 获取的场景数据)
        const mockSceneData = [
            {
                id: 'general',
                title: 'General Assistant',
                desc: '通用目的帮助',
                icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBzdHJva2U9IiM2MzY2RjEiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo='
            },
            {
                id: 'research',
                title: 'Research Assistant',
                desc: '信息收集和分析',
                icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDIxTDE2LjUxNCAxNi41MDZNMTkgMTBBOSA5IDAgMTEgMSAxMEE5IDkgMCAwIDEgMTkgMTBaIiBzdHJva2U9IiM2MzY2RjEiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo='
            },
            {
                id: 'writing',
                title: 'Writing Assistant',
                desc: '内容创作和编辑',
                icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE3IDNBMi44MjggMi44MjggMCAxIDEgMTkuODI4IDUuODI4TDYuMTcxIDE5LjQ4NUEyIDIgMCAwIDEgNC44MjggMjBIMkwyIDEyTDE2LjE3MiAzWiIgc3Ryb2tlPSIjNjM2NkYxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K'
            },
            {
                id: 'coding',
                title: 'Code Assistant',
                desc: '编程和开发',
                icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE2IDEyTDIwIDhMMTYgNE04IDEyTDQgOEw4IDRNMTIgMjBMMTQgNCIgc3Ryb2tlPSIjNjM2NkYxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K'
            }
        ];

        // Load scene data (对应 React 中的 loadConfig - 使用真实 API)
        async function loadSceneConfig() {
            isLoadingConfig = true;

            // 获取当前语言和应用
            const currentLanguage = getCurrentLanguage();
            const currentApp = getCurrentApp();

            try {
                // 调用真实 API 获取场景数据
                const apiBase = getApiBase();
                const response = await fetch(`${apiBase}/ai-base/index/getConfigByKeyFromCache?configKey=suggested_questions&locale=${currentLanguage}`);
                const result = await response.json();

                if (result && result.code === 0 && result.data) {
                    const questions = result.data[currentApp];
                    if (questions && Array.isArray(questions)) {
                        // 过滤当前语言的场景
                        const filteredQuestions = questions.filter(question => question.lang === currentLanguage);

                        // 添加通用场景（只添加英文版本）
                        const generalScene = {
                            id: 'general',
                            title: 'General Assistant',
                            desc: 'General purpose assistance for any task',
                            icon: 'https://img.medsci.cn/202507/5a4f482dc99f49938f229862f6192c3a-RX57Duz1XbGU.png',
                            lang: 'en'
                        };

                        // 将通用场景放在第一位
                        suggestedQuestions = [generalScene, ...filteredQuestions];
                        selectedSuggestId = suggestedQuestions[0]?.id || '';
                    } else {
                        console.log('No questions found for app:', currentApp);
                        // 即使没有API数据，也提供通用场景
                        suggestedQuestions = [{
                            id: 'general',
                            title: 'General Assistant',
                            desc: 'General purpose assistance for any task',
                            icon: 'https://img.medsci.cn/202507/5a4f482dc99f49938f229862f6192c3a-RX57Duz1XbGU.png',
                            lang: 'en'
                        }];
                        selectedSuggestId = 'general';
                    }
                } else {
                    console.log('Invalid API response:', result);
                    // API响应无效时，也提供通用场景
                    suggestedQuestions = [{
                        id: 'general',
                        title: 'General Assistant',
                        desc: 'General purpose assistance for any task',
                        icon: 'https://img.medsci.cn/202507/5a4f482dc99f49938f229862f6192c3a-RX57Duz1XbGU.png',
                        lang: 'en'
                    }];
                    selectedSuggestId = 'general';
                }
            } catch (error) {
                console.error('Failed to get configuration data:', error);
                // 如果API调用失败，提供通用场景
                suggestedQuestions = [{
                    id: 'general',
                    title: 'General Assistant',
                    desc: 'General purpose assistance for any task',
                    icon: 'https://img.medsci.cn/202507/5a4f482dc99f49938f229862f6192c3a-RX57Duz1XbGU.png',
                    lang: 'en'
                }];
                selectedSuggestId = 'general';
            } finally {
                isLoadingConfig = false;
            }

            return suggestedQuestions;
        }

        // 获取当前语言
        function getCurrentLanguage() {
            return  'en';
        }

        // 获取当前应用
        function getCurrentApp() {
            // 从应用选择器获取当前选中的应用
            const selectedModelSpan = document.getElementById('selected-model');
            if (selectedModelSpan) {
                return selectedModelSpan.textContent.trim();
            }

            // 如果选择器不存在，从URL路径获取应用名称
            const path = window.location.pathname;
            const appMatch = path.match(/\/(novax-base|novax-pro|elavax-base|elavax-pro)/);
            return appMatch ? appMatch[1] : 'novax-base';
        }

        // 查询应用详情
        async function getAppDetail(appName) {
            try {
                const apiBase = getApiBase();

                // 构建应用UUID映射（根据应用名称获取对应的UUID）
                const appUuidMap = {
                    'novax-base': 'novax-base',
                    'novax-pro': 'novax-pro'
                };

                const appUuid = appUuidMap[appName] || appName;

                // 调用应用详情API
                const response = await fetch(`${apiBase}/ai-base/index/getAppByNameEn?appUuid=${encodeURIComponent(appUuid)}&locale=en`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 如果需要认证，可以添加认证头
                         'Authorization': `Bearer ${getUserToken()}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result && result.code === 0 && result.data) {
                    return result.data;
                } else {
                    throw new Error(result.msg || 'Failed to get app details');
                }
            } catch (error) {
                console.error('Failed to call app details API:', error);
                throw error;
            }
        }

        // 获取API基础地址
        function getApiBase() {
            return 'https://medxy.ai/dev-api';
        }

        // 获取用户token
        function getUserToken() {
            // 简单的cookie获取函数
            const name = 'medxyToken=';
            const decodedCookie = decodeURIComponent(document.cookie);
            const ca = decodedCookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) === 0) {
                console.log('Got user token:', c.substring(name.length, c.length));
                    return c.substring(name.length, c.length);
                }
            }
            return '';
        }
         function getUserInfo() {
            // 简单的cookie获取函数
            const name = 'userInfo=';
            const decodedCookie = decodeURIComponent(document.cookie);
            const ca = decodedCookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) === 0) {
                console.log('Got user info:', c.substring(name.length, c.length));
                    return c.substring(name.length, c.length);
                }
            }
            return '';
        }

        // 检查用户登录状态
        function checkUserLogin() {
            const token = getUserToken();
            if (!token) {
            return false;
            }
            return true;
        }

        // Handle scene selection (对应 React 中的 handleSceneSelect)
        function handleSceneSelect(id, title) {
            // 获取 chatTextarea 元素
            const chatTextarea = document.querySelector('textarea[placeholder*="Enter your research"]');

            // 更新场景选择，直接使用场景标题
            if (chatTextarea) {
                chatTextarea.value = title + ' ' + userInput.trim();

                // 触发input事件以更新提交按钮状态
                const inputEvent = new Event('input', { bubbles: true });
                chatTextarea.dispatchEvent(inputEvent);

                // 自动调整textarea高度
                chatTextarea.style.height = 'auto';
                chatTextarea.style.height = Math.min(chatTextarea.scrollHeight, 200) + 'px';
            }

            selectedSuggestId = id;
            showSceneMenu = false;

            // Update prompt selector display
            const selectedPromptSpan = document.getElementById('selected-prompt');
            if (selectedPromptSpan) {
                selectedPromptSpan.textContent = title;
            }

            // Hide scene menu
            hideSceneMenu();

            // Update submit button state - try both global and window versions
            if (typeof window.updateSubmitButtonState === 'function') {
                window.updateSubmitButtonState();
            } else if (typeof updateSubmitButtonState === 'function') {
                updateSubmitButtonState();
            }
        }

        // Show scene menu (对应 React 中的场景弹出框)
        function showSceneMenuPopup() {
            if (isLoadingConfig) return;

            showSceneMenu = true;

            // Create scene menu if it doesn't exist
            let sceneMenu = document.getElementById('scene-menu');
            if (!sceneMenu) {
                sceneMenu = document.createElement('div');
                sceneMenu.id = 'scene-menu';
                sceneMenu.className = 'dropdown-menu absolute bottom-full right-0 mb-2 w-64 bg-background border border-border rounded-lg shadow-lg z-50';
                sceneMenu.innerHTML = `
                    <div class="p-2">
                        <div id="scene-options" class="space-y-1">
                            <!-- Scene options will be inserted here -->
                        </div>
                    </div>
                `;

                // Insert after prompt selector
                const promptSelector = document.getElementById('prompt-selector').parentElement;
                promptSelector.appendChild(sceneMenu);
            }

            // Populate scene options
            const sceneOptions = document.getElementById('scene-options');
            sceneOptions.innerHTML = '';

            if (isLoadingConfig) {
                sceneOptions.innerHTML = `
                    <div class="flex items-center justify-center py-4">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <span class="ml-2 text-sm text-muted-foreground">Loading...</span>
                    </div>
                `;
            } else if (suggestedQuestions.length === 0) {
                sceneOptions.innerHTML = `
                    <div class="flex items-center justify-center py-4">
                        <span class="text-sm text-muted-foreground">No scenes available for this application</span>
                    </div>
                `;
            } else {
                suggestedQuestions.forEach((scene) => {
                    const sceneOption = document.createElement('button');
                    sceneOption.className = 'dropdown-option w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-md transition-colors';
                    sceneOption.innerHTML = `
                        <div class="flex items-start gap-2">
                            <img src="${scene.icon}" alt="${scene.title}" class="w-4 h-4 object-cover rounded">
                            <div>
                                <div class="font-medium">${scene.title}</div>
                                <div class="text-xs text-muted-foreground">${scene.desc}</div>
                            </div>
                        </div>
                    `;
                    sceneOption.addEventListener('click', () => {
                        handleSceneSelect(scene.id, scene.title);
                    });
                    sceneOptions.appendChild(sceneOption);
                });
            }

            sceneMenu.style.display = 'block';
        }

        // Hide scene menu
        function hideSceneMenu() {
            showSceneMenu = false;
            const sceneMenu = document.getElementById('scene-menu');
            if (sceneMenu) {
                sceneMenu.style.display = 'none';
            }
        }

        // Model and Prompt Selector Logic
        function initializeSelectors() {
            const modelSelector = document.getElementById('model-selector');
            const modelDropdown = document.getElementById('model-dropdown');
            const selectedModelSpan = document.getElementById('selected-model');
            const modelOptions = document.querySelectorAll('.model-option');

            const promptSelector = document.getElementById('prompt-selector');
            const promptDropdown = document.getElementById('prompt-dropdown');
            const selectedPromptSpan = document.getElementById('selected-prompt');
            const promptOptions = document.querySelectorAll('.prompt-option');

            const formatSelector = document.getElementById('format-selector');
            const formatDropdown = document.getElementById('format-dropdown');
            const selectedFormatSpan = document.getElementById('selected-format');
            const formatOptions = document.querySelectorAll('.format-option');

            // Model selector functionality
            if (modelSelector && modelDropdown) {
                modelSelector.addEventListener('click', (e) => {
                    e.stopPropagation();
                    modelDropdown.classList.toggle('hidden');
                    promptDropdown?.classList.add('hidden'); // Close other dropdown
                    formatDropdown?.classList.add('hidden'); // Close format dropdown
                });

                modelOptions.forEach(option => {
                    option.addEventListener('click', async (e) => {
                        e.stopPropagation();
                        const modelName = option.querySelector('.font-medium').textContent;
                        const modelValue = option.getAttribute('data-model');

                        selectedModelSpan.textContent = modelName;
                        modelDropdown.classList.add('hidden');

                        // Update active state
                        modelOptions.forEach(opt => opt.classList.remove('bg-accent'));
                        option.classList.add('bg-accent');

                        console.log('Selected model:', modelValue);

                        try {
                            // 查询当前应用详情
                            console.log('🔄 正在获取应用详情:', { appName: modelName, appValue: modelValue });
                            const appDetail = await getAppDetail(modelName);
                            console.log('✅ 应用详情获取成功:', appDetail);

                            // 可以在这里处理应用详情数据，比如更新状态或缓存
                            // 例如：updateAppDetailCache(appDetail);

                        } catch (error) {
                            console.error('❌ 获取应用详情失败:', error);
                            // 即使获取详情失败，也继续进行应用切换
                        }

                        // 重新加载场景数据（当应用切换时）
                        await loadSceneConfig();

                        // 重置场景选择器显示
                        const selectedPromptSpan = document.getElementById('selected-prompt');
                        if (selectedPromptSpan && suggestedQuestions.length > 0) {
                            selectedPromptSpan.textContent = suggestedQuestions[0].title;
                        } else if (selectedPromptSpan) {
                            selectedPromptSpan.textContent = 'General';
                        }
                    });
                });
            }

            // Prompt selector functionality (修改为使用场景数据)
            if (promptSelector) {
                promptSelector.addEventListener('click', async (e) => {
                    e.stopPropagation();

                    // Close model dropdown
                    if (modelDropdown) {
                        modelDropdown.classList.add('hidden');
                    }

                    // Load scene data if not loaded
                    if (suggestedQuestions.length === 0) {
                        await loadSceneConfig();
                    }

                    // Show scene menu
                    showSceneMenuPopup();
                });
            }

            // Keep original prompt options for fallback
            if (promptOptions.length > 0) {
                promptOptions.forEach(option => {
                    option.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const promptName = option.querySelector('.font-medium').textContent;
                        const promptValue = option.getAttribute('data-prompt');

                        selectedPromptSpan.textContent = promptName.replace(' Assistant', '');
                        promptDropdown.classList.add('hidden');

                        // Update active state
                        promptOptions.forEach(opt => opt.classList.remove('bg-accent'));
                        option.classList.add('bg-accent');

                        // Update placeholder based on selected prompt
                        updatePlaceholder(promptValue);

                        console.log('Selected prompt:', promptValue);
                    });
                });
            }

            // Format selector functionality
            if (formatSelector && formatDropdown) {
                formatSelector.addEventListener('click', (e) => {
                    e.stopPropagation();
                    formatDropdown.classList.toggle('hidden');
                    modelDropdown?.classList.add('hidden'); // Close other dropdowns
                    promptDropdown?.classList.add('hidden');
                });

                formatOptions.forEach(option => {
                    option.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const formatName = option.querySelector('.font-medium').textContent;
                        const formatValue = option.getAttribute('data-format');

                        // Update display
                        selectedFormatSpan.textContent = formatValue === 'markdown' ? 'MD' : 'HTML';
                        formatDropdown.classList.add('hidden');

                        // Update active state
                        formatOptions.forEach(opt => opt.classList.remove('bg-accent'));
                        option.classList.add('bg-accent');

                        // Update global format state
                        window.outputFormatPlugin.setFormat(formatValue);

                        console.log('Selected format:', formatValue);
                    });
                });
            }

            // Close dropdowns when clicking outside
            document.addEventListener('click', () => {
                modelDropdown?.classList.add('hidden');
                promptDropdown?.classList.add('hidden');
                formatDropdown?.classList.add('hidden');
                hideSceneMenu(); // Also hide scene menu
            });

            // Set initial active states
            modelOptions[0]?.classList.add('bg-accent');
            promptOptions[0]?.classList.add('bg-accent');
            formatOptions[0]?.classList.add('bg-accent'); // Set markdown as default
        }

        // Update placeholder based on selected prompt template
        function updatePlaceholder(promptType) {
            const placeholders = {
                general: "Describe what you need help with...",
                coding: "Describe the code you want to write or debug...",
                research: "What topic would you like me to research?",
                writing: "What would you like me to help you write?"
            };

            if (chatTextarea && placeholders[promptType]) {
                chatTextarea.placeholder = placeholders[promptType];
            }
        }

        // Medical AI Case Data - Real conversation from a.txt
        const medicalCaseData = {
            title: "Clinical study with follow-up questions",
            caseId: "glp1ra-brain-study",
            description: "Observational research design based on clinical database",
            conversation: [
                {
                    type: "user",
                    content: "I am interested in GLP-1RA and its effect on brain senescence. Can you design a observational clinical study for me?",
                    timestamp: "2024-01-15 10:30:00"
                },
                {
                    type: "ai",
                    content: `It is thinking according to your needs, which is expected to take 20~90s. Please wait patiently☺……
~If the word document cannot be recognized, it is recommended to convert it into PDF and try again☺.

Based on the information you provided, please review the following:

**1. Your Research_Field:**
The effect of GLP-1 Receptor Agonists (GLP-1RA) on brain senescence.

**2. Your Research_Foundation:**
Not provided.

**3. Your Research_Condition:**
Not provided.

**4. Your Purpose:**
To design an observational clinical study.

Please confirm if the information above is correct. Reply **Yes** to proceed with the design process. If you need to add or modify any information, please enter your requirements directly.`,
                    timestamp: "2024-01-15 10:30:30"
                },
                {
                    type: "user",
                    content: "Yes",
                    timestamp: "2024-01-15 10:31:00"
                },
                {
                    type: "ai",
                    content: `Clinical research type: Target Trial Emulation

Based on your requirements, the following research plan is recommended:

**1. Research Direction**

• **Topic:** The Effect of GLP-1 Receptor Agonists on Brain Senescence in Patients with Type 2 Diabetes: A Target Trial Emulation Study.

• **Hypothesis:** In individuals with type 2 diabetes, the initiation of GLP-1 Receptor Agonists (GLP-1RA) is associated with a lower risk of incident dementia and markers of cognitive decline compared to the initiation of an alternative class of antidiabetic drugs (e.g., DPP-4 inhibitors).

• **Basis for Hypothesis:** Preclinical studies indicate that GLP-1 receptors are expressed in key brain regions like the hippocampus and cortex, and GLP-1RA have demonstrated neuroprotective effects, including reducing neuroinflammation and promoting neuronal survival in animal models.

**2. Brief Research Plan**

• **Study Design:** A retrospective cohort study employing the **Target Trial Emulation** framework. This design uses observational data to mimic the key components of a pragmatic randomized controlled trial (RCT).

• **Data Source:** A large longitudinal database containing patient-level data, such as national health insurance claims data, a large hospital electronic health record (EHR) network (**e.g., TriNetX, Optum, UK Biobank**).

• **Study Population:** Patients with a diagnosis of Type 2 Diabetes Mellitus (T2DM).

• **Inclusion Criteria:**
  - Age 50 years or older at the time of treatment initiation
  - A validated diagnosis of T2DM
  - New users of either a GLP-1RA or a DPP-4 inhibitor
  - At least 12 months of continuous enrollment in the database

• **Exclusion Criteria:**
  - Any diagnosis of dementia or Mild Cognitive Impairment (MCI) prior to index date
  - History of major neurological conditions
  - Diagnosis of Type 1 Diabetes

• **Primary Outcome:** Incident all-cause dementia, identified through validated algorithms using **ICD-10 codes (e.g., F00-F03, G30)**.

• **Statistical Analysis:** An **Inverse Probability of Treatment Weighting (IPTW)** approach will be used to create a pseudo-population in which the baseline characteristics are balanced.`,
                    timestamp: "2024-01-15 10:32:45"
                },
                {
                    type: "user",
                    content: "Good, but it is a little bit complicated, can you design a more simple observational study for me based on clinical database?",
                    timestamp: "2024-01-15 10:35:00"
                },
                {
                    type: "ai",
                    content: `Of course. The previous plan, while robust, can certainly be simplified for a more straightforward approach. Here is a revised, simpler observational study design based on your request.

**1. Research Direction**

• **Topic:** The Association Between GLP-1 Receptor Agonist Use and Dementia Risk in Patients with Type 2 Diabetes: A Retrospective Cohort Study.

• **Hypothesis:** In individuals with type 2 diabetes, the use of GLP-1 Receptor Agonists (GLP-1RA) is associated with a lower risk of incident dementia compared to the use of an alternative class of antidiabetic drugs (e.g., DPP-4 inhibitors).

**2. Brief Research Plan**

• **Study Design:** ***A retrospective cohort study using propensity score matching. This design is more direct than a target trial emulation. We will identify two groups of patients (GLP-1RA users and DPP-4 inhibitor users) from the database and use statistical methods to match them based on their baseline characteristics.***

• **Data Source:** A large longitudinal database containing patient-level data, such as a large hospital electronic health record (EHR) network (**e.g., TriNetX, Optum**) or national health insurance claims data.

• **Inclusion Criteria:**
  - Age 50 years or older
  - A validated diagnosis of T2DM (e.g., based on ICD-10 codes)
  - New users of either a GLP-1RA or a DPP-4 inhibitor
  - At least 12 months of continuous enrollment in the database

• **Exclusion Criteria:**
  - Any diagnosis of dementia or MCI before starting the drug
  - History of major neurological conditions
  - Diagnosis of Type 1 Diabetes

• **Statistical Analysis:**
  - ***Baseline Confounder Adjustment: We will use Propensity Score Matching (PSM). A propensity score will be calculated for every patient. Then, each GLP-1RA user will be matched 1:1 to a DPP-4 inhibitor user with a very similar propensity score.***
  - ***Primary Analysis: After matching, we will use a Cox proportional hazards model to compare the time to dementia diagnosis between the two matched groups.***

**3. Future 2-Year Plan**

***This simplified design can likely be completed on a shorter timeline.***

| **Phase** | **Task** | **Year 1** | **Year 2** |
|:---|:---|:---|:---|
| **Phase 1: Setup & Data Collection** | | |
| | Develop Study Protocol & Obtain IRB Approval | ████ | |
| | Secure Data Access & Extract Data | ████ | |
| **Phase 2: Analysis & Dissemination** | | |
| | Data Cleaning and Propensity Score Matching | | ████ |
| | Main Analysis & Manuscript Preparation | | ████ |

Please review this simplified plan. Does this direction meet your needs?`,
                    timestamp: "2024-01-15 10:37:30"
                }
            ]
        };

        // Demo Video Interaction with Medical AI Case
        function initializeDemoVideo() {
            const videoContainer = document.getElementById('videoContainer');
            const demoContainer = document.querySelector('.aspect-video');

            if (videoContainer && demoContainer) {
                videoContainer.addEventListener('click', () => {
                    console.log('Video container clicked!'); // Debug log

                    // Show medical AI case interface
                    showMedicalAICase(demoContainer.parentElement);
                });

                // Add hover effect
                videoContainer.addEventListener('mouseenter', () => {
                    videoContainer.style.transform = 'scale(1.02)';
                });

                videoContainer.addEventListener('mouseleave', () => {
                    videoContainer.style.transform = 'scale(1)';
                });
            } else {
                console.log('Video container not found!'); // Debug log
            }
        }

        // Show Medical AI Case Interface
        function showMedicalAICase(container) {
            const caseInterface = document.createElement('div');
            caseInterface.className = 'absolute inset-0 bg-white flex flex-col medical-ai-case';
            caseInterface.innerHTML = `
                <div class="flex-1 flex flex-col h-full">
                    <!-- Header -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 border-b bg-gray-50 gap-3 sm:gap-0">
                        <div class="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                            <div class="w-6 h-6 sm:w-8 sm:h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 sm:w-4 sm:h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <div class="min-w-0 flex-1">
                                <h3 class="font-semibold text-gray-900 text-sm sm:text-base truncate">${medicalCaseData.title}</h3>
                            </div>
                        </div>
                        <div class="flex items-center gap-1 sm:gap-2 flex-wrap">
                            <button id="replayBtn" class="px-2 py-1 sm:px-3 sm:py-1 text-xs sm:text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors whitespace-nowrap">
                                <span class="hidden sm:inline">Replay Conversation</span>
                                <span class="sm:hidden">Replay</span>
                            </button>
                            <button id="pauseBtn" class="px-2 py-1 sm:px-3 sm:py-1 text-xs sm:text-sm bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors hidden whitespace-nowrap">
                                Pause
                            </button>
                            <button id="resumeBtn" class="px-2 py-1 sm:px-3 sm:py-1 text-xs sm:text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors hidden whitespace-nowrap">
                                Resume
                            </button>
                            <button id="closeBtn" class="px-2 py-1 sm:px-3 sm:py-1 text-xs sm:text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors whitespace-nowrap">
                                Close
                            </button>
                        </div>
                    </div>

                    <!-- Chat Container -->
                    <div id="chatContainer" class="flex-1 overflow-y-auto p-2 sm:p-4 space-y-2 sm:space-y-4 bg-gray-50">
                        <!-- Messages will be inserted here -->
                    </div>

                    <!-- Status Bar -->
                    <div id="statusBar" class="p-2 sm:p-3 bg-white border-t text-xs sm:text-sm text-gray-600">
                        <span id="statusText">Ready to start replay...</span>
                    </div>
                </div>
            `;

            container.appendChild(caseInterface);

            // Initialize controls
            initializeCaseControls(container, caseInterface);

            // Start replay automatically
            setTimeout(() => {
                startReplay();
            }, 1000);
        }

        // Initialize Case Controls
        function initializeCaseControls(container, caseInterface) {
            const closeBtn = caseInterface.querySelector('#closeBtn');
            const replayBtn = caseInterface.querySelector('#replayBtn');
            const pauseBtn = caseInterface.querySelector('#pauseBtn');
            const resumeBtn = caseInterface.querySelector('#resumeBtn');

            closeBtn.addEventListener('click', () => {
                // Stop any ongoing replay
                stopReplay();
                container.removeChild(caseInterface);
                // Show video container again
                const videoContainer = document.getElementById('videoContainer');
                if (videoContainer) {
                    videoContainer.style.display = 'flex';
                }
            });

            replayBtn.addEventListener('click', () => {
                startReplay();
            });

            pauseBtn.addEventListener('click', () => {
                pauseReplay();
            });

            resumeBtn.addEventListener('click', () => {
                resumeReplay();
            });
        }

        // Replay State Management
        let replayTimeout;
        let isReplayPaused = false;
        let currentMessageIndex = 0;
        let replayInProgress = false;

        // Smart Scroll Management
        let autoScrollEnabled = true;
        let userScrollTimeout = null;
        let lastScrollTop = 0;

        // Loading Management
        let loadingTasks = [];
        let completedTasks = 0;

        // Loading management functions
        function showLoading() {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.classList.remove('hidden');
            }
        }

        function hideLoading() {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.classList.add('hidden');
                // Remove from DOM after animation
                setTimeout(() => {
                    if (overlay.parentNode) {
                        overlay.parentNode.removeChild(overlay);
                    }
                }, 500);
            }
        }

        function updateLoadingProgress(message) {
            const progressElement = document.getElementById('loading-progress');
            if (progressElement) {
                progressElement.textContent = message;
            }
        }

        function addLoadingTask(taskName) {
            loadingTasks.push(taskName);
            updateLoadingProgress(`Loading ${taskName}...`);
        }

        function completeLoadingTask(taskName) {
            completedTasks++;
            const progress = Math.round((completedTasks / loadingTasks.length) * 100);
            updateLoadingProgress(`${taskName} loaded (${progress}%)`);

            // Hide loading when all tasks complete
            if (completedTasks >= loadingTasks.length) {
                setTimeout(() => {
                    updateLoadingProgress('Initialization complete!');
                    setTimeout(hideLoading, 500);
                }, 300);
            }
        }

        // Initialize Smart Scroll for Chat Container
        function initializeSmartScroll(chatContainer) {
            if (!chatContainer) return;

            // Add scroll event listener
            chatContainer.addEventListener('scroll', function() {
                const currentScrollTop = chatContainer.scrollTop;
                const scrollHeight = chatContainer.scrollHeight;
                const clientHeight = chatContainer.clientHeight;

                // Check if user scrolled manually (not at bottom)
                const isAtBottom = Math.abs(scrollHeight - clientHeight - currentScrollTop) < 5;

                // If user scrolled up manually, disable auto-scroll
                if (currentScrollTop < lastScrollTop && !isAtBottom) {
                    autoScrollEnabled = false;
                    showScrollToBottomHint(chatContainer);
                }

                // If user scrolled to bottom, re-enable auto-scroll
                if (isAtBottom) {
                    autoScrollEnabled = true;
                    hideScrollToBottomHint(chatContainer);
                }

                lastScrollTop = currentScrollTop;
            });
        }

        // Smart scroll to bottom (only if auto-scroll is enabled)
        function smartScrollToBottom(chatContainer) {
            if (!chatContainer || !autoScrollEnabled) return;

            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Show scroll to bottom hint
        function showScrollToBottomHint(chatContainer) {
            // Remove existing hint if any
            hideScrollToBottomHint(chatContainer);

            const hint = document.createElement('div');
            hint.id = 'scroll-to-bottom-hint';
            hint.className = 'absolute bottom-4 right-4 bg-primary text-primary-foreground px-3 py-2 rounded-full text-xs font-medium cursor-pointer shadow-lg animate-pulse z-10';
            hint.innerHTML = `
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                    <span>New messages</span>
                </div>
            `;

            // Add click handler to scroll to bottom and re-enable auto-scroll
            hint.addEventListener('click', () => {
                autoScrollEnabled = true;
                smartScrollToBottom(chatContainer);
                hideScrollToBottomHint(chatContainer);
            });

            // Add to chat container's parent (which should have relative positioning)
            const parent = chatContainer.parentElement;
            if (parent) {
                parent.style.position = 'relative';
                parent.appendChild(hint);
            }
        }

        // Hide scroll to bottom hint
        function hideScrollToBottomHint(chatContainer) {
            const hint = document.getElementById('scroll-to-bottom-hint');
            if (hint) {
                hint.remove();
            }
        }

        // Start Replay Function
        function startReplay() {
            const chatContainer = document.getElementById('chatContainer');
            const statusText = document.getElementById('statusText');
            const replayBtn = document.getElementById('replayBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resumeBtn = document.getElementById('resumeBtn');

            if (!chatContainer || !statusText) return;

            // Clear previous messages
            chatContainer.innerHTML = '';

            // Reset state
            currentMessageIndex = 0;
            isReplayPaused = false;
            replayInProgress = true;

            // Reset smart scroll state
            autoScrollEnabled = true;
            lastScrollTop = 0;
            hideScrollToBottomHint(chatContainer);

            // Clear any existing timeout
            if (replayTimeout) {
                clearTimeout(replayTimeout);
            }

            // Initialize smart scroll for this chat container
            initializeSmartScroll(chatContainer);

            // Update button visibility
            if (replayBtn) replayBtn.classList.add('hidden');
            if (pauseBtn) pauseBtn.classList.remove('hidden');
            if (resumeBtn) resumeBtn.classList.add('hidden');

            statusText.textContent = 'Replaying conversation...';

            // Start the replay
            showNextMessage();
        }

        // Show Next Message Function
        function showNextMessage() {
            if (!replayInProgress || isReplayPaused) return;

            const chatContainer = document.getElementById('chatContainer');
            const statusText = document.getElementById('statusText');

            if (currentMessageIndex >= medicalCaseData.conversation.length) {
                // Replay completed
                replayCompleted();
                return;
            }

            const message = medicalCaseData.conversation[currentMessageIndex];
            const messageElement = createMessageElement(message);
            chatContainer.appendChild(messageElement);

            // Smart scroll to bottom (only if auto-scroll is enabled)
            smartScrollToBottom(chatContainer);

            // Show typing effect for AI messages, immediate display for user messages
            if (message.type === 'ai') {
                typeMessage(messageElement.querySelector('.message-content'), message.content, () => {
                    currentMessageIndex++;
                    if (!isReplayPaused && replayInProgress) {
                        replayTimeout = setTimeout(showNextMessage, 1500);
                    }
                });
            } else {
                // For user messages, display content immediately
                const contentElement = messageElement.querySelector('.message-content');
                renderMessageContent(contentElement, message.content, true);
                currentMessageIndex++;
                if (!isReplayPaused && replayInProgress) {
                    replayTimeout = setTimeout(showNextMessage, 1000);
                }
            }
        }

        // Pause Replay Function
        function pauseReplay() {
            isReplayPaused = true;
            const statusText = document.getElementById('statusText');
            const pauseBtn = document.getElementById('pauseBtn');
            const resumeBtn = document.getElementById('resumeBtn');

            // Clear main replay timeout
            if (replayTimeout) {
                clearTimeout(replayTimeout);
            }

            // Stop typing effect
            if (currentTypingTimeout) {
                clearTimeout(currentTypingTimeout);
                currentTypingTimeout = null;
            }

            // Update UI
            if (statusText) statusText.textContent = 'Replay paused';
            if (pauseBtn) pauseBtn.classList.add('hidden');
            if (resumeBtn) resumeBtn.classList.remove('hidden');
        }

        // Resume Replay Function
        function resumeReplay() {
            if (!replayInProgress) return;

            isReplayPaused = false;
            const statusText = document.getElementById('statusText');
            const pauseBtn = document.getElementById('pauseBtn');
            const resumeBtn = document.getElementById('resumeBtn');

            // Update UI
            if (statusText) statusText.textContent = 'Replaying conversation...';
            if (pauseBtn) pauseBtn.classList.remove('hidden');
            if (resumeBtn) resumeBtn.classList.add('hidden');

            // Resume typing if there's an ongoing typing state
            if (currentTypingState) {
                resumeTyping();
            } else {
                // Continue with next message
                showNextMessage();
            }
        }

        // Stop Replay Function
        function stopReplay() {
            replayInProgress = false;
            isReplayPaused = false;
            currentMessageIndex = 0;

            // Clear main replay timeout
            if (replayTimeout) {
                clearTimeout(replayTimeout);
            }

            // Stop typing effect
            stopTyping();

            // Reset button visibility
            const replayBtn = document.getElementById('replayBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resumeBtn = document.getElementById('resumeBtn');

            if (replayBtn) replayBtn.classList.remove('hidden');
            if (pauseBtn) pauseBtn.classList.add('hidden');
            if (resumeBtn) resumeBtn.classList.add('hidden');
        }

        // Replay Completed Function
        function replayCompleted() {
            replayInProgress = false;
            isReplayPaused = false;

            const statusText = document.getElementById('statusText');
            const replayBtn = document.getElementById('replayBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resumeBtn = document.getElementById('resumeBtn');

            // Update UI
            if (statusText) statusText.textContent = 'Replay completed';
            if (replayBtn) replayBtn.classList.remove('hidden');
            if (pauseBtn) pauseBtn.classList.add('hidden');
            if (resumeBtn) resumeBtn.classList.add('hidden');
        }

        // Create Message Element
        function createMessageElement(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} px-1 sm:px-0`;

            const isUser = message.type === 'user';
            const bgColor = isUser ? 'bg-blue-500 text-white' : 'bg-white border';
            const alignment = isUser ? 'text-right' : 'text-left';

            messageDiv.innerHTML = `
                <div class="max-w-[85%] sm:max-w-3xl">
                    <div class="${bgColor} rounded-lg px-2 py-1.5 sm:px-3 sm:py-2 shadow-sm">
                        <div class="message-content ${alignment} text-xs sm:text-sm leading-relaxed ${isUser ? 'whitespace-pre-wrap' : ''}"></div>
                    </div>
                </div>
            `;

            return messageDiv;
        }

        // Render message content with format support (for non-typing scenarios)
        function renderMessageContent(element, content, isUser = false) {
            if (isUser) {
                // User messages display as plain text
                element.textContent = content;
            } else {
                // AI messages render with markdown support
                if (window.outputFormatPlugin && window.outputFormatPlugin.currentFormat === 'markdown') {
                    try {
                        const renderedContent = window.outputFormatPlugin.renderContent(content);
                        element.innerHTML = renderedContent;

                        // Apply syntax highlighting if available
                        if (typeof hljs !== 'undefined') {
                            element.querySelectorAll('pre code').forEach((block) => {
                                hljs.highlightElement(block);
                            });
                        }
                    } catch (error) {
                        console.error('Error rendering markdown:', error);
                        element.textContent = content; // Fallback to plain text
                    }
                } else {
                    element.textContent = content;
                }
            }
        }

        // Type Message Effect with Pause Support and Real-time Markdown Rendering
        let currentTypingTimeout;
        let currentTypingState = null;

        function typeMessage(element, text, callback) {
            // Start with empty element
            element.innerHTML = '';

            // Store the full text and typing state
            currentTypingState = {
                element: element,
                fullText: text,
                callback: callback,
                currentIndex: 0
            };

            typeCharSimple();
        }

        function typeCharSimple() {
            if (!currentTypingState || !replayInProgress || isReplayPaused) {
                return;
            }

            const { element, fullText, callback, currentIndex } = currentTypingState;

            if (currentIndex < fullText.length) {
                // Add next character
                const currentText = fullText.substring(0, currentIndex + 1);

                // Render the current text as markdown if needed
                if (window.outputFormatPlugin && window.outputFormatPlugin.currentFormat === 'markdown') {
                    try {
                        element.innerHTML = window.outputFormatPlugin.renderContent(currentText);
                    } catch (error) {
                        element.textContent = currentText;
                    }
                } else {
                    element.textContent = currentText;
                }

                currentTypingState.currentIndex++;

                // Smart scroll during typing to keep content visible
                const chatContainer = document.getElementById('chatContainer');
                if (chatContainer) {
                    smartScrollToBottom(chatContainer);
                }

                currentTypingTimeout = setTimeout(typeCharSimple, 20); // Typing speed
            } else {
                // Typing completed - apply final formatting
                if (window.outputFormatPlugin && window.outputFormatPlugin.currentFormat === 'markdown') {
                    try {
                        element.innerHTML = window.outputFormatPlugin.renderContent(fullText);
                    } catch (error) {
                        element.textContent = fullText;
                    }
                } else {
                    element.textContent = fullText;
                }

                // Apply syntax highlighting
                if (typeof hljs !== 'undefined') {
                    element.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });
                }

                currentTypingState = null;
                if (callback && replayInProgress && !isReplayPaused) {
                    callback();
                }
            }
        }

        function stopTyping() {
            if (currentTypingTimeout) {
                clearTimeout(currentTypingTimeout);
                currentTypingTimeout = null;
            }
            currentTypingState = null;
        }

        function resumeTyping() {
            if (currentTypingState && replayInProgress && !isReplayPaused) {
                typeCharSimple();
            }
        }

        // FAQ Data Loading and Accordion Functionality
        async function loadFAQData() {
            const faqContainer = document.getElementById('faq-container');

            try {
                const apiBase = getApiBase();
                const response = await fetch(`${apiBase}/ai-base/index/getConfigByKeyFromCache?configKey=project_faq&locale=en`);
                const result = await response.json();

                if (result.code === 0 && result.data && result.data.en) {
                    const faqData = result.data.en;
                    renderFAQItems(faqData);
                } else {
                    throw new Error('Failed to load FAQ data');
                }
            } catch (error) {
                console.error('Error loading FAQ data:', error);
                // Fallback to default FAQ items
                renderDefaultFAQ();
            }
        }

        function renderFAQItems(faqData) {
            const faqContainer = document.getElementById('faq-container');
            faqContainer.innerHTML = '';
            faqData.forEach((item, index) => {
                const faqItem = document.createElement('div');
                faqItem.className = 'faq-item bg-background border border-border rounded-lg overflow-hidden';
                faqItem.innerHTML = `
                    <button class="faq-trigger w-full px-4 sm:px-6 py-3 sm:py-4 text-left flex items-center justify-between hover:bg-accent/50 transition-colors">
                        <span class="font-medium text-foreground text-sm sm:text-base pr-2">${item.Q}</span>
                        <svg class="faq-icon w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground transition-transform flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-content hidden px-4 sm:px-6 pb-3 sm:pb-4">
                        <p class="text-muted-foreground leading-relaxed text-sm sm:text-base">${item.A}</p>
                    </div>
                `;
                faqContainer.appendChild(faqItem);
            });

            // Initialize accordion functionality after rendering
            initializeFAQAccordion();
        }

        function renderDefaultFAQ() {
            const defaultFAQ = [
                {
                    Q: "What services can you provide?",
                    A: "We offer a tool specialized in medical research design, dedicated to providing researchers with professional AI-powered assistant services, and promoting innovation and development in medical science."
                },
                {
                    Q: "How do I use the platform?",
                    A: "After registration and login, you can directly enter your requirements in the main input box, or upload related materials using the 'Upload File' function. The system will process the information based on your input."
                },
                {
                    Q: "Can I use it for free?",
                    A: "The Base version is currently free to use. Simply register to start using it immediately and experience the core features."
                }
            ];
            renderFAQItems(defaultFAQ);
        }

        function initializeFAQAccordion() {
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                const trigger = item.querySelector('.faq-trigger');
                const content = item.querySelector('.faq-content');
                const icon = item.querySelector('.faq-icon');

                if (trigger && content && icon) {
                    trigger.addEventListener('click', () => {
                        const isOpen = !content.classList.contains('hidden');

                        // Close all other FAQ items
                        faqItems.forEach(otherItem => {
                            if (otherItem !== item) {
                                const otherContent = otherItem.querySelector('.faq-content');
                                const otherIcon = otherItem.querySelector('.faq-icon');
                                if (otherContent && otherIcon) {
                                    otherContent.classList.add('hidden');
                                    otherIcon.style.transform = 'rotate(0deg)';
                                }
                            }
                        });

                        // Toggle current item
                        if (isOpen) {
                            content.classList.add('hidden');
                            icon.style.transform = 'rotate(0deg)';
                        } else {
                            content.classList.remove('hidden');
                            icon.style.transform = 'rotate(180deg)';
                        }
                    });
                }
            });
        }

        // Intersection Observer for Animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });

        // Function to load case data from external JSON or replace with your data
        function loadCaseData() {
            // TO REPLACE WITH YOUR JSON DATA:
            // 1. Replace the return object below with your actual JSON data
            // 2. Ensure it has the structure: { "elavax-base": [...], "elavax-pro": [...], "novax-base": [...], "novax-pro": [...] }
            // 3. Each case object should have: { "id": "", "title": "", "desc": "", "icon": "", "lang": "" }
            // 4. Only novax-base and novax-pro cases will be displayed (elavax cases are excluded)

            return {
                "elavax-base":[{"icon":"https://img.medsci.cn/20241225/1735098448003_8538692.jpg","id":"1a2b3c4d5e6f","lang":"en","title":"Endocrinology Frontiers","desc":"Endocrinology Frontiers manuscript review ensures rigorous evaluation by experts to maintain high scientific standards and advance endocrine research."},{"icon":"https://img.medsci.cn/202401012/1728697600406_8538692.png","id":"3394e359c","lang":"en","title":"Start Now","desc":"By analyzing key information such as journal scope, impact factor, and review cycles, this study provides precise journal recommendations for researchers to facilitate effective publication of academic achievements."},{"icon":"https://img.medsci.cn/20240518/1716039928771_5579292.jpg","id":"0505e33e7","lang":"en","title":"Hepatology News","desc":"Hepatology News provides timely updates and insights on the latest research, clinical advances, and emerging trends in liver disease and hepatology."}],"elavax-pro":[{"icon":"https://img.medsci.cn/20240518/1716039928771_5579292.jpg","id":"7b28e4138","lang":"en","title":"Evaluating Manuscripts Against BMC Cancer Journal Standards","desc":"This study systematically analyzes the submission requirements of BMC Cancer journal, including its publication standards, formatting guidelines, and academic quality criteria, providing authors with pre-assessment services to determine whether their manuscripts meet the journal's acceptance standards."},{"icon":"https://img.medsci.cn/20220719/1658202183571_5552845.jpg","id":"f79de405f","lang":"en","title":"Conducting Peer Review Work","desc":"Based on the workflow and core competencies of academic peer review, including review preparation, content analysis, quality evaluation, and feedback provision, this study provides practical operational guidance and professional development recommendations for reviewers."},{"icon":"https://img.medsci.cn/Random/patient-meeting-a-doctor-PAYSRMZ.jpg","id":"8758e39cf","lang":"en","title":"Inquiry into Standard Evaluation Appendix Content for Nature Journal","desc":"Based on the standard evaluation appendices required for Nature journal submissions, this study systematically reviews their content requirements and formatting guidelines, providing authors with detailed preparation guidelines to ensure assessment materials meet journal requirements."},{"icon":"https://img.medsci.cn/Random/atrial-fibrillation-s7-photo-of-blood-pressure-monitor.jpg","id":"f4aae3777","lang":"en","title":"Evaluation of Article Revision Suggestions","desc":"Conducting comprehensive evaluation from perspectives of reasonableness, feasibility, and effectiveness to provide scientific basis for authors to optimize their revision strategies."}],"novax-pro":[{"icon":"https://img.medsci.cn/20241108/1731046030949_8538692.jpg","id":"9a4fe1978","lang":"en","title":"Based on brief information","desc":"Based on brief information, a concise research plan is formulated to guide subsequent investigation and innovation."},{"icon":"https://img.medsci.cn/20240509/1715223995977_5579292.jpg","id":"e6d7e1720","lang":"en","title":"Research method transfer to user’s study area","desc":"Transferring proven research methods to the user's study area to enhance scientific rigor and accelerate discovery."},{"icon":"https://img.medsci.cn/Random/doctor-signing-contract-V6EFLDM.jpg","id":"8ce0e14eb","lang":"en","title":"Based on investigation report","desc":"Based on the investigation report, a strategic research plan is developed to address key findings and guide future studies."}],"novax-base":[{"icon":"https://img.medsci.cn/Random/doctor-signing-contract-V6EFLDM.jpg","id":"3b4c5d6e7f8a","lang":"en","title":"Simple input with follow-up questions","desc":"A simple initial input combined with iterative follow-up questions enables refined user needs modeling and facilitates intelligent, personalized research planning and database-driven insights."},{"icon":"https://img.medsci.cn/20221109/1668024406447_4754896.jpeg","id":"8tbfdemwy3","lang":"en","title":"Research following published literature","desc":"Builds upon established knowledge by systematically analyzing and extending insights from published literature to guide robust, evidence-based research development."},{"icon":"https://img.medsci.cn/Random/health-insurance-PK235DP.jpg","id":"su37k8obxp","lang":"en","title":"Clinical study with follow-up questions","desc":"Initiates a clinical study framework with adaptive follow-up questions to dynamically refine patient profiling, improve data accuracy, and support personalized medical research."}]
            }
        }

        // Static cases are now rendered in HTML, no dynamic generation needed





        // Static case count update function
        function updateCaseCount() {
            const countElement = document.getElementById('case-count');
            if (!countElement) return;

            const visibleCases = document.querySelectorAll('.case-card:not([style*="display: none"])');
            const currentFilter = getCurrentFilter();

            let filterText = '';
            switch(currentFilter) {
                case 'all':
                    filterText = 'All Cases';
                    break;
                case 'novax-base':
                    filterText = 'novax-base Cases';
                    break;
                case 'novax-pro':
                    filterText = 'novax-pro Cases';
                    break;
                default:
                    filterText = 'Cases';
            }

            countElement.textContent = ``;
        }

        function getCurrentFilter() {
            const activeButton = document.querySelector('.filter-btn.active');
            return activeButton ? activeButton.getAttribute('data-filter') : 'all';
        }

        function getCurrentSearchTerm() {
            const searchInput = document.getElementById('case-search');
            return searchInput ? searchInput.value : '';
        }

        // Static case filtering function
        function filterStaticCases() {
            const filter = getCurrentFilter();
            const searchTerm = getCurrentSearchTerm().toLowerCase();
            const allCases = document.querySelectorAll('.case-card');

            let visibleCount = 0;

            allCases.forEach(caseCard => {
                const category = caseCard.getAttribute('data-category');
                const title = caseCard.querySelector('h3').textContent.toLowerCase();
                const desc = caseCard.querySelector('p').textContent.toLowerCase();

                // Check filter match
                let filterMatch = false;
                if (filter === 'all') {
                    filterMatch = category === 'novax-base' || category === 'novax-pro';
                } else {
                    filterMatch = category === filter;
                }

                // Check search match
                let searchMatch = true;
                if (searchTerm.trim()) {
                    searchMatch = title.includes(searchTerm) || desc.includes(searchTerm);
                }

                // Show/hide case
                if (filterMatch && searchMatch) {
                    caseCard.style.display = '';
                    visibleCount++;
                } else {
                    caseCard.style.display = 'none';
                }
            });

            // Update count
            updateCaseCount();

            // Show empty message if no cases visible
            const container = document.getElementById('cases-container');
            let emptyMessage = container.querySelector('.empty-message');

            if (visibleCount === 0) {
                if (!emptyMessage) {
                    emptyMessage = document.createElement('div');
                    emptyMessage.className = 'empty-message col-span-full text-center py-12';
                    emptyMessage.innerHTML = `
                        <div class="text-6xl mb-4">🔍</div>
                        <h3 class="text-lg font-medium mb-2">No Cases Found</h3>
                        <p class="text-muted-foreground">No cases match your current filter and search criteria</p>
                    `;
                    container.appendChild(emptyMessage);
                }
                emptyMessage.style.display = '';
            } else {
                if (emptyMessage) {
                    emptyMessage.style.display = 'none';
                }
            }
        }

        function initializeCaseFiltering() {
            console.log('Initializing static case filtering...');

            const filterButtons = document.querySelectorAll('.filter-btn');
            const periodButtons = document.querySelectorAll('.period-btn');
            const searchInput = document.getElementById('case-search');

            // Period button event listeners
            periodButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all period buttons
                    periodButtons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    button.classList.add('active');

                    // Get selected period
                    const selectedPeriod = button.getAttribute('data-period');
                    console.log('Selected subscription period:', selectedPeriod);

                    // Update pricing display based on selected period
                    updateSubscriptionPeriod(selectedPeriod);
                });
            });

            // Filter button event listeners
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    button.classList.add('active');

                    // Filter static cases
                    filterStaticCases();
                });
            });

            // Search input event listener
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', () => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        filterStaticCases();
                    }, 300); // Debounce search
                });
            }

            // Initial filter
            filterStaticCases();
        }

        // Subscription data based on your API structure
        const subscriptionPlans = {
            // Base应用订阅计划 (只有免费版本)
            base: {
                free: {
                    online: 1,
                    packageKey: "====placeholder====",
                    type: "免费",
                    periodType: "MONTH",
                    monthNum: 1,
                    oldPrice: null,
                    feePrice: 0.00,
                    coinType: "美元",
                    priceId: "免费订阅",
                    num: null
                }
            },
            // Pro应用订阅计划 (包月、包季、包年)
            pro: {
                monthly: {
                    online: 1,
                    packageKey: "====placeholder====",
                    type: "Monthly Subscription",
                    periodType: "MONTH",
                    monthNum: 1,
                    oldPrice: null,
                    feePrice: 19.00,
                    coinType: "USD",
                    priceId: "price_1Rq8dkJZcnPg0hIN3yp8uKRX",
                    num: null
                },
                quarterly: {
                    online: 1,
                    packageKey: "====placeholder====",
                    type: "Quarterly Subscription",
                    periodType: "MONTH",
                    monthNum: 3,
                    oldPrice: null,
                    feePrice: 39.00,
                    coinType: "USD",
                    priceId: "price_1Rq8dlJZcnPg0hINxi1G8Rzv",
                    num: null
                }
            }
        };

        // Function to handle subscription period changes
        function updateSubscriptionPeriod(period) {
            console.log('Updating subscription period to:', period);

            // Hide all period cards first
            const allPeriodCards = document.querySelectorAll('.period-card');
            allPeriodCards.forEach(card => {
                card.style.display = 'none';
            });

            // Show Base Free card (always visible)
            const baseFreeCard = document.querySelector('.base-free-card');
            if (baseFreeCard) {
                baseFreeCard.style.display = 'flex';
            }

            // Show cards for the selected period
            const selectedCards = document.querySelectorAll(`.period-card[data-period="${period}"]`);
            selectedCards.forEach(card => {
                card.style.display = 'flex';
            });

            console.log(`Showing ${selectedCards.length} cards for period: ${period}`);
        }

        // Handle subscription button clicks
        function initializeSubscriptionButtons() {
            const subscribeButtons = document.querySelectorAll('.subscribe-btn');

            subscribeButtons.forEach(button => {
                button.addEventListener('click',async (e) => {
                    e.preventDefault();

                    const app = button.getAttribute('data-app'); // base or pro
                    const planKey = button.getAttribute('data-plan-key'); // free, monthly, quarterly, yearly
                    const planType = button.getAttribute('data-plan-type');
                    const price = button.getAttribute('data-price');
                    const period = button.getAttribute('data-period');
                    const months = button.getAttribute('data-months');
                    const priceId = button.getAttribute('data-price-id');

                    // Get subscription data from predefined plans
                    let subscriptionData;
                    console.log('===app', app, 'planKey', subscriptionPlans);
                    if (app === 'base' && planKey === 'free') {
                        subscriptionData = subscriptionPlans.base.free;
                    } else if (app === 'pro' && planKey === 'monthly') {
                        subscriptionData = subscriptionPlans.pro.monthly;
                    } else if (app === 'pro' && planKey === 'quarterly') {
                        subscriptionData = subscriptionPlans.pro.quarterly;
                    }
                    const currentApp = getCurrentApp();
                    const appDetail = await getAppDetail(app=='base'?'novax-base':'novax-pro');
                    let data = {"appUuid":appDetail.appUuid,"priceId":subscriptionData.priceId,"monthNum":subscriptionData.monthNum,"packageKey":subscriptionData.packageKey,"packageType":subscriptionData.type};
                    if (subscriptionData) {

                        // Call subscription API
                        handleSubscription(data, app, planKey);
                    } else {
                        console.error('Invalid subscription plan:', { app, planKey });
                    }
                });
            });
        }

        // Check if user is logged in
        function checkUserLogin() {
            const token = getUserToken();
            return !!(token && token !== 'null' && token !== 'undefined');
        }

        // Redirect to login page
        function redirectToLogin() {
            const loginUrl = '/auth/login';
            window.location.href = loginUrl;
        }

        // Handle subscription API call
        async function handleSubscription(subscriptionData, app, planKey) {
            try {
                // Check if user is logged in first
                if (!checkUserLogin()) {
                    sessionStorage.setItem('redirectUrl', window.location.href);
                    location.href = 'https://medxy.ai/en/login'
                    return;
                }

                console.log(`Calling ${app.toUpperCase()} ${planKey.toUpperCase()} subscription API with data:`, subscriptionData);

                const apiBase = getApiBase();
                const response = await fetch(`${apiBase}/ai-base/appUser/createSubscription`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getUserToken()}`
                    },
                    body: JSON.stringify(subscriptionData)
                });

                if (response.ok) {
                    const result = await response.json();
                    if(app == 'pro'){
                        location.href = result.data
                    }
                    if(app == 'base'){
                        location.href = 'https://ai.medsci.cn/en/novax-base'
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('Subscription failed:', error);
                showMessage(`${app.toUpperCase()} ${planKey} subscription failed. Please try again.`, 'error');
            }
        }

        // Research Proposals Quick Selection Functionality
        function initializeResearchProposals() {
            const proposalOptions = document.querySelectorAll('.proposal-option');
            const categoryTags = document.querySelectorAll('.category-tag');
            const closeButton = document.querySelector('.close-proposals');
            const proposalsContainer = document.querySelector('.research-proposals-container');
            const chatTextarea = document.querySelector('textarea[placeholder*="Enter your research"]');



            // New elements for the redesigned layout
            const proposalListContainer = document.querySelector('.proposal-list-container');
            const currentCategoryTitle = document.getElementById('current-category-title');
            const currentCategoryIcon = document.getElementById('current-category-icon');

            // Proposal option clicking
            proposalOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const text = option.dataset.text;
                    if (chatTextarea && text) {
                        // Add click animation
                        const item = option.querySelector('.proposal-item');
                        item.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            item.style.transform = '';
                        }, 150);
                        // Fill textarea with selected text
                        chatTextarea.value = text;
                        chatTextarea.focus();

                        // Trigger input event to update submit button state
                        const inputEvent = new Event('input', { bubbles: true });
                        chatTextarea.dispatchEvent(inputEvent);

                        // Auto-resize textarea if needed
                        chatTextarea.style.height = 'auto';
                        chatTextarea.style.height = chatTextarea.scrollHeight + 'px';

                        // Hide proposal list container after selection
                        if (proposalListContainer) {
                            proposalListContainer.style.animation = 'fadeOut 0.3s ease-out';
                            setTimeout(() => {
                                proposalListContainer.classList.add('hidden');
                            }, 300);
                        }

                        // Remove active state from all category tags
                        categoryTags.forEach(t => t.classList.remove('active'));

                        // Smooth scroll to textarea
                        chatTextarea.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                    }
                });
            });

            // Category tag functionality
            categoryTags.forEach(tag => {
                tag.addEventListener('click', () => {
                    // Remove active class from all tags
                    categoryTags.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tag
                    tag.classList.add('active');

                    const category = tag.dataset.category;
                    const categoryNames = {
                        'general': 'General Use',
                        'design': 'Research Design',
                        'planning': 'Research Planning',
                        'modeling': 'Data Modeling'
                    };

                    const categoryIcons = {
                        'general': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4"></path>
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v4"></path>
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4"></path>`,
                        'design': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>`,
                        'planning': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>`,
                        'modeling': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>`
                    };

                    // Update category title and icon
                    const categoryName = categoryNames[category] || 'General Use';
                    const categoryIcon = categoryIcons[category] || categoryIcons['general'];

                    if (currentCategoryTitle) currentCategoryTitle.textContent = categoryName;
                    if (currentCategoryIcon) currentCategoryIcon.innerHTML = categoryIcon;

                    // Show proposal list container
                    if (proposalListContainer) {
                        proposalListContainer.classList.remove('hidden');
                        proposalListContainer.style.animation = 'fadeIn 0.3s ease-out';
                    }

                    // Hide all proposal options
                    proposalOptions.forEach(option => {
                        option.classList.add('hidden');
                    });

                    // Show relevant proposal options
                    const relevantOptions = document.querySelectorAll(`.category-${category}`);
                    relevantOptions.forEach((option, index) => {
                        option.classList.remove('hidden');
                        // Add staggered animation
                        option.style.animation = `fadeIn 0.4s ease-out ${index * 0.05}s both`;
                    });
                });
            });

            // Close button functionality
            if (closeButton) {
                closeButton.addEventListener('click', () => {
                    // Hide proposal list container only
                    if (proposalListContainer) {
                        proposalListContainer.style.animation = 'fadeOut 0.3s ease-out';
                        setTimeout(() => {
                            proposalListContainer.classList.add('hidden');
                        }, 300);
                    }

                    // Remove active state from all category tags (keep tags visible)
                    categoryTags.forEach(t => t.classList.remove('active'));
                });
            }

            // Initialize: Hide all proposal options by default
            proposalOptions.forEach(option => {
                option.classList.add('hidden');
            });
        }

        // Initialize Get Started Button
        function initializeGetStartedButton() {
            const getStartedBtn = document.getElementById('get-started-btn');

            if (getStartedBtn) {
                getStartedBtn.addEventListener('click', () => {
                    // Check if user is logged in
                    if (checkUserLogin()) {
                        // User is logged in, redirect to novax-base
                        location.href = 'https://medxy.ai/en/novax-base';
                    } else {
                        // User is not logged in, redirect to login page
                        location.href = 'https://medxy.ai/en/login';
                    }
                });
            }
        }

        // Check Pro subscription status and update button
        async function checkProSubscriptionStatus() {
            try {
                const appDetail = await getAppDetail('novax-pro');

                // Check if user has active subscription (state 1 or 3)
                if (appDetail && appDetail?.appUser && (appDetail?.appUser?.status == 1 || appDetail?.appUser?.status == 3)) {
                    // Find Pro Monthly subscription button
                    const proMonthlyButton = document.querySelector('.pro-monthly-card .subscribe-btn');

                    if (proMonthlyButton) {
                        // Change button text to "Get Started"
                        proMonthlyButton.textContent = 'Get Started';

                        // Remove existing click handlers and add new one
                        proMonthlyButton.removeAttribute('data-app');
                        proMonthlyButton.removeAttribute('data-plan');
                        proMonthlyButton.removeAttribute('data-plan-type');
                        proMonthlyButton.removeAttribute('data-price');
                        proMonthlyButton.removeAttribute('data-period');
                        proMonthlyButton.removeAttribute('data-months');
                        proMonthlyButton.removeAttribute('data-price-id');

                        // Add new click handler for direct access
                        proMonthlyButton.onclick = function(e) {
                            e.preventDefault();
                            location.href = 'https://medxy.ai/en/novax-pro';
                        };

                        // Add visual indicator that this is an active subscription
                        proMonthlyButton.classList.add('bg-green-600', 'hover:bg-green-700');
                        proMonthlyButton.classList.remove('bg-primary', 'hover:bg-primary/90');
                    }
                }
            } catch (error) {
                console.error('Error checking Pro subscription status:', error);
            }
        }

        // Add fadeOut animation and video container styles to CSS if not already present
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.95);
                }
            }

            #videoContainer {
                transition: all 0.3s ease;
            }

            #videoContainer:hover {
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            .medical-ai-case {
                animation: slideIn 0.5s ease-out;
            }

            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize enhancements when DOM is loaded
        document.addEventListener('DOMContentLoaded', async () => {
            // Show loading and initialize tasks
            showLoading();

            // Define loading tasks
            addLoadingTask('UI Components');
            addLoadingTask('Navigation');
            addLoadingTask('Selectors');
            addLoadingTask('Demo Video');
            addLoadingTask('Research Proposals');
            addLoadingTask('Get Started Button');
            addLoadingTask('FAQ Data');
            addLoadingTask('App Details');
            addLoadingTask('Subscription Status');
            addLoadingTask('Scene Configuration');

            try {
                // Initialize UI components
                enhanceFlickeringGrid();
                updateActiveNavItem();
                completeLoadingTask('UI Components');

                // Initialize navigation
                completeLoadingTask('Navigation');

                // Initialize selectors
                initializeSelectors();
                completeLoadingTask('Selectors');

                // Initialize demo video
                initializeDemoVideo();
                completeLoadingTask('Demo Video');

                // Initialize research proposals
                initializeResearchProposals();
                completeLoadingTask('Research Proposals');

                // Initialize get started button
                initializeGetStartedButton();
                completeLoadingTask('Get Started Button');

                // Load FAQ data
                await loadFAQData();
                completeLoadingTask('FAQ Data');

                // Load app details
                await getAppDetail('novax-base');
                completeLoadingTask('App Details');

                // Check subscription status
                await checkProSubscriptionStatus();
                completeLoadingTask('Subscription Status');

                // Load scene configuration
                await loadSceneConfig();
                completeLoadingTask('Scene Configuration');

            } catch (error) {
                console.error('Initialization error:', error);
                updateLoadingProgress('Initialization failed. Retrying...');
                // Still hide loading after error
                setTimeout(hideLoading, 2000);
            }

            // 初始化场景选择器显示
            const selectedPromptSpan = document.getElementById('selected-prompt');
            if (selectedPromptSpan && suggestedQuestions.length > 0) {
                selectedPromptSpan.textContent = suggestedQuestions[0].title;
            } else if (selectedPromptSpan) {
                selectedPromptSpan.textContent = 'Select Scene';
            }
            initializeContactModal(); // Initialize contact modal
            initializeMobileMenu(); // Initialize mobile menu
            initializeSmoothScroll(); // Initialize smooth scroll for anchor links
            initializeCaseFiltering(); // Initialize case filtering system
            initializeSubscriptionButtons(); // Initialize subscription buttons

            // Set initial theme icon
            const isDark = html.classList.contains('dark');
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                const icon = themeToggle.querySelector('svg');
                if (isDark) {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>';
                }
            }

            // Add scroll listener for active nav item updates
            window.addEventListener('scroll', updateActiveNavItem);

            // Add resize listener to update scroll behavior on screen size change
            window.addEventListener('resize', () => {
                // Debounce resize events
                clearTimeout(window.resizeTimeout);
                window.resizeTimeout = setTimeout(() => {
                    updateActiveNavItem();
                }, 100);
            });
        });

        // Add hover effects to buttons
        document.querySelectorAll('.btn-primary, .btn-secondary').forEach(button => {
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-2px)';
                button.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
            });

            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0)';
                button.style.boxShadow = '';
            });
        });

        // Calculate actual navbar height dynamically
        function getNavbarHeight() {
            const navbar = document.getElementById('navbar');
            const navbarContainer = document.getElementById('navbar-container');

            if (navbar && navbarContainer) {
                const navbarRect = navbar.getBoundingClientRect();
                const containerRect = navbarContainer.getBoundingClientRect();

                // Total height including margins and padding
                const totalHeight = navbarRect.height + 32; // 32px for top-4 margin and some buffer
                return totalHeight;
            }

            // Fallback values based on screen size
            if (window.innerWidth <= 480) {
                return 80;
            } else if (window.innerWidth <= 640) {
                return 90;
            } else if (window.innerWidth <= 768) {
                return 100;
            } else {
                return 140;
            }
        }

        // Smooth Scroll with Offset for Anchor Links
        function initializeSmoothScroll() {
            // Handle all navigation links (desktop and mobile)
            const allNavLinks = document.querySelectorAll('a[href^="#"]');

            allNavLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    const targetId = link.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Use dynamic navbar height calculation
                        const offset = getNavbarHeight();

                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - offset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // Mobile Menu Functionality
        function initializeMobileMenu() {
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
            const themeToggleMobile = document.getElementById('theme-toggle-mobile');
            const themeToggleDesktop = document.getElementById('theme-toggle');

            let isMenuOpen = false;

            // Toggle mobile menu
            function toggleMobileMenu() {
                if (isMenuOpen) {
                    closeMobileMenu();
                } else {
                    openMobileMenu();
                }
            }

            function openMobileMenu() {
                mobileMenu.classList.remove('hidden', 'closing');
                mobileMenuToggle.classList.add('active');
                isMenuOpen = true;
            }

            function closeMobileMenu() {
                mobileMenu.classList.add('closing');
                mobileMenuToggle.classList.remove('active');
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                    mobileMenu.classList.remove('closing');
                }, 200);
                isMenuOpen = false;
            }

            // Event listeners
            mobileMenuToggle.addEventListener('click', toggleMobileMenu);

            // Close menu when clicking on nav links
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', () => {
                    closeMobileMenu();
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (isMenuOpen && !mobileMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                    closeMobileMenu();
                }
            });

            // Sync theme toggles
            function syncThemeToggle() {
                const isDark = document.documentElement.classList.contains('dark');
                const iconPath = isDark
                    ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>'
                    : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>';

                if (themeToggleMobile) {
                    themeToggleMobile.querySelector('svg').innerHTML = iconPath;
                }
                if (themeToggleDesktop) {
                    themeToggleDesktop.querySelector('svg').innerHTML = iconPath;
                }
            }

            // Theme toggle for mobile
            if (themeToggleMobile) {
                themeToggleMobile.addEventListener('click', () => {
                    document.documentElement.classList.toggle('dark');
                    localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
                    syncThemeToggle();
                });
            }

            // Initial sync
            syncThemeToggle();

            // Close menu on window resize to desktop size
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 768 && isMenuOpen) {
                    closeMobileMenu();
                }
            });
        }

        // Contact Modal Functionality (Exact Feedback Implementation)
        function initializeContactModal() {
            const modal = document.getElementById('contact-modal');
            const modalContainer = modal.querySelector('.modal-container');
            const trigger = document.getElementById('contact-modal-trigger');
            const closeBtn = document.getElementById('contact-modal-close');
            const cancelBtn = document.getElementById('contact-cancel');
            const form = document.getElementById('contact-form');
            const submitBtn = document.getElementById('contact-submit');
            const submitText = document.getElementById('submit-text');
            const submitSpinner = document.getElementById('submit-spinner');
            const contentTextarea = document.getElementById('mgContent');
            const charCount = document.getElementById('char-count');

            // File upload state (exactly like FeedbackModal)
            const uploadedImages = [];
            const maxFiles = 3;
            const maxFileSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
            let uploading = false;

            // Get upload elements
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');
            const uploadedImagesContainer = document.getElementById('uploaded-images');
            const maxImagesMessage = document.getElementById('max-images-message');
            const uploadProgress = document.getElementById('upload-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const imagePreviewModal = document.getElementById('image-preview-modal');
            const previewImage = document.getElementById('preview-image');
            const closePreview = document.getElementById('close-preview');

            // Console errors collection (exactly like FeedbackModal)
            const consoleErrors = [];
            let errorListenerAdded = false;

            // Add console error listeners (exactly like FeedbackModal)
            if (!errorListenerAdded) {
                window.addEventListener('error', (event) => {
                    consoleErrors.push({
                        message: event.message,
                        source: event.filename,
                        line: event.lineno,
                        column: event.colno,
                        timestamp: Date.now(),
                        type: 'error'
                    });
                });

                window.addEventListener('unhandledrejection', (event) => {
                    consoleErrors.push({
                        message: event.reason?.toString() || 'Unhandled promise rejection',
                        timestamp: Date.now(),
                        type: 'unhandledrejection'
                    });
                });

                errorListenerAdded = true;
            }

            // Character count functionality
            if (contentTextarea && charCount) {
                contentTextarea.addEventListener('input', () => {
                    const length = contentTextarea.value.length;
                    charCount.textContent = length;
                });
            }



            // File validation (exactly like FeedbackModal)
            function validateFiles(files) {
                const errors = [];
                const validFiles = [];

                // Check total file count
                if (uploadedImages.length + files.length > maxFiles) {
                    errors.push(`Cannot upload more than ${maxFiles} files. You can upload ${maxFiles - uploadedImages.length} more files.`);
                    return { validFiles: [], errors };
                }

                // Validate each file
                for (const file of files) {
                    // Check file type
                    if (!allowedTypes.includes(file.type)) {
                        errors.push(`File "${file.name}" is not a supported image format. Please use PNG, JPG, or GIF.`);
                        continue;
                    }

                    // Check file size
                    if (file.size > maxFileSize) {
                        const sizeMB = (file.size / (1024 * 1024)).toFixed(1);
                        errors.push(`File "${file.name}" is too large (${sizeMB}MB). Maximum size is 10MB.`);
                        continue;
                    }

                    validFiles.push(file);
                }

                return { validFiles, errors };
            }

            // Real image upload (like FeedbackModal's xaiApi.uploadImage)
            async function uploadImageToApi(file) {
                const formData = new FormData();
                formData.append('file', file);

                const apiBase = getApiBase();
                const response = await fetch(`${apiBase}/ai-base/openapi/upload-picture`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`Upload failed: HTTP ${response.status}`);
                }

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Upload API returned non-JSON response. Please check the API endpoint.');
                }

                const result = await response.json();

                // Check if upload was successful
                if (result.status === 200 && result.data) {
                    return {
                        url: result.data.url || result.data,
                        name: file.name,
                        size: file.size
                    };
                } else {
                    throw new Error(result.message || 'Upload failed');
                }
            }


            // Get default email (exactly like FeedbackModal)
            function getDefaultEmail() {
                const hostname = window.location.hostname;
                if (hostname === 'ai.medon.com.cn') {
                    return '<EMAIL>';
                } else if (hostname === 'ai.medsci.cn') {
                    return '<EMAIL>';
                }
                return '<EMAIL>';
            }



            // Fallback: Convert file to base64 for local preview
            function convertFileToBase64(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        resolve({
                            url: e.target.result,
                            name: file.name,
                            size: file.size,
                            isBase64: true // Mark as base64 for later handling
                        });
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            }

            // Handle image upload (exactly like FeedbackModal)
            async function handleImageUpload(files) {
                const { validFiles, errors } = validateFiles(files);

                // Show errors
                if (errors.length > 0) {
                    errors.forEach(error => showMessage(error, 'error'));
                    return;
                }

                if (validFiles.length === 0) return;

                uploading = true;
                updateUploadAreaState();
                showUploadProgress();

                try {
                    // Try to upload to API first, fallback to base64 if failed
                    const uploadResults = [];

                    for (const file of validFiles) {
                        try {
                            // Try API upload first
                            const result = await uploadImageToApi(file);
                            uploadResults.push(result);
                        } catch (apiError) {
                            console.warn('API upload failed, using base64 fallback:', apiError.message);
                            // Fallback to base64 for preview
                            const base64Result = await convertFileToBase64(file);
                            uploadResults.push(base64Result);
                        }
                    }

                    uploadedImages.push(...uploadResults);
                    updateUploadedImagesDisplay();

                    const apiUploads = uploadResults.filter(r => !r.isBase64).length;
                    const base64Uploads = uploadResults.filter(r => r.isBase64).length;

                    if (apiUploads > 0 && base64Uploads === 0) {
                        // showMessage(`Successfully uploaded ${validFiles.length} file(s)`, 'success');
                    } else if (base64Uploads > 0) {
                        // showMessage(`${validFiles.length} file(s) added for preview (API upload unavailable)`, 'warning');
                    }
                } catch (error) {
                    console.error('Image upload failed:', error);
                    showMessage(`Upload failed: ${error.message}`, 'error');
                } finally {
                    uploading = false;
                    hideUploadProgress();
                    updateUploadAreaState();
                }
            }

            // Remove uploaded image (exactly like FeedbackModal)
            function handleImageRemove(url) {
                const index = uploadedImages.findIndex(img => img.url === url);
                if (index > -1) {
                    uploadedImages.splice(index, 1);
                    updateUploadedImagesDisplay();
                }
            }

            // Update uploaded images display (like FeedbackModal's grid layout)
            function updateUploadedImagesDisplay() {
                if (uploadedImages.length === 0) {
                    uploadedImagesContainer.style.display = 'none';
                    maxImagesMessage.style.display = 'none';
                    return;
                }

                uploadedImagesContainer.style.display = 'grid';
                uploadedImagesContainer.innerHTML = '';

                uploadedImages.forEach((img, index) => {
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'relative group';
                    imageDiv.innerHTML = `
                        <img
                            src="${img.url}"
                            alt="Uploaded image ${index + 1}"
                            class="w-full h-24 object-cover rounded border cursor-pointer"
                            onclick="showImagePreview('${img.url}')"
                        />
                        <button
                            onclick="handleImageRemove('${img.url}')"
                            class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity text-xs hover:bg-red-600"
                        >
                            ×
                        </button>
                    `;
                    uploadedImagesContainer.appendChild(imageDiv);
                });

                // Show max images message
                if (uploadedImages.length >= maxFiles) {
                    maxImagesMessage.style.display = 'block';
                } else {
                    maxImagesMessage.style.display = 'none';
                }
            }

            // Format feedback content (exactly like FeedbackModal)
            function formatFeedbackContent(userContent) {
                // Collect system info
                const systemInfo = [
                    `Browser Info: ${navigator.appName} ${navigator.appVersion}, ${navigator.platform}`,
                ];

                // Add console error info
                if (consoleErrors.length > 0) {
                    const errorMessages = consoleErrors
                        .map(err => `${err.type}: ${err.message}${err.source ? ` at ${err.source}:${err.line}:${err.column}` : ''}`)
                        .join('\n');
                    systemInfo.push(`console err: ${errorMessages}`);
                } else {
                    systemInfo.push('console err: No errors');
                }

                // User info
                const userInfoData = [
                    `User ID: unknown`,
                ];

                // Page info
                const pageInfo = [
                    `Page URL: ${window.location.href}`,
                    `Page Title: ${document.title}`,
                ];

                // Combine all info (exactly like FeedbackModal)
                const allInfo = [
                    `User Input Content: ${userContent}`,
                    '',
                    `User Information:`,
                    ...userInfoData,
                    '',
                    `System Information:`,
                    ...systemInfo,
                    '',
                    `Page Information:`,
                    ...pageInfo,
                ];

                return allInfo.join('\n');
            }



            // Drag and drop handlers (exactly like FeedbackModal)
            function handleDragEnter(e) {
                e.preventDefault();
                e.stopPropagation();
                if (uploading || uploadedImages.length >= maxFiles) return;

                uploadArea.classList.add('border-blue-400', 'bg-blue-50');
                uploadArea.classList.remove('border-gray-300');
            }

            function handleDragOver(e) {
                e.preventDefault();
                e.stopPropagation();
                if (uploading || uploadedImages.length >= maxFiles) return;

                e.dataTransfer.dropEffect = 'copy';
            }

            function handleDragLeave(e) {
                e.preventDefault();
                e.stopPropagation();
                if (uploading || uploadedImages.length >= maxFiles) return;

                if (!uploadArea.contains(e.relatedTarget)) {
                    uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                    uploadArea.classList.add('border-gray-300');
                }
            }

            function handleDrop(e) {
                e.preventDefault();
                e.stopPropagation();
                if (uploading || uploadedImages.length >= maxFiles) return;

                uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                uploadArea.classList.add('border-gray-300');

                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    handleImageUpload(files);
                }
            }

            // Update upload area state
            function updateUploadAreaState() {
                if (uploading || uploadedImages.length >= maxFiles) {
                    uploadArea.style.opacity = '0.5';
                    uploadArea.style.cursor = 'not-allowed';
                } else {
                    uploadArea.style.opacity = '1';
                    uploadArea.style.cursor = 'pointer';
                }
            }

            // Show/hide upload progress
            function showUploadProgress() {
                uploadProgress.style.display = 'block';
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '%';
                }, 200);

                // Store interval for cleanup
                uploadProgress.dataset.interval = interval;
            }

            function hideUploadProgress() {
                const interval = uploadProgress.dataset.interval;
                if (interval) {
                    clearInterval(interval);
                }
                progressBar.style.width = '100%';
                progressText.textContent = '100%';
                setTimeout(() => {
                    uploadProgress.style.display = 'none';
                    progressBar.style.width = '0%';
                    progressText.textContent = '0%';
                }, 500);
            }

            // Show image preview (like FeedbackModal's Image component)
            function showImagePreview(url) {
                previewImage.src = url;
                imagePreviewModal.style.display = 'flex';
            }

            // Submit feedback (exactly like FeedbackModal)
            async function handleSubmit(values) {
                // Check if user is logged in first
                if (!checkUserLogin()) {
                    sessionStorage.setItem('redirectUrl', window.location.href);
                    window.open('https://medxy.ai/en/login', '_blank')
                    return;
                }

                // Get real user info from login status
                const userInfo = {
                    realName: 'User', // Get from actual user session
                    userName: 'user',
                    mobile: '',
                    email: getDefaultEmail(),
                    userId: 'user'
                };

                setLoading(true);

                try {
                    // Format feedback content (exactly like FeedbackModal)
                    const formattedContent = formatFeedbackContent(values.mgContent);

                    // Build app name + language suffix + (feedback) as title (exactly like FeedbackModal)
                    const currentLanguage = 'en';
                    const currentAppName = 'novax-base';
                    const feedbackText = currentLanguage === 'zh' ? 'Feedback' : 'Feedback';
                    const titleWithAppAndLang = `${currentAppName}-${currentLanguage}（${feedbackText}）`;

                    // Build feedback data (exactly like FeedbackModal)
                    const feedbackData = {
                        projectId: 1, // getFeedbackProjectId()
                        mgTitle: titleWithAppAndLang,
                        mgRealName: userInfo.realName || userInfo.userName || '',
                        mgTell: userInfo.mobile || '',
                        mgEmail: userInfo.email || getDefaultEmail(),
                        mgUnit: '',
                        mgContent: formattedContent,
                        mgAccessoryList: uploadedImages, // Include uploaded images
                        mgIsPublic: 1,
                        mgType: 0,
                        mgSource: 'PC',
                        mgUserid: 0,
                        mgUsername: userInfo.userName || '',
                        versionInfo: '1.0.0',
                        moduleType: 'feedback',
                        objectId: 0,
                        objectTitle: titleWithAppAndLang,
                        clientIp: ''
                    };

                    // Submit feedback (exactly like FeedbackModal's xaiApi.submitFeedback)
                    const apiBase = getApiBase();
                    const response = await fetch(`${apiBase}/ai-base/openapi/leave-message?locale=${currentLanguage}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(feedbackData),
                    });

                    console.log('API response status:', response.status);

                    if (response.ok) {
                        const responseData = await response.json();
                        console.log('API response data:', responseData);

                        // Parse API response format: {"status":200,"message":"成功","data":null}
                        if (responseData && responseData.status === 200) {
                            // showMessage('Feedback submitted successfully!', 'success');
                            form.reset();
                            uploadedImages.length = 0;
                            updateUploadedImagesDisplay();
                            setTimeout(() => {
                                closeModal();
                            }, 2000);
                        } else {
                            throw new Error(responseData?.message || 'Submit failed');
                        }
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    console.error('Submit feedback failed:', error);
                    showMessage('Failed to submit feedback, please try again', 'error');
                } finally {
                    setLoading(false);
                }
            }

            // Helper functions
            function openModal() {
                console.log('Opening contact modal...');
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                setTimeout(() => {
                    if (contentTextarea) contentTextarea.focus();
                }, 100);
            }

            function closeModal() {
                form.reset();
                if (charCount) charCount.textContent = '0';

                // Clear uploaded images (exactly like FeedbackModal)
                uploadedImages.length = 0;
                updateUploadedImagesDisplay();

                modal.classList.add('hidden');
                document.body.style.overflow = '';
            }

            function setLoading(loading) {
                submitBtn.disabled = loading;
                if (loading) {
                    submitText.textContent = 'Submitting...';
                    submitSpinner.classList.remove('hidden');
                } else {
                    submitText.textContent = 'Submit Feedback';
                    submitSpinner.classList.add('hidden');
                }
            }

            function showMessage(message, type) {
                // Create Antd-style message notification
                const messageContainer = document.createElement('div');
                let bgColor, textColor, borderColor, icon;

                switch(type) {
                    case 'success':
                        bgColor = 'bg-green-50';
                        textColor = 'text-green-800';
                        borderColor = 'border-green-200';
                        icon = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
                        break;
                    case 'warning':
                        bgColor = 'bg-yellow-50';
                        textColor = 'text-yellow-800';
                        borderColor = 'border-yellow-200';
                        icon = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>';
                        break;
                    default: // error
                        bgColor = 'bg-red-50';
                        textColor = 'text-red-800';
                        borderColor = 'border-red-200';
                        icon = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
                }

                messageContainer.className = `fixed top-6 left-1/2 transform -translate-x-1/2 z-[9999] px-4 py-2 rounded-md shadow-lg text-sm font-medium transition-all duration-300 ${bgColor} ${textColor} border ${borderColor}`;

                messageContainer.innerHTML = `
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            ${icon}
                        </svg>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(messageContainer);

                // Auto remove after 3 seconds
                setTimeout(() => {
                    messageContainer.style.opacity = '0';
                    messageContainer.style.transform = 'translate(-50%, -20px)';
                    setTimeout(() => {
                        if (messageContainer.parentNode) {
                            messageContainer.parentNode.removeChild(messageContainer);
                        }
                    }, 300);
                }, 3000);
            }



            // Event listeners
            if (trigger) {
                console.log('Contact trigger found, adding click listener');
                trigger.addEventListener('click', (e) => {
                    console.log('Contact trigger clicked!');
                    e.preventDefault();
                    openModal();
                });
            } else {
                console.error('Contact trigger not found!');
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeModal);
            }

            // File upload event listeners (exactly like FeedbackModal)
            if (uploadArea) {
                uploadArea.addEventListener('dragenter', handleDragEnter);
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);
                uploadArea.addEventListener('click', () => {
                    if (!uploading && uploadedImages.length < maxFiles) {
                        fileInput.click();
                    }
                });
            }

            if (fileInput) {
                fileInput.addEventListener('change', (e) => {
                    const files = Array.from(e.target.files || []);
                    if (files.length > 0) {
                        handleImageUpload(files);
                    }
                    e.target.value = ''; // Reset for reselection
                });
            }

            // Image preview modal event listeners
            if (closePreview) {
                closePreview.addEventListener('click', () => {
                    imagePreviewModal.style.display = 'none';
                });
            }

            if (imagePreviewModal) {
                imagePreviewModal.addEventListener('click', (e) => {
                    if (e.target === imagePreviewModal) {
                        imagePreviewModal.style.display = 'none';
                    }
                });
            }

            // Make functions globally accessible
            window.handleImageRemove = handleImageRemove;
            window.showImagePreview = showImagePreview;

            // Close on backdrop click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Close on Escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal();
                }
                if (e.key === 'Escape' && imagePreviewModal.style.display === 'flex') {
                    imagePreviewModal.style.display = 'none';
                }
            });

            // Form submission
            form.addEventListener('submit', async (e) => {
                e.preventDefault();

                // Validate required fields
                const mgContent = contentTextarea.value.trim();
                if (!mgContent) {
                    showMessage('Content is required', 'error');
                    return;
                }

                if (mgContent.length < 10) {
                    showMessage('Content must be at least 10 characters', 'error');
                    return;
                }

                if (mgContent.length > 1000) {
                    showMessage('Content must not exceed 1000 characters', 'error');
                    return;
                }

                // Submit feedback
                await handleSubmit({ mgContent });
            });
        }

        // ========== Output Format Plugin ==========
        window.outputFormatPlugin = {
            currentFormat: 'markdown',
            renderedContent: '',

            init: function() {
                console.log('📋 Output Format Plugin initialized');
                this.setupMarkdown();
            },

            setupMarkdown: function() {
                if (typeof marked !== 'undefined') {
                    marked.setOptions({
                        highlight: function(code, lang) {
                            if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                                try {
                                    return hljs.highlight(code, { language: lang }).value;
                                } catch (err) {}
                            }
                            return typeof hljs !== 'undefined' ? hljs.highlightAuto(code).value : code;
                        },
                        breaks: true,
                        gfm: true
                    });
                }
            },

            setFormat: function(format) {
                this.currentFormat = format;
                console.log('Format changed to:', format);
            },

            renderContent: function(content) {
                if (!content) return '';

                try {
                    if (this.currentFormat === 'markdown') {
                        return this.renderMarkdown(content);
                    } else {
                        return this.renderHTML(content);
                    }
                } catch (error) {
                    console.error('Render error:', error);
                    return content;
                }
            },

            renderMarkdown: function(content) {
                if (typeof marked === 'undefined') {
                    console.warn('Marked.js not loaded, returning plain content');
                    return content;
                }

                const htmlOutput = marked.parse(content);
                this.renderedContent = htmlOutput;
                return htmlOutput;
            },

            renderHTML: function(content) {
                if (typeof marked === 'undefined') {
                    console.warn('Marked.js not loaded, returning plain content');
                    return content;
                }

                const htmlOutput = marked.parse(content);
                this.renderedContent = htmlOutput;
                return htmlOutput;
            },

            downloadFile: function(content, filename) {
                if (!content) {
                    this.showMessage('No content to download', 'error');
                    return;
                }

                const processedContent = this.renderContent(content);
                filename = filename || `novax-output.${this.currentFormat === 'markdown' ? 'html' : 'html'}`;

                const blob = new Blob([processedContent], { type: 'text/html' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showMessage(`File downloaded: ${filename}`, 'success');
            },

            copyToClipboard: function(content) {
                if (!content) {
                    this.showMessage('No content to copy', 'error');
                    return;
                }

                const processedContent = this.renderContent(content);

                navigator.clipboard.writeText(processedContent).then(() => {
                    this.showMessage('Content copied to clipboard!', 'success');
                }).catch(err => {
                    this.showMessage('Failed to copy content', 'error');
                    console.error('Copy error:', err);
                });
            },

            showMessage: function(message, type) {
                // Create or update status message
                let statusDiv = document.getElementById('output-status-message');
                if (!statusDiv) {
                    statusDiv = document.createElement('div');
                    statusDiv.id = 'output-status-message';
                    statusDiv.className = 'fixed top-4 right-4 z-50 p-3 rounded-lg shadow-lg transition-all duration-300';
                    document.body.appendChild(statusDiv);
                }

                statusDiv.textContent = message;
                statusDiv.className = `fixed top-4 right-4 z-50 p-3 rounded-lg shadow-lg transition-all duration-300 ${
                    type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                }`;

                // Auto hide after 3 seconds
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.parentNode.removeChild(statusDiv);
                    }
                }, 3000);
            },

            // Test function for demo
            testPlugin: function() {
                const sampleContent = `# Test Output

This is a **test** of the output format plugin.

## Features
- Markdown rendering
- HTML output
- File download
- Clipboard copy

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

> This is a test blockquote.
`;

                console.log('Testing output format plugin...');
                console.log('Current format:', this.currentFormat);
                console.log('Sample content:', sampleContent);

                const rendered = this.renderContent(sampleContent);
                console.log('Rendered content:', rendered);

                this.showMessage('Plugin test completed! Check console for details.', 'success');
            }
        };

        // Initialize plugin when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                window.outputFormatPlugin.init();
            });
        } else {
            window.outputFormatPlugin.init();
        }

        // Console welcome message
        console.log('%c🚀 NovaX Landing Page Loaded!', 'color: #3b82f6; font-size: 16px; font-weight: bold;');

    </script>
</body>
</html>

