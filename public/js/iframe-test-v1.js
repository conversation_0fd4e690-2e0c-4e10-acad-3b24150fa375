// 寮曞叆寰俊鎺堟潈鏂规硶
function addScript() {
    document.write('<script language="javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/jquery-latest.min.js"></script>')
}
addScript()
function isJSON(str) {
    if (typeof str == 'string') {
        try {
            var obj=JSON.parse(str);
            if(typeof obj == 'object' && obj ){
                return true;
            }else{
                return false;
            }

        } catch(e) {
            return false;
        }
    }
}

window.addEventListener('message', function (e) {
    // console.log(e, 'eeeeeeeeeee')
    let data = e.data && isJSON(e.data) ? JSON.parse(e.data) : ''
    // console.log(data, 'data')
    if(!data) {
        return
    }
    if(data.type == 'reload') {
        window.location.reload()
    }
    if(data.type == 'completeLogin') {
        if(window.location.pathname.split('/')[1] == 'form' || window.location.pathname.split('/')[1] == '' || window.location.pathname.split('/')[2] == 'subscription' || window.location.pathname.split('/')[1] == 'user') {
            window.location.reload()
            return false
        }
        // console.log(updateCookies, 'updateCookies')
        if(typeof updateCookies == "function" && typeof userInfoHeader == "function") {
            updateCookies()
            userInfoHeader()
            if(typeof commentInit == "function") {
                // 璇︽儏璇勮
                window.location.reload()
            }
            let status = getUserStatus()
            // console.log(status, 'getUserStatus')
            if(status == 0 && !window.location.host.includes('ai')) {
                removeLoginDom()
                addPerfectInfoDom()
            }else {
                removeLoginDom()
            }
        }else {
            window.location.reload()
        }
    }
    if(data.type == 'hidden') {
        removeLoginDom()
    }
    if(data.type == 'closePerfectInfo') {
        removePerfectInfoDom()
        if(window.location.pathname.split('/')[1] == 'form') {
            window.location.reload()
            return false
        }
    }
    if(data.type == 'redirectUrl') {
        window.top.location.href = data.data
    }
    if(data.type == 'openUrl') {
        window.open(data.data, '_blank')
    }
    if(data.type == 'authWx') {
        authWechat();
    }
    if(data.type == 'limitScroll') {
        document.body.style = "overflow:hidden;"
    }
})

function getUserStatus() {
    var res = ''
    jQuery.ajax({
        type: 'get',
        url: '../perfectInfo/userInfoStatus',
        contentType: "application/json",
        async: false,
        dataType: 'json',
        success: function (result) {
            if (result.status == 200 && result.message == "success") {//鎴愬姛
                res = result.data.isCompleteInfo == null ? 1 : result.data.isCompleteInfo
            }
        },
        error: function (result) {// 璇锋眰鎶ラ敊鎵ц
            // console.log(result)
        }
    });
    return res
}


let source = window.location.pathname.split('/')[1]
let sourceChild = window.location.pathname.split('/')[2]
saveCookie('registerBusinessName',source, 10)
saveCookie('registerBusinessNameChild',sourceChild, 10)

function saveCookie(cookieName, cookieValue, expiresYears) {
    var d = new Date();
    d.setTime(d.getTime() + expiresYears * 365 * 24 * 3600 * 1000);
    domain = '.medsci.cn'
    document.cookie = cookieName + "=" + cookieValue + ";expires=" + d.toGMTString() + ';domain=' + domain + ';path=/';
}
function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return decodeURIComponent(pair[1]);
        }
    }
    return (false);
}
// 寰俊鎺堟潈
function authWechat() {
    // console.log(window.top.location.href, 'window.top.location.href')
    var baseUrl = 'https://login.medsci.cn'
    if(window.top.location.href.includes('test') || window.top.location.href.includes('medon')){
        baseUrl = 'https://login-test.medon.com.cn'
    }
    jQuery.ajax({
        type: 'get',
        url: baseUrl+'/medsciUserAuth/auth',
        contentType: "application/json",
        async: false,
        dataType: 'json',
        data: {
            url: window.top.location.href
        },
        success: function (result) {
            // console.log(result, 'rrr')
            window.top.location.href = result.data;
        },
        error: function (result) {// 璇锋眰鎶ラ敊鎵ц
            // console.log(result)
        }
    });
}

// var code = getQueryVariable('code');
// var state = getQueryVariable('state');
// if(code && state && code != '0') {
//     jQuery(document).ready(function() {
//         setTimeout(function() {
//             addLogin()
//         }, 100)
//     })
// }
jQuery(document).ready(function() {
    // console.log('dom 鍔犺浇瀹屾垚')
    var code = getQueryVariable('code');
    var state = getQueryVariable('state');
    var accessToken = getQueryVariable('accessToken')
    if(code && state && code != '0' && !accessToken && !document.cookie.includes('userInfo=')) {
        addLogin(code, state)
    }
})
function addLoginDom(hasHint) {
    if(is_weixin()) {
        if(window.top.location.href.includes('form=logout')) {
            //鍦ㄩ渶瑕佺櫥闄嗙殑椤甸潰閫€鍑虹櫥褰曞悗锛屽脊妗�
            addLogin('','', hasHint)
        }else {
            addLogin('','', hasHint)
            // oauthLogin()
        }
    } else {
        addLogin('','', hasHint)
    }
}
function addLogin(code, state, hasHint) {
    removeLoginDom()
    var hasHintV = hasHint ? hasHint : '';
    var iframe = document.createElement('iframe');
    iframe.id = 'loginIframe'
    iframe.style.cssText="width: 100vw;height: 100vh;overflow: hidden;position: fixed;top: 0;left: 0;z-index: 99999"
    const ipCheck = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
    var src
    if(window.top.location.href.includes('localhost') || ipCheck.test(window.location.hostname)) {
        src = 'https://login-test.medon.com.cn/module/login?hasHint=' + hasHintV + '&comeUrl=' + window.top.location.href;
    }else if(window.top.location.href.includes('test') || window.top.location.href.includes('medon')) {
        if(code && state && code != '0') {
            src = 'https://login-test.medon.com.cn/module/login?code=' + code + '&state=' + state + '&hasHint=' + hasHintV + '&comeUrl=' + window.top.location.href;
        } else {
            src = 'https://login-test.medon.com.cn/module/login?hasHint=' + hasHintV + '&comeUrl=' + window.top.location.href;
        }
        // src = 'https://login-test.medon.com.cn/pop'
    } else {
        if(code && state && code != '0') {
            src = 'https://login.medsci.cn/module/login?code=' + code + '&state=' + state + '&hasHint=' + hasHintV + '&comeUrl=' + window.top.location.href;
            // console.log(src, 'src')
        } else {
            src = 'https://login.medsci.cn/module/login?hasHint=' + hasHintV + '&comeUrl=' + window.top.location.href;
        }
    }
    // console.log(src, 'src')
    iframe.setAttribute('src',src)
    iframe.setAttribute('frameborder','0')
    document.body.appendChild(iframe);
}


function removeLoginDom() {
    // console.log('removeLoginDom')
    var iframe = document.getElementById('loginIframe');
    if(!!iframe) {
        document.body.removeChild(iframe);
    }
}
function addPerfectInfoDom() {
    removePerfectInfoDom()
    var iframe = document.createElement('iframe');
    iframe.id = 'perfectInfoIframe'
    iframe.style.cssText="width: 100vw;height: 100vh;overflow: hidden;position: fixed;top: 0;left: 0;z-index: 99999"
    const ipCheck = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
    var src
    var parentLink = window.top.location.href
    var infoType;
    switch (true) {
        case parentLink.includes('live.'):
            infoType = 1;
            break;
        case parentLink.includes('live-test.'):
            infoType = 1;
            break;
        case parentLink.includes('/sci/index.do'):
            infoType = 2;
            break;
        case parentLink.includes('/sci/submit.do'):
            infoType = 2;
            break;
        default:
            infoType = 0;
            break;
    }

    if(window.top.location.href.includes('localhost') || ipCheck.test(window.location.hostname)) {
        // src = 'http://portal-test.medsci.cn/perfectInfo'
        src = 'http://portal-test.medon.com.cn/module/perfectInfo?infoType=' + infoType + '&comeUrl=' + window.top.location.href
    } else if(window.top.location.href.includes('test') || window.top.location.href.includes('medon')){
        // src = 'https://portal-test.medsci.cn/perfectInfo'
        src = 'https://portal-test.medon.com.cn/module/perfectInfo?infoType=' + infoType + '&comeUrl=' + window.top.location.href
    } else {
        // src = 'https://www.medsci.cn/perfectInfo'
        src = 'https://www.medsci.cn/module/perfectInfo?infoType=' + infoType + '&comeUrl=' + window.top.location.href
    }
    iframe.setAttribute('src',src)
    iframe.setAttribute('frameborder','0')
    document.body.appendChild(iframe);
}

function removePerfectInfoDom() {
    var iframe = document.getElementById('perfectInfoIframe');
    if(!!iframe) {
        document.body.removeChild(iframe);
    }
}


function is_weixin(){
    let ua = navigator.userAgent.toLowerCase();
    if(ua.match(/MicroMessenger/i)=="micromessenger"&&ua.match(/WxWork/i) != "wxwork") {
        return true
    } else {
        return false
    }
}
function oauthLogin() {
    const ipCheck = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
    let url
    if(window.top.location.href.includes('localhost') || ipCheck.test(window.location.hostname)) {
        url = 'http://login-test.medon.com.cn'
    } else if(window.top.location.href.includes('test') || window.top.location.href.includes('medon')){
        url = 'https://login-test.medon.com.cn'
    } else {
        url = 'https://login.medsci.cn'
    }
    let redirect_url = window.top.location.href
    window.top.location.href = `${url}/wechat/webpage/oauth?redirect_url=${redirect_url}`
}
