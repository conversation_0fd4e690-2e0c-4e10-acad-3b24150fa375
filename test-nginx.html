<!DOCTYPE html>
<html>
<head>
    <title>Nginx Test</title>
</head>
<body>
    <h1>Nginx Configuration Test</h1>
    <p>If you see this, <PERSON>inx is working but not serving prerendered files.</p>
    <script>
        // 检查响应头
        fetch('/zh/novax-pro')
            .then(response => {
                console.log('Response Headers:');
                for (let [key, value] of response.headers.entries()) {
                    console.log(key + ': ' + value);
                }
                return response.text();
            })
            .then(html => {
                console.log('HTML Length:', html.length);
                console.log('Contains prerendered content:', html.includes('科研灵感引擎 Pro'));
            });
    </script>
</body>
</html>
