#!/usr/bin/env node

/**
 * 简单的预渲染脚本
 * 使用 JSDOM 来渲染 React 应用的关键页面
 * 生成包含完整 HTML 内容和 SEO 元数据的静态文件
 */

import { JSDOM } from 'jsdom';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import puppeteer from 'puppeteer';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
// 从环境变量中检测构建模式
const detectBuildMode = () => {
  // 优先使用 PRERENDER_MODE 环境变量（从构建脚本传递）
  const prerenderMode = process.env.PRERENDER_MODE;
  const npmScript = process.env.npm_lifecycle_event;
  const nodeEnv = process.env.NODE_ENV;

  console.log(`🔍 检测环境 - PRERENDER_MODE: ${prerenderMode}, npm脚本: ${npmScript}, NODE_ENV: ${nodeEnv}`);

  if (prerenderMode) {
    return prerenderMode;
  }

  // 检查 npm 脚本名称（备用方案）
  if (npmScript) {
    if (npmScript.includes('test')) return 'test';
    if (npmScript.includes('prod')) return 'production';
    if (npmScript.includes('international')) return 'international';
    if (npmScript.includes('dev')) return 'development';
  }

  // 检查 NODE_ENV
  if (nodeEnv) {
    return nodeEnv;
  }

  // 默认为开发环境
  return 'development';
};

const mode = detectBuildMode();
const envFile = `.env.${mode}`;
const envPath = path.join(__dirname, '..', envFile);

// 加载对应的环境变量文件
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(`📄 加载环境变量文件: ${envFile} (检测到模式: ${mode})`);
} else {
  console.log(`⚠️  环境变量文件不存在: ${envFile}，使用默认配置`);
}

// 获取自定义输出目录
const customDistDir = process.env.DIST_DIR || 'dist';

/**
 * 根据打包模式获取API基础URL
 */
function getApiBaseUrl(buildMode) {
  switch (buildMode) {
    case 'prod':
    case 'production':
      return 'https://ai.medsci.cn';
    case 'international':
      return 'https://medxy.ai';
    default:
      return 'https://ai.medon.com.cn';
  }
}

/**
 * 将应用语言转换为语言代码
 */
function convertAppLangToCode(appLang) {
  const langMap = {
    '英文': 'en',
    '中文': 'zh'
  };
  return langMap[appLang] || 'en'; // 默认返回英文
}

/**
 * 根据构建模式获取对应的域名
 * 基于启动命令判断使用哪个域名作为configKey
 */
function getDomainByBuildMode(buildMode) {
  // 检查npm脚本名称来判断域名
  const npmScript = process.env.npm_lifecycle_event;

  console.log(`🔍 根据启动命令判断域名 - npm脚本: ${npmScript}, 构建模式: ${buildMode}`);

  // 根据启动命令判断域名
  if (npmScript) {
    if (npmScript.includes('international')) {
      return 'www.medxy.ai';
    }
    if (npmScript.includes('prod')) {
      return 'ai.medsci.cn';
    }
    if (npmScript.includes('test')) {
      return 'ai.medon.com.cn';
    }
  }

  // 备用方案：根据构建模式判断
  switch (buildMode) {
    case 'prod':
    case 'production':
      return 'ai.medsci.cn';
    case 'international':
      return 'www.medxy.ai';
    default:
      return 'ai.medon.com.cn';
  }
}

/**
 * 从API获取应用数据并生成路由
 */
async function getRoutesFromAPI(buildMode) {
  console.log('📡 正在从API获取应用数据...', buildMode);
  try {
    const apiBaseUrl = getApiBaseUrl(buildMode);
    const domain = getDomainByBuildMode(buildMode);
    const apiUrl = `${apiBaseUrl}/dev-api/ai-base/index/getAppByDomain`;
    const allRoutes = [];
    const locales = ['zh', 'en']; // 支持的语言

    // 为每种语言分别调用API
    for (const locale of locales) {
      try {
        console.log(`📡 获取 ${locale} 语言的应用数据...`);
        console.log(`🌐 API URL: ${apiUrl}`);
        console.log(`🏠 域名: ${domain}, 语言: ${locale}`);

        const response = await fetch(apiUrl+'?locale='+locale, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            configKey: domain
          })
        });

        if (!response.ok) {
          console.warn(`⚠️  ${locale} 语言的API请求失败: ${response.status}`);
          continue;
        }

        const result = await response.json();
        console.log(`📊 ${locale} 语言API响应:`, {
          code: result.code,
          dataLength: result.data?.length || 0,
          message: result.msg
        });

        if (result.code === 0 && result.data && Array.isArray(result.data)) {
          // 处理该语言的应用数据
          result.data.forEach(app => {
            if (app.appLang && app.appNameEn) {
              const langCode = convertAppLangToCode(app.appLang);
              const route = `/${langCode}/${app.appNameEn}`;

              // 避免重复路由
              if (!allRoutes.includes(route)) {
                allRoutes.push(route);
                console.log(`📍 生成路由: ${route} (${app.appName}) [API语言:${locale}, 应用语言:${app.appLang}]`);
              } else {
                console.log(`🔄 跳过重复路由: ${route}`);
              }
            }
          });
        } else {
          console.warn(`⚠️  ${locale} 语言API返回数据格式错误:`, result);
        }

      } catch (error) {
        console.warn(`⚠️  获取 ${locale} 语言数据失败:`, error.message);
      }
    }
    allRoutes.push('/zh/knowledgebase');
    allRoutes.push('/en/knowledgebase');
    if (allRoutes.length > 0) {
      return allRoutes;
    } else {
      throw new Error('未获取到任何路由数据');
    }

  } catch (error) {
    console.error('❌ 从API获取路由失败:', error.message);
    console.log('🔄 使用fallback静态路由配置');

    // 返回fallback静态路由
    return [
      '/zh/novax-base', '/zh/novax-pro',
      '/zh/elavax-base', '/zh/elavax-pro',
      '/en/novax-base', '/en/novax-pro',
      '/en/elavax-base', '/en/elavax-pro',
      '/zh/datascore-base', '/en/datascore-base',
      '/zh/litra-x', '/zh/knowledgebase', '/en/knowledgebase'
    ];
  }
}

/**
 * 处理API响应数据
 */
function processApiResponse(result) {
  if (result.code !== 0 || !result.data || !Array.isArray(result.data)) {
    throw new Error(`API返回数据格式错误: ${JSON.stringify(result)}`);
  }

  const routes = [];

  // 遍历API返回的应用数据
  result.data.forEach(app => {
    if (app.appLang && app.appNameEn) {
      const langCode = convertAppLangToCode(app.appLang);
      const route = `/${langCode}/${app.appNameEn}`;
      routes.push(route);
      console.log(`📍 生成路由: ${route} (${app.appName})`);
    }
  });

  console.log(`✅ 成功从API获取 ${routes.length} 个路由`);
  return routes;
}

// 配置
const config = {
  distDir: path.join(__dirname, `../${customDistDir}`),
  routes: [], // 将在运行时从API动态获取
  templatePath: path.join(__dirname, `../${customDistDir}/index.html`)
};

/**
 * 读取基础 HTML 模板
 */
function readTemplate() {
  try {
    return fs.readFileSync(config.templatePath, 'utf-8');
  } catch (error) {
    console.error('❌ 无法读取 HTML 模板:', error.message);
    process.exit(1);
  }
}

/**
 * 为特定路由生成 SEO 元数据
 */
function generateSEOMetadata(route) {
  const isEnglish = route.includes('/en/');

  // 默认元数据
  const metadata = {
  
  };

  // NovaX 系列应用
  if (route.includes('novax-base')) {
    if (isEnglish) {
      metadata.title = 'Research Inspiration Engine | Innovation Design Tool';
      metadata.description = 'Quickly generate innovative research ideas! Input research background to rapidly obtain research direction design, experimental ideas and preliminary concepts to help launch research projects';
      metadata.keywords = 'Research Design,Innovation Inspiration,Project Direction,Experimental Design,Research Assistant,Research Concepts';
      metadata.ogTitle = 'Research Inspiration Engine | Innovation Design Tool';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png';
    } else {
      metadata.title = '科研灵感引擎 | 创新思路设计工具';
      metadata.description = '快速生成科研创新思路！输入研究背景，快速获取科研方向设计、实验思路与初步构想，助力启动研究项目';
      metadata.keywords = '科研思路设计,创新灵感,课题方向,实验设计,科研助手,研究构想';
      metadata.ogTitle = '科研灵感引擎 | 创新思路设计工具';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png';
    }
  } else if (route.includes('litra-x')) {
    if (isEnglish) {
      metadata.title = 'Output Accelerator | Rapid Inspiration to Draft - Writing Elite Mentor Assistant';
      metadata.description = 'Submit your research topic, and the Writing Elite Mentor will generate a draft for you instantly! Helping you efficiently transform research inspiration into academic results.';
      metadata.keywords = 'Rapid manuscript writing, shortened writing cycle, content framework generation, research inspiration transformation, academic output production';
      metadata.ogTitle = 'Output Accelerator | Rapid Inspiration to Draft - Writing Elite Mentor Assistant';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/3014db161cda2d1bbdbef3873ed82db47126ffa2394589ba51c14ed038138027.png';
    } else {
      metadata.title = '成果产出加速器 | 灵感快速成稿 - 写作精英导师助手';
      metadata.description = '提交研究主题，写作精英导师即刻为你生成稿件！助你高效转化科研灵感为学术成果。';
      metadata.keywords = '稿件快速撰写，撰写周期缩短，内容框架生成，科研灵感转化，学术成果产出';
      metadata.ogTitle = '成果产出加速器 | 灵感快速成稿 - 写作精英导师助手';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/3014db161cda2d1bbdbef3873ed82db47126ffa2394589ba51c14ed038138027.png';
    }
  }else if (route.includes('litrax-bak-base')) {
    if (isEnglish) {
      metadata.title = 'Output Accelerator | Rapid Inspiration to Draft - Writing Elite Mentor Assistant';
      metadata.description = 'Submit your research topic, and the Writing Elite Mentor will generate a draft for you instantly! Helping you efficiently transform research inspiration into academic results.';
      metadata.keywords = 'Rapid manuscript writing, shortened writing cycle, content framework generation, research inspiration transformation, academic output production';
      metadata.ogTitle = 'Output Accelerator | Rapid Inspiration to Draft - Writing Elite Mentor Assistant';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/3014db161cda2d1bbdbef3873ed82db47126ffa2394589ba51c14ed038138027.png';
    } else {
      metadata.title = '成果产出加速器 | 灵感快速成稿 - 写作精英导师助手';
      metadata.description = '提交研究主题，写作精英导师即刻为你生成稿件！助你高效转化科研灵感为学术成果。';
      metadata.keywords = '稿件快速撰写，撰写周期缩短，内容框架生成，科研灵感转化，学术成果产出';
      metadata.ogTitle = '成果产出加速器 | 灵感快速成稿 - 写作精英导师助手';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/3014db161cda2d1bbdbef3873ed82db47126ffa2394589ba51c14ed038138027.png';
    }
  }
  else if (route.includes('competition-model-base')) {
    if (isEnglish) {
      metadata.title = 'World\'s Leading General-Purpose Large Language Model, Powerful AI Assistant';
      metadata.description = 'One-stop solution for your work and creative needs: AI-powered writing, code generation, multilingual translation, email & copy polishing, role-playing conversations, and knowledgeable Q&A, significantly boosting your productivity.';
      metadata.keywords = 'AI writing, code generation, AI programming, intelligent translation, copy editing, chatbot, knowledge Q&A, productivity tool';
      metadata.ogTitle = 'World\'s Leading General-Purpose Large Language Model, Powerful AI Assistant';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/2149caf6fb9c5f62a9abfe952066afa9d01f0e7f9119703a505173f9f84a778e.svg';
    } else {
      metadata.title = '全球领先的通用大语言模型，强大的AI智能助手';
      metadata.description = '一站式解决您工作与创作需求：AI智能写作、编程代码生成、多语言精准翻译、邮件文案润色、角色对话聊天与知识问答解惑，极大提升您的效率。';
      metadata.keywords = 'AI写作, 代码生成, AI编程, 智能翻译, 文案润色, 聊天机器人, 知识问答, 效率工具';
      metadata.ogTitle = '全球领先的通用大语言模型，强大的AI智能助手';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/2149caf6fb9c5f62a9abfe952066afa9d01f0e7f9119703a505173f9f84a778e.svg';
    }
  }else if (route.includes('competition-deepseek-base')) {
    if (isEnglish) {
      metadata.title = 'World\'s Leading General-Purpose Large Language Model, Powerful AI Assistant';
      metadata.description = 'One-stop solution for your work and creative needs: AI-powered writing, code generation, multilingual translation, email & copy polishing, role-playing conversations, and knowledgeable Q&A, significantly boosting your productivity.';
      metadata.keywords = 'AI writing, code generation, AI programming, intelligent translation, copy editing, chatbot, knowledge Q&A, productivity tool';
      metadata.ogTitle = 'World\'s Leading General-Purpose Large Language Model, Powerful AI Assistant';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/2149caf6fb9c5f62a9abfe952066afa9d01f0e7f9119703a505173f9f84a778e.svg';
    } else {
      metadata.title = '全球领先的通用大语言模型，强大的AI智能助手';
      metadata.description = '一站式解决您工作与创作需求：AI智能写作、编程代码生成、多语言精准翻译、邮件文案润色、角色对话聊天与知识问答解惑，极大提升您的效率。';
      metadata.keywords = 'AI写作, 代码生成, AI编程, 智能翻译, 文案润色, 聊天机器人, 知识问答, 效率工具';
      metadata.ogTitle = '全球领先的通用大语言模型，强大的AI智能助手';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/2149caf6fb9c5f62a9abfe952066afa9d01f0e7f9119703a505173f9f84a778e.svg';
    }
  }else if (route.includes('competition-model2-base')) {
    if (isEnglish) {
      metadata.title = 'World\'s Leading General-Purpose Large Language Model, Powerful AI Assistant';
      metadata.description = 'One-stop solution for your work and creative needs: AI-powered writing, code generation, multilingual translation, email & copy polishing, role-playing conversations, and knowledgeable Q&A, significantly boosting your productivity.';
      metadata.keywords = 'AI writing, code generation, AI programming, intelligent translation, copy editing, chatbot, knowledge Q&A, productivity tool';
      metadata.ogTitle = 'World\'s Leading General-Purpose Large Language Model, Powerful AI Assistant';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/2149caf6fb9c5f62a9abfe952066afa9d01f0e7f9119703a505173f9f84a778e.svg';
    } else {
      metadata.title = '全球领先的通用大语言模型，强大的AI智能助手';
      metadata.description = '一站式解决您工作与创作需求：AI智能写作、编程代码生成、多语言精准翻译、邮件文案润色、角色对话聊天与知识问答解惑，极大提升您的效率。';
      metadata.keywords = 'AI写作, 代码生成, AI编程, 智能翻译, 文案润色, 聊天机器人, 知识问答, 效率工具';
      metadata.ogTitle = '全球领先的通用大语言模型，强大的AI智能助手';
      metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/2149caf6fb9c5f62a9abfe952066afa9d01f0e7f9119703a505173f9f84a778e.svg';
    }
  }
 else if(route.includes('ai-doctor')) { 
    if (isEnglish) {
      metadata.title = 'Comprehensive Health Management - Disease Prevention and Scientific Wellness Guide';
      metadata.description = 'Explore common health knowledge, disease prevention, traditional and modern wellness practices, understand health management for special populations, medical report analysis, and anti-aging strategies, debunk health myths, and enhance quality of life.';
      metadata.keywords = 'health management, disease prevention, Traditional Chinese Medicine, modern wellness, special populations health, health myths, medical report analysis, anti-aging, health tools, personal health';
      metadata.ogTitle = 'Comprehensive Health Management - Disease Prevention and Scientific Wellness Guide';
    } else {
      metadata.title = '全面健康管理 - 预防疾病与科学养生指南';
      metadata.description = '探索常见健康知识、疾病预防、传统与现代养生方法，了解特殊人群健康管理、医疗报告分析及抗衰老策略，破除健康迷思，提升生活质量。';
      metadata.keywords = '健康管理，疾病预防，传统中医，现代养生，特殊人群健康，健康迷思，医疗报告分析，抗衰老，健康工具，个人健康';
      metadata.ogTitle = '全面健康管理 - 预防疾病与科学养生指南';
    }
    metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/b903a6c575549fbc9fc4a54f478de8b6aff87e9bd8607df2329a524cd932e91a.png';
  }
  // 知识库路由
  else if (route.includes('knowledgebase')) {
    if (isEnglish) {
      metadata.title = 'Knowledge Base | Medical AI Document Management Platform';
      metadata.description = 'Professional medical document management and knowledge base platform. Upload, organize, and search your medical research documents, papers, and knowledge resources with AI-powered insights.';
      metadata.keywords = 'knowledge base, document management, medical research, AI search, document upload, knowledge organization, medical documents, research papers';
      metadata.ogTitle = 'Knowledge Base | Medical AI Document Management Platform';
    } else {
      metadata.title = '知识库 | 医学AI文档管理平台';
      metadata.description = '专业的医学文档管理和知识库平台。上传、整理和搜索您的医学研究文档、论文和知识资源，配备AI智能洞察功能。';
      metadata.keywords = '知识库,文档管理,医学研究,AI搜索,文档上传,知识整理,医学文档,研究论文';
      metadata.ogTitle = '知识库 | 医学AI文档管理平台';
    }
    metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png';
  }
  else if (route.includes('derma-muse')) {
    if (isEnglish) {
      metadata.title = 'Comprehensive Skin Health and Beauty Care - Type Analysis, Disease Management, and Minimally Invasive Treatments';
      metadata.description = 'Discover your skin type, manage conditions like dermatitis, and gain professional beauty treatment advice and practical guidance for vague symptoms, combining Traditional Chinese Medicine and modern skincare to enhance skin health and aesthetics.';
      metadata.keywords = 'skin type, dermatitis management, beauty care, skin health, Traditional Chinese Medicine, modern skincare, aesthetic medicine, skin lesions, minimally invasive treatments, healthy skin';
      metadata.ogTitle = 'Comprehensive Skin Health and Beauty Care - Type Analysis, Disease Management, and Minimally Invasive Treatments';
    } else {
      metadata.title = '全面皮肤健康与美容护理 - 类型分析、疾病管理与微创治疗';
      metadata.description = '了解您的皮肤类型，管理皮炎等皮肤问题，获取专业美容医学建议及模糊症状的实用指导，结合传统中医与现代护肤，提升皮肤健康与美感。';
      metadata.keywords = '皮肤类型，皮炎管理，美容护理，皮肤健康，传统中医，现代护肤，美容医学，皮肤病变，微创治疗，健康皮肤';
      metadata.ogTitle = '全面皮肤健康与美容护理 - 类型分析、疾病管理与微创治疗';
    }
    metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/2af001fec41949ea9d3e123f70042cb334c2a04fccb2a4fdc90d54ba0933c9dd.png';
  }
  // ElaVaX 系列应用
  else if (route.includes('elavax-base')) {
    if (isEnglish) {
      metadata.title = 'Research Opportunity Insight Platform | Academic Development Intelligence Matching';
      metadata.description = 'Intelligent research development opportunity analysis tool! Deep interpretation of research results, precise matching of target journals/platforms, mining academic collaboration and transformation opportunities to boost career development';
      metadata.keywords = 'Research Opportunity Insight,Academic Platform Matching,Journal Recommendation,Research Translation,AI Academic Expert';
      metadata.ogTitle = 'Research Opportunity Insight Platform | Academic Development Intelligence Matching';
    } else {
      metadata.title = '科研机遇洞察平台 | 学术发展智能匹配';
      metadata.description = '科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展';
      metadata.keywords = '科研机遇洞察,学术平台匹配,期刊推荐,研究转化,AI学术专家';
      metadata.ogTitle = '科研机遇洞察平台 | 学术发展智能匹配';
    }
    metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png';
  } else if (route.includes('elavax-pro')) {
    if (isEnglish) {
      metadata.title = 'Research Opportunity Insight Platform | Advanced Academic Development Intelligence Matching';
      metadata.description = 'Intelligent research development opportunity analysis tool! Deep interpretation of research results, precise matching of target journals/platforms, mining academic collaboration and transformation opportunities to boost career development';
      metadata.keywords = 'Research Opportunity Insight,Academic Platform Matching,Journal Recommendation,Research Translation,AI Academic Expert';
      metadata.ogTitle = 'Research Opportunity Insight Platform | Advanced Academic Development Intelligence Matching';
    } else {
      metadata.title = '科研机遇洞察平台 | 高级学术发展智能匹配';
      metadata.description = '科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展';
      metadata.keywords = '科研机遇洞察,学术平台匹配,期刊推荐,研究转化,AI学术专家';
      metadata.ogTitle = '科研机遇洞察平台 | 高级学术发展智能匹配';
    }
    metadata.ogImage = 'https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png';
  }
  // 设置 ogDescription
  metadata.ogDescription = metadata.description;

  return metadata;
}

/**
 * 使用 Puppeteer 获取真实的 React 组件渲染内容
 */
async function getRealRenderedContent(route) {
  let browser;
  try {
    console.log(`🌐 正在获取 ${route} 的真实渲染内容...`);

    // 启动浏览器
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();

    // 设置视口
    await page.setViewport({ width: 1200, height: 800 });

    // 设置用户代理
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    // 访问开发服务器或生产服务器
    const url = `http://localhost:3000${route}`;
    console.log(`🔗 访问: ${url} (模式: ${mode})`);

    // 导航到页面
    await page.goto(url, {
      waitUntil: 'domcontentloaded',
      timeout: 60000
    });

    // 注入预渲染环境标识
    await page.evaluate(() => {
      window.__PRERENDER__ = true;
      window.__PRERENDER_MODE__ = 'puppeteer';
    });

    // 等待 React 应用完全加载
    try {
      await page.waitForSelector('#root', { timeout: 30000 });
      console.log('✅ React 应用已加载');
    } catch (e) {
      console.log('⚠️  等待 #root 超时，继续尝试...');
    }

    // 等待页面基本渲染完成
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 检查页面是否有内容
    const hasContent = await page.evaluate(() => {
      const root = document.getElementById('root');
      return root && root.innerHTML.trim().length > 100;
    });

    if (!hasContent) {
      console.log('⚠️  页面内容为空，等待更长时间...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // 等待特定的组件加载完成
    try {
      // 等待React应用的主要容器
      await page.waitForSelector('.flex.h-screen', { timeout: 30000 });
      console.log('✅ 主要布局容器已加载');
    } catch (e) {
      console.log('⚠️  等待主要布局容器超时，尝试其他选择器...');

      // 尝试等待其他可能的容器
      try {
        await page.waitForSelector('[data-app-uuid], .home-content, .app-content, main', { timeout: 20000 });
        console.log('✅ 备用组件已加载');
      } catch (e2) {
        console.log('⚠️  等待所有组件超时，检查页面状态...');

        // 检查页面是否有错误
        const pageContent = await page.evaluate(() => {
          return {
            hasRoot: !!document.getElementById('root'),
            rootContent: document.getElementById('root')?.innerHTML?.substring(0, 200) || '',
            hasError: document.querySelector('.error, [data-error]') !== null,
            title: document.title
          };
        });

        console.log('📊 页面状态:', pageContent);
      }
    }

    // 额外等待确保所有异步内容加载完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 获取渲染后的内容
    const content = await page.evaluate(() => {
      const root = document.getElementById('root');
      if (!root) return null;

      // 移除一些可能导致问题的元素
      const elementsToRemove = root.querySelectorAll('script, noscript, [data-reactroot]');
      elementsToRemove.forEach(el => {
        if (el.tagName === 'SCRIPT' || el.tagName === 'NOSCRIPT') {
          el.remove();
        }
      });

      return root.innerHTML;
    });

    if (content && content.trim()) {
      console.log(`✅ 成功获取 ${route} 的真实渲染内容 (${content.length} 字符)`);
      return content;
    } else {
      console.log(`⚠️  ${route} 的渲染内容为空`);
      return null;
    }

  } catch (error) {
    console.error(`❌ 获取 ${route} 渲染内容失败:`, error.message);
    return null;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * 获取应用信息
 */
function getAppInfo(route) {
  const isEnglish = route.includes('/en/');

  if (route.includes('novax-base')) {
    return {
      title: isEnglish ? 'NovaX Base - Core Research Inspiration' : 'NovaX Base - 核心思路启发',
      description: isEnglish
        ? 'Provides core research inspiration for medical studies, single research direction design based on research background.'
        : 'NovaX Base提供医学研究核心思路启发，基于研究背景进行单次科研方向思路设计。',
      className: 'novax-base',
      questions: isEnglish ? [
        'How to design a research protocol?',
        'What are the key elements of medical research?',
        'How to choose appropriate research methods?',
        'How to formulate research hypotheses?'
      ] : [
        '如何设计研究方案？',
        '医学研究的关键要素有哪些？',
        '如何选择合适的研究方法？',
        '如何制定研究假设？'
      ],
      features: [
        {
          icon: '🔬',
          title: isEnglish ? 'Research Design' : '研究设计',
          description: isEnglish ? 'Professional research protocol design' : '专业的研究方案设计'
        },
        {
          icon: '💡',
          title: isEnglish ? 'Inspiration Generation' : '思路启发',
          description: isEnglish ? 'Creative research direction suggestions' : '创新性研究方向建议'
        },
        {
          icon: '📊',
          title: isEnglish ? 'Method Selection' : '方法选择',
          description: isEnglish ? 'Optimal research methodology recommendations' : '最优研究方法推荐'
        }
      ]
    };
  } else if (route.includes('elavax-base')) {
    return {
      title: isEnglish ? 'ElaVaX Base - Medical Data Analysis' : 'ElaVaX Base - 医学数据分析',
      description: isEnglish
        ? 'Professional medical data analysis, statistical analysis, data visualization, and research design services.'
        : '专业的医学数据分析，包括统计分析、数据可视化、研究设计等服务。',
      className: 'elavax-base',
      questions: isEnglish ? [
        'How to perform statistical analysis?',
        'What visualization methods are suitable?',
        'How to interpret research results?',
        'How to choose statistical tests?'
      ] : [
        '如何进行统计分析？',
        '适合什么样的可视化方法？',
        '如何解读研究结果？',
        '如何选择统计检验方法？'
      ],
      features: [
        {
          icon: '📈',
          title: isEnglish ? 'Statistical Analysis' : '统计分析',
          description: isEnglish ? 'Comprehensive statistical analysis tools' : '全面的统计分析工具'
        },
        {
          icon: '📊',
          title: isEnglish ? 'Data Visualization' : '数据可视化',
          description: isEnglish ? 'Professional data visualization' : '专业的数据可视化'
        },
        {
          icon: '🔍',
          title: isEnglish ? 'Result Interpretation' : '结果解读',
          description: isEnglish ? 'Expert result interpretation' : '专业的结果解读'
        }
      ]
    };
  } else {
    // 默认应用信息
    return {
      title: isEnglish ? 'Medical AI Platform' : '医学AI平台',
      description: isEnglish ? 'Professional medical AI tools and services.' : '专业的医学AI工具和服务。',
      className: 'default-app',
      questions: [],
      features: []
    };
  }
}

/**
 * 获取应用标题
 */
function getAppTitle(route, isEnglish) {
  const appInfo = getAppInfo(route);
  return appInfo.title;
}

/**
 * 获取路由对应的主要内容
 */
function getMainContentForRoute(route, isEnglish) {
  if (route === '/') {
    return `
      <!-- 首页主要内容 -->
      <div class="home-main-content">
        <div class="welcome-section">
          <h2>${isEnglish ? 'Welcome to Medical AI Platform' : '欢迎使用医学AI平台'}</h2>
          <p>${isEnglish ? 'Professional medical AI tools for research, writing and analysis.' : '专业的医学AI工具，用于研究、写作和分析。'}</p>
        </div>

        <div class="app-grid">
          <div class="app-card novax-series">
            <h3>${isEnglish ? 'NovaX Series' : 'NovaX 系列'}</h3>
            <p>${isEnglish ? 'Research inspiration and planning' : '研究思路启发和规划'}</p>
            <div class="app-links">
              <a href="${isEnglish ? '/en' : '/zh'}/novax-base">Base</a>
              <a href="${isEnglish ? '/en' : '/zh'}/novax-pro">Pro</a>
              <a href="${isEnglish ? '/en' : '/zh'}/novax-ultra">Ultra</a>
            </div>
          </div>

          <div class="app-card elavax-series">
            <h3>${isEnglish ? 'ElaVaX Series' : 'ElaVaX 系列'}</h3>
            <p>${isEnglish ? 'Medical data analysis and visualization' : '医学数据分析和可视化'}</p>
            <div class="app-links">
              <a href="${isEnglish ? '/en' : '/zh'}/elavax-base">Base</a>
              <a href="${isEnglish ? '/en' : '/zh'}/elavax-pro">Pro</a>
              <a href="${isEnglish ? '/en' : '/zh'}/elavax-ultra">Ultra</a>
            </div>
          </div>
        </div>
      </div>
    `;
  } else {
    // 应用特定页面
    const appInfo = getAppInfo(route);
    return `
      <!-- 应用特定主要内容 -->
      <div class="app-main-content ${appInfo.className}">
        <div class="app-intro">
          <h2>${appInfo.title}</h2>
          <p class="app-description">${appInfo.description}</p>
        </div>

        <!-- 聊天界面 -->
        <div class="chat-interface">
          <div class="chat-messages">
            <div class="welcome-message">
              <p>${isEnglish ? 'Hello! How can I help you today?' : '您好！我今天可以为您做些什么？'}</p>
            </div>
          </div>

          <div class="chat-input-container">
            <div class="input-wrapper">
              <textarea
                placeholder="${isEnglish ? 'Type your question here...' : '请在此输入您的问题...'}"
                class="chat-input"
                rows="3"
                disabled
              ></textarea>
              <button class="send-button" disabled>
                ${isEnglish ? 'Send' : '发送'}
              </button>
            </div>
          </div>
        </div>

        <!-- 建议问题 -->
        <div class="suggested-questions">
          <h3>${isEnglish ? 'Suggested Questions' : '建议问题'}</h3>
          <div class="questions-grid">
            ${appInfo.questions.map(q => `
              <button class="question-button" disabled>${q}</button>
            `).join('')}
          </div>
        </div>

        <!-- 功能特性 -->
        <div class="app-features">
          <h3>${isEnglish ? 'Key Features' : '核心功能'}</h3>
          <div class="features-grid">
            ${appInfo.features.map(feature => `
              <div class="feature-card">
                <div class="feature-icon">${feature.icon}</div>
                <h4>${feature.title}</h4>
                <p>${feature.description}</p>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }
}

/**
 * 生成静态内容结构 - 使用真实的 React 渲染内容
 */
async function generateStaticContent(route, metadata) {
  // 尝试获取真实的渲染内容
  const realContent = await getRealRenderedContent(route);

  if (realContent) {
    // 如果成功获取真实内容，使用它
    // 使用CSS类而不是内联样式，便于SEO和后续控制
    return `<div class="prerender-content" data-prerendered="true">${realContent}</div>`;
  }

  // 如果获取失败，回退到静态内容
  console.log(`⚠️  回退到静态内容: ${route}`);
  return generateFallbackContent(route, metadata);
}

/**
 * 生成回退的静态内容
 */
function generateFallbackContent(route, metadata) {
  const isEnglish = route.includes('/en/');

  // 生成简化的静态内容作为回退
  let staticContent = `
    <div class="prerender-content" data-prerendered="true">
      <div class="prerender-fallback">
        <h1>${metadata.title}</h1>
        <p>${metadata.description}</p>

        <!-- 应用导航 -->
        <nav class="app-navigation">
          <ul>
            <li><a href="${isEnglish ? '/en' : '/zh'}/novax-base">NovaX Base</a></li>
            <li><a href="${isEnglish ? '/en' : '/zh'}/novax-pro">NovaX Pro</a></li>
            <li><a href="${isEnglish ? '/en' : '/zh'}/novax-ultra">NovaX Ultra</a></li>
            <li><a href="${isEnglish ? '/en' : '/zh'}/elavax-base">ElaVaX Base</a></li>
            <li><a href="${isEnglish ? '/en' : '/zh'}/elavax-pro">ElaVaX Pro</a></li>
            <li><a href="${isEnglish ? '/en' : '/zh'}/elavax-ultra">ElaVaX Ultra</a></li>
          </ul>
        </nav>
  `;

  // 根据路由添加特定的应用内容
  if (route.includes('novax-base')) {
    staticContent += `
      <!-- NovaX Base 应用内容 -->
      <div class="app-content novax-base">
        <h2>${isEnglish ? 'Core Idea Inspiration' : '核心思路启发'}</h2>
        <p>${isEnglish
          ? 'Single research direction design based on research background.'
          : '基于研究背景进行单次科研方向思路设计。'}</p>

        <!-- 建议问题 -->
        <div class="suggested-questions">
          <h3>${isEnglish ? 'Suggested Questions' : '建议问题'}</h3>
          <ul>
            <li>${isEnglish ? 'How to design a research proposal?' : '如何设计研究方案？'}</li>
            <li>${isEnglish ? 'What are the key elements of medical research?' : '医学研究的关键要素有哪些？'}</li>
            <li>${isEnglish ? 'How to choose appropriate research methods?' : '如何选择合适的研究方法？'}</li>
          </ul>
        </div>

        <!-- 案例示例 -->
        <div class="case-examples">
          <h3>${isEnglish ? 'Case Examples' : '案例示例'}</h3>
          <div class="case-item">
            <h4>${isEnglish ? 'Clinical Research Design' : '临床研究设计'}</h4>
            <p>${isEnglish
              ? 'Example of designing a randomized controlled trial for medical intervention.'
              : '医学干预随机对照试验设计示例。'}</p>
          </div>
        </div>
      </div>
    `;
  } else if (route.includes('elavax-base')) {
    staticContent += `
      <!-- ElaVaX Base 应用内容 -->
      <div class="app-content elavax-base">
        <h2>${isEnglish ? 'Medical Data Analysis' : '医学数据分析'}</h2>
        <p>${isEnglish
          ? 'Professional medical data analysis, statistical analysis, data visualization, and research design services.'
          : '专业的医学数据分析，包括统计分析、数据可视化、研究设计等服务。'}</p>

        <!-- 分析功能 -->
        <div class="analysis-features">
          <h3>${isEnglish ? 'Analysis Features' : '分析功能'}</h3>
          <ul>
            <li>${isEnglish ? 'Statistical Analysis' : '统计分析'}</li>
            <li>${isEnglish ? 'Data Visualization' : '数据可视化'}</li>
            <li>${isEnglish ? 'Research Design' : '研究设计'}</li>
            <li>${isEnglish ? 'Result Interpretation' : '结果解读'}</li>
          </ul>
        </div>
      </div>
    `;
  } else if (route.includes('/login')) {
    staticContent += `
      <!-- 登录页面内容 -->
      <div class="login-content">
        <h2>${isEnglish ? 'Login to Medxy AI' : '登录 Medxy AI'}</h2>
        <p>${isEnglish
          ? 'Access professional medical AI tools and services.'
          : '访问专业的医学AI工具和服务。'}</p>

        <div class="login-features">
          <h3>${isEnglish ? 'Platform Features' : '平台功能'}</h3>
          <ul>
            <li>${isEnglish ? 'Medical Writing Assistant' : '医学写作助手'}</li>
            <li>${isEnglish ? 'Data Analysis Tools' : '数据分析工具'}</li>
            <li>${isEnglish ? 'Research Planning' : '研究规划'}</li>
            <li>${isEnglish ? 'Professional Consultation' : '专业咨询'}</li>
          </ul>
        </div>
      </div>
    `;
  } else {
    // 首页或其他页面的默认内容
    staticContent += `
      <!-- 首页内容 -->
      <div class="home-content">
        <h2>${isEnglish ? 'Medical AI Agent Platform' : '医学AI智能体平台'}</h2>
        <p>${isEnglish
          ? 'Professional medical AI tools for research, writing, and analysis.'
          : '专业的医学AI工具，用于研究、写作和分析。'}</p>

        <!-- 主要功能 -->
        <div class="main-features">
          <h3>${isEnglish ? 'Main Features' : '主要功能'}</h3>
          <div class="feature-grid">
            <div class="feature-item">
              <h4>NovaX ${isEnglish ? 'Series' : '系列'}</h4>
              <p>${isEnglish ? 'Research idea inspiration and planning' : '研究思路启发和规划'}</p>
            </div>
            <div class="feature-item">
              <h4>ElaVaX ${isEnglish ? 'Series' : '系列'}</h4>
              <p>${isEnglish ? 'Medical data analysis and visualization' : '医学数据分析和可视化'}</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  staticContent += `
              <!-- 聊天界面占位 -->
              <div class="chat-interface-placeholder">
                <div class="chat-input-area">
                  <textarea placeholder="${isEnglish ? 'Enter your question...' : '请输入您的问题...'}" class="chat-input" disabled></textarea>
                  <button class="send-button" disabled>${isEnglish ? 'Send' : '发送'}</button>
                </div>
              </div>

              <!-- 加载指示器 -->
              <div class="app-loading">
                <div class="loading-spinner"></div>
                <p>${isEnglish ? 'Loading application...' : '正在加载应用...'}</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  `;

  return staticContent;
}

/**
 * 修复资源路径问题 - 将js文件路径改为带语言和应用名称的绝对路径
 */
function fixResourcePaths(document, route) {
  // 计算相对路径深度（用于assets等资源）
  const pathSegments = route.split('/').filter(Boolean);
  const depth = pathSegments.length;
  const relativePath = depth > 0 ? '../'.repeat(depth) : './';

  // 修复 script 标签的 src 属性
  const scriptTags = document.querySelectorAll('script[src]');

  scriptTags.forEach((script) => {
    const src = script.getAttribute('src');

    if (src) {
      // 处理相对路径 ./
      if (src.startsWith('./')) {
        const cleanPath = src.replace(/^\.\//, '');

        // 如果是js文件或assets文件，使用带路由的绝对路径
        if (cleanPath.startsWith('js/') || cleanPath.startsWith('assets/')) {
          script.setAttribute('src', `${route}/${cleanPath}`);
        } else {
          // 其他资源使用相对路径
          script.setAttribute('src', relativePath + cleanPath);
        }
      }
      // 处理已经是相对路径的情况（如 ../../assets/）
      else if (src.match(/^\.\.\/.*assets\//)) {
        const cleanPath = src.replace(/^(\.\.\/)+/, '');
        // 如果是 assets 文件，也转换为绝对路径
        if (cleanPath.startsWith('assets/')) {
          script.setAttribute('src', `${route}/${cleanPath}`);
        } else {
          script.setAttribute('src', relativePath + cleanPath);
        }
      }
    }
  });

  // 修复 link 标签的 href 属性 (CSS 文件)
  const linkTags = document.querySelectorAll('link[href]');
  linkTags.forEach(link => {
    const href = link.getAttribute('href');
    if (href && !href.startsWith('http')) {
      // 处理相对路径 ./
      if (href.startsWith('./')) {
        const cleanPath = href.replace(/^\.\//, '');
        // 如果是 assets 文件，转换为绝对路径
        if (cleanPath.startsWith('assets/')) {
          link.setAttribute('href', `${route}/${cleanPath}`);
        } else {
          link.setAttribute('href', relativePath + cleanPath);
        }
      }
      // 处理已经是相对路径的情况（如 ../../assets/）
      else if (href.match(/^\.\.\/.*assets\//)) {
        const cleanPath = href.replace(/^(\.\.\/)+/, '');
        // 如果是 assets 文件，也转换为绝对路径
        if (cleanPath.startsWith('assets/')) {
          link.setAttribute('href', `${route}/${cleanPath}`);
        } else {
          link.setAttribute('href', relativePath + cleanPath);
        }
      }
    }
  });

  // 字符串替换作为备用方案
  const htmlContent = document.documentElement.outerHTML;
  let fixedContent = htmlContent
    // 处理原始的相对路径
    .replace(/src="\.\/assets\//g, `src="${relativePath}assets/`)
    .replace(/href="\.\/assets\//g, `href="${relativePath}assets/`)
    .replace(/src="\.\/js\//g, `src="${route}/js/`)
    // 处理已经被处理过的相对路径（如 ../../assets/）
    .replace(/src="(\.\.\/)+assets\//g, `src="${relativePath}assets/`)
    .replace(/href="(\.\.\/)+assets\//g, `href="${relativePath}assets/`);

  if (fixedContent !== htmlContent) {
    const newDom = new (document.defaultView.DOMParser)().parseFromString(fixedContent, 'text/html');
    document.documentElement.innerHTML = newDom.documentElement.innerHTML;
  }
}

/**
 * 生成预渲染的 HTML 内容
 */
async function generatePrerenderedHTML(route, template) {
  const metadata = generateSEOMetadata(route);
  
  // 使用 JSDOM 解析 HTML
  const dom = new JSDOM(template);
  const document = dom.window.document;

  // 更新页面标题
  const titleElement = document.querySelector('title');
  if (titleElement) {
    titleElement.textContent = metadata.title;
  }

  // 更新 meta 标签
  const updateMetaTag = (name, content, attribute = 'name') => {
    let metaTag = document.querySelector(`meta[${attribute}="${name}"]`);
    if (metaTag) {
      metaTag.setAttribute('content', content);
    } else {
      metaTag = document.createElement('meta');
      metaTag.setAttribute(attribute, name);
      metaTag.setAttribute('content', content);
      document.head.appendChild(metaTag);
    }
  };

  // 更新基础 meta 标签
  updateMetaTag('description', metadata.description);
  updateMetaTag('keywords', metadata.keywords);

  // 更新 Open Graph 标签
  updateMetaTag('og:title', metadata.ogTitle, 'property');
  updateMetaTag('og:description', metadata.ogDescription, 'property');
  updateMetaTag('og:image', metadata.ogImage, 'property');
  updateMetaTag('og:type', 'website', 'property');

  // 更新 Twitter 标签
  updateMetaTag('twitter:title', metadata.ogTitle, 'name');
  updateMetaTag('twitter:description', metadata.ogDescription, 'name');
  updateMetaTag('twitter:image', metadata.ogImage, 'name');
  updateMetaTag('twitter:card', 'summary_large_image', 'name');

  // 添加结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": metadata.title,
    "description": metadata.description,
    "url": `https://ai.medsci.cn${route}`,
    "applicationCategory": "HealthApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "CNY"
    }
  };

  const scriptTag = document.createElement('script');
  scriptTag.type = 'application/ld+json';
  scriptTag.textContent = JSON.stringify(structuredData, null, 2);
  document.head.appendChild(scriptTag);

  // 添加预渲染控制样式
  const prerenderStyle = document.createElement('style');
  prerenderStyle.textContent = `
    /* 预渲染内容控制 - 默认隐藏，避免与React hydration冲突 */
    .prerender-content {
      display: none;
    }

    /* 只有在JavaScript未加载时才显示预渲染内容（用于SEO） */
    .no-js .prerender-content {
      display: block;
    }

    /* 防止内容闪烁 */
    .prerender-content {
      transition: none;
    }
  `;
  document.head.appendChild(prerenderStyle);

  // 添加noscript标签来处理JavaScript禁用的情况
  const noscriptStyle = document.createElement('noscript');
  noscriptStyle.innerHTML = `
    <style>
      .prerender-content {
        display: block !important;
      }
    </style>
  `;
  document.head.appendChild(noscriptStyle);

  // 修复资源路径问题
  fixResourcePaths(document, route);

  // 在 root div 中添加预渲染内容
  const rootDiv = document.getElementById('root');
  if (rootDiv) {
    // 清空root内容，避免与React hydration冲突
    rootDiv.innerHTML = '';

    // 在root外部添加预渲染内容，仅用于SEO
    const staticContent = await generateStaticContent(route, metadata);

    // 创建一个隐藏的SEO内容容器
    const seoContainer = document.createElement('div');
    seoContainer.className = 'prerender-content seo-content';
    seoContainer.innerHTML = staticContent.replace('<div class="prerender-content" data-prerendered="true">', '').replace('</div>', '');

    // 将SEO内容添加到body末尾，而不是root内部
    document.body.appendChild(seoContainer);
  }

  return dom.serialize();
}

/**
 * 清理和格式化 HTML（用于生产环境）
 */
function cleanHTML(html) {
  // 移除开发时的调试属性
  html = html.replace(/\s*data-react-source="[^"]*"/g, '');

  // 移除多余的空白，但保持基本的可读性
  html = html
    .replace(/\s+/g, ' ')
    .replace(/>\s+</g, '>\n<')
    .replace(/\s+>/g, '>')
    .replace(/<\s+/g, '<')
    .trim();

  // 为主要标签添加换行，提高可读性
  html = html
    .replace(/<head>/g, '\n<head>')
    .replace(/<\/head>/g, '\n</head>\n')
    .replace(/<body>/g, '\n<body>')
    .replace(/<\/body>/g, '\n</body>\n')
    .replace(/<title>/g, '\n  <title>')
    .replace(/<meta/g, '\n  <meta')
    .replace(/<link/g, '\n  <link')
    .replace(/<script/g, '\n  <script');

  return html;
}

/**
 * 确保目录存在
 */
function ensureDirectoryExists(filePath) {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

/**
 * 获取多语言案例数据
 */
async function getCasesData() {
  try {
    // 从环境变量获取API基础URL，如果没有则根据环境判断
    const reactAppEnv = process.env.REACT_APP_ENV || mode;
    const apiBaseUrl = process.env.VITE_API_BASE_URL ||
                      (reactAppEnv === 'production' ? 'https://ai.medsci.cn/' :
                       reactAppEnv === 'international' ? 'https://medxy.ai/' :
                       reactAppEnv === 'test' ? 'https://ai.medon.com.cn/' :
                       'https://ai.medon.com.cn/');

    console.log(`🌐 当前环境: ${reactAppEnv}, API基础URL: ${apiBaseUrl}`);

    // 支持的语言列表
    const languages = ['zh', 'en'];
    const allCases = [];

    // 为每种语言创建独立的API调用
    for (const lang of languages) {
      try {
        console.log(`📡 获取 ${lang} 语言的案例数据...`);

        // 为每种语言添加语言参数
        const url = `${apiBaseUrl}dev-api/ai-base/index/getConfigByKeyFromCache?configKey=case_examples&locale=${lang}`;
        const response = await fetch(url);

        if (!response.ok) {
          console.warn(`⚠️  ${lang} 语言的API请求失败: ${response.status}`);
          continue;
        }

        const result = await response.json();

        if (result.code === 0 && result.data) {
          // 遍历所有应用类型
          Object.keys(result.data).forEach(appType => {
            const appCases = result.data[appType];
            if (Array.isArray(appCases)) {
              // 为每个案例添加应用类型和语言信息
              appCases.forEach(caseItem => {
                allCases.push({
                  ...caseItem,
                  appType: appType,
                  language: lang
                });
              });
            }
          });

          console.log(`✅ 成功获取 ${lang} 语言的案例数据`);
        } else {
          console.warn(`⚠️  ${lang} 语言的API返回数据格式错误`);
        }
      } catch (error) {
        console.error(`❌ 获取 ${lang} 语言数据失败:`, error.message);
      }
    }

    console.log(`🎯 总共获取 ${allCases.length} 个案例`);
    return allCases;
  } catch (error) {
    console.error('❌ 获取案例数据失败:', error.message);
    return [];
  }
}

/**
 * 生成 sitemap.xml
 */
async function generateSitemap() {
  // 根据构建模式确定基础URL
  const baseUrl = getApiBaseUrl(mode);

  // 获取多语言案例数据
  const casesData = await getCasesData();

  // 基础路由 - 使用动态获取的路由
  const baseRoutes = config.routes.map(route => `  <url>
    <loc>${baseUrl}${route}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${route === '/' ? '1.0' : '0.8'}</priority>
  </url>`);

  // 案例路由 - 根据语言信息生成对应的路由
  const caseRoutes = [];
  casesData.forEach(caseItem => {
    if (caseItem.id && caseItem.language) {
      // 根据案例的语言信息生成对应的路由
      caseRoutes.push(`  <url>
    <loc>${baseUrl}/${caseItem.language}/cases/${caseItem.id}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>`);
    }
  });

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${baseRoutes.join('\n')}
${caseRoutes.join('\n')}
</urlset>`;

  const sitemapPath = path.join(config.distDir, 'sitemap.xml');
  fs.writeFileSync(sitemapPath, sitemap, 'utf-8');

  // 统计信息
  const zhCases = casesData.filter(item => item.language === 'zh').length;
  const enCases = casesData.filter(item => item.language === 'en').length;

  console.log(`✅ 生成 sitemap.xml (包含 ${baseRoutes.length} 个基础路由和 ${caseRoutes.length} 个案例路由)`);
  console.log(`   案例分布: 中文 ${zhCases} 个, 英文 ${enCases} 个`);
  console.log(`🌐 使用基础URL: ${baseUrl}`);
}

/**
 * 主预渲染函数
 */
async function prerender() {
  console.log('🚀 开始预渲染...');
  console.log(`🔍 当前构建模式: ${mode}`);

  // 检查 dist 目录是否存在
  if (!fs.existsSync(config.distDir)) {
    console.error('❌ dist 目录不存在，请先运行构建命令');
    process.exit(1);
  }

  // 从API动态获取路由配置
  console.log('📡 正在从API获取路由配置...');
  const routes = await getRoutesFromAPI(mode);
  config.routes = routes;

  console.log(`📋 将要预渲染的路由 (${routes.length} 个):`);
  routes.forEach(route => console.log(`   - ${route}`));

  // 读取 HTML 模板
  const template = readTemplate();
  console.log('📄 读取 HTML 模板成功');

  // 预渲染每个路由
  for (const route of config.routes) {
    try {
      const html = await generatePrerenderedHTML(route, template);
      const cleanedHTML = cleanHTML(html);

      let outputPath;
      if (route === '/') {
        outputPath = path.join(config.distDir, 'index.html');
      } else {
        outputPath = path.join(config.distDir, route, 'index.html');
      }

      ensureDirectoryExists(outputPath);
      fs.writeFileSync(outputPath, cleanedHTML, 'utf-8');
      console.log(`✅ ${route}`);

    } catch (error) {
      console.error(`❌ ${route}: ${error.message}`);
    }
  }

  // 生成 sitemap
  await generateSitemap();

  console.log('🎉 预渲染完成！');
}

// 运行预渲染
prerender().catch(error => {
  console.error('❌ 预渲染失败:', error);
  process.exit(1);
});
