#!/usr/bin/env node

/**
 * 复制 novax.html 文件到 dist 目录的脚本
 * 确保在国际化构建时，novax.html 文件能够被正确复制到构建输出目录
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 获取命令行参数中的目标目录
const args = process.argv.slice(2);
const targetDirArg = args.find(arg => arg.startsWith('--target'));
let targetDir = 'dist';

if (targetDirArg) {
  if (targetDirArg.includes('=')) {
    targetDir = targetDirArg.split('=')[1];
  } else {
    // 如果是 --target dist_international 这种格式
    const targetIndex = args.indexOf(targetDirArg);
    if (targetIndex !== -1 && args[targetIndex + 1]) {
      targetDir = args[targetIndex + 1];
    }
  }
}

// 配置路径
const config = {
  sourceFile: path.join(__dirname, '../public/novax.html'),
  distDir: path.join(__dirname, `../${targetDir}`),
  targetFile: path.join(__dirname, `../${targetDir}/novax.html`)
};

/**
 * 确保目录存在
 */
function ensureDirectoryExists(filePath) {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 创建目录: ${dir}`);
  }
}

/**
 * 复制 novax.html 文件
 */
function copyNovaxHtml() {
  console.log('🚀 开始复制 novax.html 文件...');

  // 检查源文件是否存在
  if (!fs.existsSync(config.sourceFile)) {
    console.error(`❌ 源文件不存在: ${config.sourceFile}`);
    process.exit(1);
  }

  // 检查 dist 目录是否存在
  if (!fs.existsSync(config.distDir)) {
    console.error(`❌ dist 目录不存在: ${config.distDir}`);
    console.error('请先运行构建命令生成 dist 目录');
    process.exit(1);
  }

  try {
    // 确保目标目录存在
    ensureDirectoryExists(config.targetFile);

    // 复制文件
    fs.copyFileSync(config.sourceFile, config.targetFile);
    
    console.log(`✅ 成功复制 novax.html`);
    console.log(`   源文件: ${config.sourceFile}`);
    console.log(`   目标文件: ${config.targetFile}`);

    // 验证文件是否复制成功
    if (fs.existsSync(config.targetFile)) {
      const stats = fs.statSync(config.targetFile);
      console.log(`📊 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log('🎉 novax.html 复制完成！');
    } else {
      console.error('❌ 文件复制验证失败');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ 复制文件时发生错误:', error.message);
    process.exit(1);
  }
}

// 运行复制操作
copyNovaxHtml();
