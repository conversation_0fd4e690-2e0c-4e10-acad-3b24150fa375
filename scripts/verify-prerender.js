#!/usr/bin/env node

/**
 * 预渲染验证脚本
 * 检查预渲染是否成功，验证HTML文件是否包含正确的内容和元数据
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置
const config = {
  distDir: path.join(__dirname, '../dist'),
  routes: [
    '/',
    '/zh/novax-base',
    '/zh/elavax-base',
    '/zh/novax-pro',
    '/zh/novax-ultra',
    '/zh/elavax-pro',
    '/zh/elavax-ultra',
    '/en/novax-base',
    '/en/elavax-base',
    '/en/novax-pro',
    '/en/novax-ultra',
    '/en/elavax-pro',
    '/en/elavax-ultra',
    '/zh/login',
    '/en/login'
  ]
};

/**
 * 验证单个HTML文件
 */
function verifyHTMLFile(filePath, route) {
  const results = {
    route,
    filePath,
    exists: false,
    hasTitle: false,
    hasDescription: false,
    hasKeywords: false,
    hasOgTags: false,
    hasTwitterTags: false,
    hasStructuredData: false,
    hasPrerenderedContent: false,
    errors: [],
    warnings: []
  };

  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      results.errors.push('HTML文件不存在');
      return results;
    }
    results.exists = true;

    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf-8');

    // 检查基础元数据
    if (content.includes('<title>') && !content.includes('<title></title>')) {
      results.hasTitle = true;
    } else {
      results.errors.push('缺少页面标题或标题为空');
    }

    if (content.includes('name="description"') && content.includes('content=')) {
      results.hasDescription = true;
    } else {
      results.warnings.push('缺少页面描述');
    }

    if (content.includes('name="keywords"') && content.includes('content=')) {
      results.hasKeywords = true;
    } else {
      results.warnings.push('缺少关键词');
    }

    // 检查Open Graph标签
    if (content.includes('property="og:title"') && content.includes('property="og:description"')) {
      results.hasOgTags = true;
    } else {
      results.warnings.push('缺少Open Graph标签');
    }

    // 检查Twitter标签
    if (content.includes('name="twitter:title"') && content.includes('name="twitter:description"')) {
      results.hasTwitterTags = true;
    } else {
      results.warnings.push('缺少Twitter标签');
    }

    // 检查结构化数据
    if (content.includes('application/ld+json')) {
      results.hasStructuredData = true;
    } else {
      results.warnings.push('缺少结构化数据');
    }

    // 检查预渲染内容
    if (content.includes('data-prerendered="true"')) {
      results.hasPrerenderedContent = true;
    } else {
      results.warnings.push('缺少预渲染内容标记');
    }

    // 检查特定路由的内容
    if (route.includes('novax-base')) {
      if (!content.includes('NovaX Base') && !content.includes('核心思路启发')) {
        results.warnings.push('缺少NovaX Base特定内容');
      }
    } else if (route.includes('elavax-base')) {
      if (!content.includes('ElaVaX Base') && !content.includes('医学数据分析')) {
        results.warnings.push('缺少ElaVaX Base特定内容');
      }
    }

    // 检查语言特定内容
    if (route.includes('/en/')) {
      if (!content.includes('Medical AI') && !content.includes('Medxy AI')) {
        results.warnings.push('缺少英文内容');
      }
    } else if (route.includes('/zh/')) {
      if (!content.includes('医学AI') && !content.includes('梅斯小智')) {
        results.warnings.push('缺少中文内容');
      }
    }

  } catch (error) {
    results.errors.push(`读取文件失败: ${error.message}`);
  }

  return results;
}

/**
 * 生成验证报告
 */
function generateReport(results) {
  console.log('\n🔍 预渲染验证报告');
  console.log('='.repeat(50));

  let totalFiles = results.length;
  let successFiles = 0;
  let warningFiles = 0;
  let errorFiles = 0;

  results.forEach(result => {
    const { route, exists, hasTitle, hasDescription, errors, warnings } = result;
    
    console.log(`\n📄 路由: ${route}`);
    
    if (!exists) {
      console.log('❌ 文件不存在');
      errorFiles++;
      return;
    }

    if (errors.length > 0) {
      console.log('❌ 错误:');
      errors.forEach(error => console.log(`   - ${error}`));
      errorFiles++;
    } else if (warnings.length > 0) {
      console.log('⚠️  警告:');
      warnings.forEach(warning => console.log(`   - ${warning}`));
      warningFiles++;
    } else {
      console.log('✅ 验证通过');
      successFiles++;
    }

    // 显示详细信息
    const details = [];
    if (hasTitle) details.push('标题✓');
    if (hasDescription) details.push('描述✓');
    if (result.hasKeywords) details.push('关键词✓');
    if (result.hasOgTags) details.push('OG标签✓');
    if (result.hasTwitterTags) details.push('Twitter标签✓');
    if (result.hasStructuredData) details.push('结构化数据✓');
    if (result.hasPrerenderedContent) details.push('预渲染内容✓');

    if (details.length > 0) {
      console.log(`   包含: ${details.join(', ')}`);
    }
  });

  // 总结
  console.log('\n📊 验证总结');
  console.log('='.repeat(30));
  console.log(`总文件数: ${totalFiles}`);
  console.log(`✅ 成功: ${successFiles}`);
  console.log(`⚠️  警告: ${warningFiles}`);
  console.log(`❌ 错误: ${errorFiles}`);

  const successRate = ((successFiles + warningFiles) / totalFiles * 100).toFixed(1);
  console.log(`📈 成功率: ${successRate}%`);

  if (errorFiles > 0) {
    console.log('\n❌ 存在错误，请检查预渲染配置');
    process.exit(1);
  } else if (warningFiles > 0) {
    console.log('\n⚠️  存在警告，建议优化');
  } else {
    console.log('\n🎉 所有文件验证通过！');
  }
}

/**
 * 主验证函数
 */
function verifyPrerender() {
  console.log('🚀 开始验证预渲染结果...');

  const results = [];

  config.routes.forEach(route => {
    let filePath;
    if (route === '/') {
      filePath = path.join(config.distDir, 'index.html');
    } else {
      filePath = path.join(config.distDir, route, 'index.html');
    }

    const result = verifyHTMLFile(filePath, route);
    results.push(result);
  });

  generateReport(results);
}

// 运行验证
verifyPrerender();
