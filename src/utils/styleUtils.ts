/**
 * 样式处理工具函数
 * 用于安全地提取、清理和注入HTML中的CSS样式
 */

// 危险的CSS属性列表（防止XSS攻击）
const DANGEROUS_CSS_PROPERTIES = [
  'expression',
  'javascript',
  'vbscript',
  'onload',
  'onerror',
  'onclick',
  'onmouseover',
  'onfocus',
  'onblur',
  'onchange',
  'onsubmit',
  'onreset',
  'onselect',
  'onunload',
  'behavior',
  'binding',
  '-moz-binding',
  'filter',
  'zoom'
];

// 危险的CSS值模式
const DANGEROUS_CSS_VALUES = [
  /javascript:/i,
  /vbscript:/i,
  /expression\s*\(/i,
  /url\s*\(\s*javascript:/i,
  /url\s*\(\s*vbscript:/i,
  /url\s*\(\s*data:.*script/i,
  /@import/i,
  /behavior:/i,
  /binding:/i,
  /-moz-binding:/i
];

/**
 * 清理CSS内容，移除危险的属性和值
 * @param cssContent CSS内容字符串
 * @returns 清理后的安全CSS内容
 */
export const sanitizeCSS = (cssContent: string): string => {
  if (!cssContent || typeof cssContent !== 'string') {
    return '';
  }

  let cleanCSS = cssContent;

  // 移除注释（防止注释中隐藏恶意代码）
  cleanCSS = cleanCSS.replace(/\/\*[\s\S]*?\*\//g, '');

  // 检查并移除危险的CSS值
  DANGEROUS_CSS_VALUES.forEach(pattern => {
    cleanCSS = cleanCSS.replace(pattern, '');
  });

  // 移除危险的CSS属性
  DANGEROUS_CSS_PROPERTIES.forEach(prop => {
    const regex = new RegExp(`\\b${prop}\\s*:.*?(?:;|$)`, 'gi');
    cleanCSS = cleanCSS.replace(regex, '');
  });

  // 移除@import规则（防止加载外部恶意样式）
  cleanCSS = cleanCSS.replace(/@import\s+[^;]+;?/gi, '');

  // 移除@charset规则（不需要）
  cleanCSS = cleanCSS.replace(/@charset\s+[^;]+;?/gi, '');

  // 清理多余的空白
  cleanCSS = cleanCSS.replace(/\s+/g, ' ').trim();

  return cleanCSS;
};

/**
 * 从HTML内容中提取style标签
 * @param htmlContent HTML内容字符串
 * @returns 提取的样式内容数组
 */
export const extractStyleTags = (htmlContent: string): string[] => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return [];
  }

  const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
  const styles: string[] = [];
  let match;

  while ((match = styleRegex.exec(htmlContent)) !== null) {
    const styleContent = match[1];
    if (styleContent && styleContent.trim()) {
      // 清理并验证样式内容
      const cleanStyle = sanitizeCSS(styleContent);
      if (cleanStyle) {
        styles.push(cleanStyle);
      }
    }
  }

  return styles;
};

/**
 * 移除HTML内容中的style标签
 * @param htmlContent HTML内容字符串
 * @returns 移除style标签后的HTML内容
 */
export const removeStyleTags = (htmlContent: string): string => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent;
  }

  return htmlContent.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
};

/**
 * 为CSS选择器添加作用域前缀，避免全局样式污染
 * @param cssContent CSS内容
 * @param scopePrefix 作用域前缀
 * @returns 添加作用域后的CSS内容
 */
export const addCSSScope = (cssContent: string, scopePrefix: string): string => {
  if (!cssContent || !scopePrefix) {
    return cssContent;
  }

  // 简单的CSS选择器作用域处理
  // 这里使用基本的正则表达式，对于复杂的CSS可能需要更完善的解析器
  return cssContent.replace(/([^{}]+)\s*{/g, (match, selector) => {
    // 清理选择器
    const cleanSelector = selector.trim();
    
    // 跳过@规则（如@media, @keyframes等）
    if (cleanSelector.startsWith('@')) {
      return match;
    }

    // 为每个选择器添加作用域前缀
    const scopedSelectors = cleanSelector
      .split(',')
      .map((sel: string) => {
        const trimmedSel = sel.trim();
        // 如果选择器已经包含作用域前缀，则不重复添加
        if (trimmedSel.startsWith(scopePrefix)) {
          return trimmedSel;
        }
        return `${scopePrefix} ${trimmedSel}`;
      })
      .join(', ');

    return `${scopedSelectors} {`;
  });
};

/**
 * 动态注入CSS样式到页面头部
 * @param cssContent CSS内容
 * @param styleId 样式元素的ID（用于后续移除）
 * @returns 创建的style元素
 */
export const injectCSS = (cssContent: string, styleId: string): HTMLStyleElement | null => {
  if (!cssContent || typeof document === 'undefined') {
    return null;
  }

  // 检查是否已存在相同ID的样式元素
  const existingStyle = document.getElementById(styleId) as HTMLStyleElement;
  if (existingStyle) {
    // 更新现有样式内容
    existingStyle.textContent = cssContent;
    return existingStyle;
  }

  // 创建新的style元素
  const styleElement = document.createElement('style');
  styleElement.id = styleId;
  styleElement.type = 'text/css';
  styleElement.textContent = cssContent;

  // 添加到head中
  document.head.appendChild(styleElement);

  return styleElement;
};

/**
 * 移除注入的CSS样式
 * @param styleId 样式元素的ID
 */
export const removeInjectedCSS = (styleId: string): void => {
  if (typeof document === 'undefined') {
    return;
  }

  const styleElement = document.getElementById(styleId);
  if (styleElement && styleElement.parentNode) {
    styleElement.parentNode.removeChild(styleElement);
  }
};

/**
 * 生成唯一的样式ID
 * @param prefix ID前缀
 * @returns 唯一的样式ID
 */
export const generateStyleId = (prefix: string = 'ai-response-style'): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * 处理AI回复内容中的样式
 * 提取样式并注入到页面，返回清理后的HTML内容
 * @param htmlContent HTML内容
 * @param scopePrefix 作用域前缀
 * @returns 处理结果对象
 */
export interface StyleProcessResult {
  cleanedHTML: string;
  injectedStyles: string[];
  styleIds: string[];
}

export const processAIResponseStyles = (
  htmlContent: string, 
  scopePrefix: string = '.ai-response-container'
): StyleProcessResult => {
  // 提取样式
  const extractedStyles = extractStyleTags(htmlContent);
  
  // 移除原始的style标签
  const cleanedHTML = removeStyleTags(htmlContent);
  
  const injectedStyles: string[] = [];
  const styleIds: string[] = [];

  // 处理并注入每个样式
  extractedStyles.forEach((style, index) => {
    // 添加作用域
    const scopedStyle = addCSSScope(style, scopePrefix);
    
    // 生成唯一ID并注入
    const styleId = generateStyleId(`ai-response-style-${index}`);
    const injectedElement = injectCSS(scopedStyle, styleId);
    
    if (injectedElement) {
      injectedStyles.push(scopedStyle);
      styleIds.push(styleId);
    }
  });

  return {
    cleanedHTML,
    injectedStyles,
    styleIds
  };
};
