/**
 * 全局缓存管理工具
 * 
 * 提供统一的缓存管理功能，包括清理、统计、监控等
 */

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** 总缓存键数量 */
  totalKeys: number;
  /** 配置缓存键数量 */
  configCacheKeys: number;
  /** 总缓存大小（字节） */
  totalSize: number;
  /** 最旧缓存时间 */
  oldestCache: Date | null;
  /** 最新缓存时间 */
  newestCache: Date | null;
  /** 按应用分组的统计 */
  appStats: Record<string, {
    keys: number;
    size: number;
    languages: string[];
  }>;
}

/**
 * 缓存清理选项
 */
export interface ClearCacheOptions {
  /** 应用英文名称 */
  appEN?: string;
  /** 语言代码 */
  language?: string;
  /** 配置键 */
  configKey?: string;
  /** 是否清理过期缓存 */
  expiredOnly?: boolean;
  /** 缓存过期时间（毫秒） */
  maxAge?: number;
}

/**
 * 获取全局缓存统计信息
 */
export const getGlobalCacheStats = (): CacheStats => {
  const stats: CacheStats = {
    totalKeys: 0,
    configCacheKeys: 0,
    totalSize: 0,
    oldestCache: null,
    newestCache: null,
    appStats: {}
  };

  try {
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (!key) continue;

      stats.totalKeys++;

      if (key.startsWith('config_cache_')) {
        stats.configCacheKeys++;

        try {
          const value = sessionStorage.getItem(key);
          if (value) {
            stats.totalSize += value.length;

            // 解析缓存数据获取时间戳
            const cacheData = JSON.parse(value);
            if (cacheData.timestamp) {
              const cacheDate = new Date(cacheData.timestamp);
              
              if (!stats.oldestCache || cacheDate < stats.oldestCache) {
                stats.oldestCache = cacheDate;
              }
              if (!stats.newestCache || cacheDate > stats.newestCache) {
                stats.newestCache = cacheDate;
              }
            }

            // 解析应用统计信息
            const keyParts = key.split('_');
            if (keyParts.length >= 4) {
              const appEN = keyParts[2];
              const language = keyParts[3];

              if (!stats.appStats[appEN]) {
                stats.appStats[appEN] = {
                  keys: 0,
                  size: 0,
                  languages: []
                };
              }

              stats.appStats[appEN].keys++;
              stats.appStats[appEN].size += value.length;
              
              if (!stats.appStats[appEN].languages.includes(language)) {
                stats.appStats[appEN].languages.push(language);
              }
            }
          }
        } catch (error) {
          console.warn('解析缓存数据失败:', key, error);
        }
      }
    }
  } catch (error) {
    console.error('获取缓存统计失败:', error);
  }

  return stats;
};

/**
 * 清理缓存
 */
export const clearCache = (options: ClearCacheOptions = {}): number => {
  const {
    appEN,
    language,
    configKey,
    expiredOnly = false,
    maxAge = Number.MAX_SAFE_INTEGER
  } = options;

  let clearedCount = 0;
  const keysToRemove: string[] = [];
  const now = Date.now();

  try {
    // 先收集所有键，避免在遍历过程中修改sessionStorage
    const allKeys: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key) {
        allKeys.push(key);
      }
    }

    // 过滤出需要删除的键
    allKeys.forEach(key => {
      if (!key.startsWith('config_cache_')) return;

      // 解析缓存键
      const keyParts = key.split('_');
      if (keyParts.length < 4) return;

      const keyAppEN = keyParts[2];
      const keyLanguage = keyParts[3];
      const keyConfigKey = keyParts.slice(4).join('_');

      // 应用过滤条件
      if (appEN && keyAppEN !== appEN) return;
      if (language && keyLanguage !== language) return;
      if (configKey && keyConfigKey !== configKey) return;

      // 检查是否过期（仅在expiredOnly为true时）
      if (expiredOnly) {
        try {
          const value = sessionStorage.getItem(key);
          if (value) {
            const cacheData = JSON.parse(value);
            const age = now - (cacheData.timestamp || 0);
            if (age < maxAge) return;
          }
        } catch (error) {
          // 解析失败的缓存项也应该被清理
        }
      }

      keysToRemove.push(key);
    });

    // 执行清理
    keysToRemove.forEach(key => {
      sessionStorage.removeItem(key);
      clearedCount++;
    });

    console.log('🗑️ CacheManager - 缓存清理完成', {
      clearedCount,
      options,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('🗑️ CacheManager - 缓存清理失败:', error);
  }

  return clearedCount;
};

/**
 * 清理过期缓存
 */
export const clearExpiredCache = (maxAge: number = 24 * 60 * 60 * 1000): number => {
  return clearCache({
    expiredOnly: true,
    maxAge
  });
};

/**
 * 清理指定应用的所有缓存
 */
export const clearAppCache = (appEN: string): number => {
  return clearCache({ appEN });
};

/**
 * 清理指定语言的所有缓存
 */
export const clearLanguageCache = (language: string): number => {
  return clearCache({ language });
};

/**
 * 获取缓存大小（格式化字符串）
 */
export const getFormattedCacheSize = (sizeInBytes: number): string => {
  if (sizeInBytes < 1024) {
    return `${sizeInBytes}B`;
  } else if (sizeInBytes < 1024 * 1024) {
    return `${(sizeInBytes / 1024).toFixed(1)}KB`;
  } else {
    return `${(sizeInBytes / (1024 * 1024)).toFixed(1)}MB`;
  }
};

/**
 * 打印缓存统计信息到控制台
 */
export const logCacheStats = (): void => {
  const stats = getGlobalCacheStats();
  
  console.group('📊 缓存统计信息');
  console.log('总缓存键数:', stats.totalKeys);
  console.log('配置缓存键数:', stats.configCacheKeys);
  console.log('总缓存大小:', getFormattedCacheSize(stats.totalSize));
  
  if (stats.oldestCache) {
    console.log('最旧缓存:', stats.oldestCache.toLocaleString());
  }
  if (stats.newestCache) {
    console.log('最新缓存:', stats.newestCache.toLocaleString());
  }

  if (Object.keys(stats.appStats).length > 0) {
    console.group('按应用分组:');
    Object.entries(stats.appStats).forEach(([appEN, appStat]) => {
      console.log(`${appEN}:`, {
        keys: appStat.keys,
        size: getFormattedCacheSize(appStat.size),
        languages: appStat.languages.join(', ')
      });
    });
    console.groupEnd();
  }

  console.groupEnd();
};

/**
 * 监控缓存使用情况
 */
export const monitorCacheUsage = (): void => {
  const stats = getGlobalCacheStats();
  
  // 检查缓存大小是否过大（超过5MB）
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (stats.totalSize > maxSize) {
    console.warn('⚠️ 缓存大小过大:', getFormattedCacheSize(stats.totalSize));
    
    // 自动清理过期缓存
    const cleared = clearExpiredCache();
    if (cleared > 0) {
      console.log('🧹 自动清理过期缓存:', cleared, '个');
    }
  }

  // 检查缓存数量是否过多（超过100个）
  if (stats.configCacheKeys > 100) {
    console.warn('⚠️ 缓存数量过多:', stats.configCacheKeys, '个');
  }
};

/**
 * 初始化缓存管理器
 * 在应用启动时调用，设置定期清理任务
 */
export const initCacheManager = (): void => {
  console.log('🚀 CacheManager - 初始化缓存管理器');

  // 定期监控缓存使用情况（每5分钟）
  setInterval(() => {
    monitorCacheUsage();
  }, 5 * 60 * 1000);

  // 页面卸载时清理过期缓存
  window.addEventListener('beforeunload', () => {
    clearExpiredCache();
  });

  // 初始监控
  monitorCacheUsage();
};

export default {
  getGlobalCacheStats,
  clearCache,
  clearExpiredCache,
  clearAppCache,
  clearLanguageCache,
  getFormattedCacheSize,
  logCacheStats,
  monitorCacheUsage,
  initCacheManager
};
