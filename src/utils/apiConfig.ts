import Cookies from 'js-cookie'
import { getEnvConfig } from './envConfig'

/**
 * 统一的API配置工具函数
 * 基于环境变量获取API基础地址和相关配置
 */
export interface ApiConfig {
  apiBase: string
  user: string
  medxyToken: string
}

/**
 * 获取API配置
 * 基于环境变量动态设置API基础地址
 */
export const getApiConfig = (userInfo?: any): ApiConfig => {
  // 从环境配置获取API基础地址
  const envConfig = getEnvConfig()
  const apiBase = envConfig.apiBase

  // 获取用户信息
  let user = 'nologin'
  if (userInfo?.userName) {
    user = userInfo.userName
  } else {
    // 从 localStorage 获取用户信息作为备选
    try {
      const storedUserInfo = JSON.parse(Cookies.get("userInfo") || '{}')
      user = storedUserInfo.userName || 'nologin'
    } catch (error) {
      console.warn('Failed to parse userInfo from localStorage:', error)
    }
  }

  return {
    apiBase,
    user,
    medxyToken: Cookies.get('medxyToken') || ''
  }
}
