/**
 * 登录事件管理器
 * 统一管理登录相关事件，防止重复触发和调用
 */
export class LoginEventManager {
  private static eventHistory = new Map<string, number>()
  private static eventCooldown = 1000 // 事件冷却时间（毫秒）
  
  /**
   * 检查事件是否在冷却期内
   */
  private static isEventInCooldown(eventType: string): boolean {
    const lastTime = this.eventHistory.get(eventType)
    if (!lastTime) return false
    
    return Date.now() - lastTime < this.eventCooldown
  }
  
  /**
   * 记录事件触发时间
   */
  private static recordEvent(eventType: string) {
    this.eventHistory.set(eventType, Date.now())
  }
  
  /**
   * 安全触发登录成功事件（防重复）
   */
  static triggerLoginSuccess(userInfo: any, options: { immediate?: boolean } = {}) {
    const eventType = 'loginSuccess'
    
    if (this.isEventInCooldown(eventType)) {
      console.log('⏰ LoginEventManager: loginSuccess事件在冷却期内，跳过触发')
      return false
    }
    
    console.log('🚀 LoginEventManager: 触发loginSuccess事件')
    
    const event = new CustomEvent('loginSuccess', {
      detail: { 
        userInfo, 
        immediate: options.immediate || false,
        timestamp: Date.now()
      }
    })
    
    window.dispatchEvent(event)
    this.recordEvent(eventType)
    
    return true
  }
  
  /**
   * 安全触发用户信息更新事件（防重复）
   */
  static triggerUserInfoUpdated(userInfo: any, options: { immediate?: boolean } = {}) {
    const eventType = 'userInfoUpdated'
    
    if (this.isEventInCooldown(eventType)) {
      console.log('⏰ LoginEventManager: userInfoUpdated事件在冷却期内，跳过触发')
      return false
    }
    
    console.log('🚀 LoginEventManager: 触发userInfoUpdated事件')
    
    const event = new CustomEvent('userInfoUpdated', {
      detail: { 
        userInfo, 
        immediate: options.immediate || false,
        timestamp: Date.now()
      }
    })
    
    window.dispatchEvent(event)
    this.recordEvent(eventType)
    
    return true
  }
  
  /**
   * 安全触发登录状态变化事件（防重复）
   */
  static triggerLoginStatusChanged(type: 'login' | 'logout', options: { immediate?: boolean } = {}) {
    const eventType = `loginStatusChanged:${type}`
    
    if (this.isEventInCooldown(eventType)) {
      console.log(`⏰ LoginEventManager: loginStatusChanged(${type})事件在冷却期内，跳过触发`)
      return false
    }
    
    console.log(`🚀 LoginEventManager: 触发loginStatusChanged(${type})事件`)
    
    const event = new CustomEvent('loginStatusChanged', {
      detail: { 
        type, 
        immediate: options.immediate || false,
        timestamp: Date.now()
      }
    })
    
    window.dispatchEvent(event)
    this.recordEvent(eventType)
    
    return true
  }
  
  /**
   * 批量触发登录相关事件（推荐使用）
   */
  static triggerLoginEvents(userInfo: any, options: { immediate?: boolean } = {}) {
    console.log('🎯 LoginEventManager: 批量触发登录事件')
    
    const results = {
      loginSuccess: this.triggerLoginSuccess(userInfo, options),
      userInfoUpdated: this.triggerUserInfoUpdated(userInfo, options),
      loginStatusChanged: this.triggerLoginStatusChanged('login', options)
    }
    
    console.log('📊 LoginEventManager: 事件触发结果:', results)
    return results
  }
  
  /**
   * 清除事件历史记录
   */
  static clearEventHistory() {
    this.eventHistory.clear()
    console.log('🧹 LoginEventManager: 事件历史记录已清除')
  }
  
  /**
   * 获取事件统计信息
   */
  static getEventStats() {
    const stats = {
      totalEvents: this.eventHistory.size,
      events: Array.from(this.eventHistory.entries()).map(([type, time]) => ({
        type,
        time,
        age: Date.now() - time
      })),
      cooldownPeriod: this.eventCooldown
    }
    
    console.log('📊 LoginEventManager: 事件统计:', stats)
    return stats
  }
  
  /**
   * 设置事件冷却时间
   */
  static setCooldownPeriod(ms: number) {
    this.eventCooldown = ms
    console.log(`⚙️ LoginEventManager: 事件冷却时间设置为${ms}ms`)
  }
  
  /**
   * 创建防重复的事件监听器
   */
  static createDedupedListener<T extends Event>(
    eventType: string,
    handler: (event: T) => void,
    cooldown: number = 500
  ) {
    let lastCall = 0
    
    return (event: T) => {
      const now = Date.now()
      if (now - lastCall < cooldown) {
        console.log(`⏰ LoginEventManager: ${eventType}监听器在冷却期内，跳过处理`)
        return
      }
      
      lastCall = now
      handler(event)
    }
  }
  
  /**
   * 在开发环境下提供调试工具
   */
  static initDebugTools() {
    if (process.env.NODE_ENV === 'development') {
      (window as any).LoginEventManager = this
      console.log('🔧 LoginEventManager调试工具已挂载到window对象')
      console.log('💡 使用方法:')
      console.log('  - window.LoginEventManager.getEventStats() // 查看事件统计')
      console.log('  - window.LoginEventManager.clearEventHistory() // 清除事件历史')
      console.log('  - window.LoginEventManager.setCooldownPeriod(2000) // 设置冷却时间')
    }
  }
}

// 自动初始化调试工具
LoginEventManager.initDebugTools()

export default LoginEventManager
