/**
 * 意见反馈配置工具函数
 * 基于环境变量进行环境判断和projectId配置
 */

import { extractAppNameFromPath } from '../i18n/utils'
import { getCurrentEnvironment, getEnvConfig } from './envConfig'

/**
 * 生产环境应用名称到projectId的映射表
 * 用于在生产环境中为不同应用分配不同的projectId
 */
const PRODUCTION_APP_PROJECT_ID_MAP: Record<string, number> = {
  'novax-base': 251,
  'novax-pro': 252,
  'elavax-base': 253,
  'elavax-pro': 254,
}

/**
 * 获取意见反馈的projectId
 * 基于环境变量和应用名称判断并返回对应的projectId
 *
 * @returns {number} projectId
 */
export const getFeedbackProjectId = (): number => {
  const currentEnv = getCurrentEnvironment()
  const envConfig = getEnvConfig()

  // 生产环境 - 根据应用名称分配不同的projectId
  if (currentEnv === 'production') {
    // 从当前URL路径中提取应用名称
    const appName = extractAppNameFromPath(window.location.pathname)


    // 查找应用对应的projectId
    const projectId = PRODUCTION_APP_PROJECT_ID_MAP[appName]

    if (projectId) {
      return projectId
    }

    // 如果无法识别应用名称，使用默认的生产环境projectId
    console.warn(`未识别的应用名称: ${appName}，使用默认projectId`)
    return envConfig.feedbackProjectId
  }

  // 其他环境使用配置的默认projectId
  return envConfig.feedbackProjectId
}

/**
 * 获取意见反馈配置
 * 包含projectId和其他相关配置
 *
 * 环境配置说明（基于环境变量和应用名称判断）：
 * - 生产环境 (REACT_APP_ENV=production)：
 *   - novax-base → projectId = 251
 *   - novax-pro → projectId = 252
 *   - elavax-base → projectId = 253
 *   - elavax-pro → projectId = 254
 *   - 其他/未识别应用 → 使用环境变量REACT_APP_FEEDBACK_PROJECT_ID或默认值250
 * - 其他环境：使用环境变量REACT_APP_FEEDBACK_PROJECT_ID或默认值193
 */
export const getFeedbackConfig = () => {
  return {
    projectId: getFeedbackProjectId(),
    // 可以在这里添加其他配置项
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 3,
    supportedFormats: ['png', 'jpg', 'jpeg', 'gif']
  }
}
