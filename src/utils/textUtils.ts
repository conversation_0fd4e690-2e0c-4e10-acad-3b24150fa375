/**
 * 文本处理工具函数
 * 用于从HTML/Markdown内容中提取纯文本
 */

/**
 * 从HTML字符串中提取纯文本
 * @param html HTML字符串
 * @returns 纯文本字符串
 */
export const extractTextFromHTML = (html: string): string => {
  // 创建一个临时的DOM元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  
  // 获取纯文本内容
  let text = tempDiv.textContent || tempDiv.innerText || '';
  
  // 清理多余的空白字符
  text = text.replace(/\s+/g, ' ').trim();
  
  return text;
};

/**
 * 从Markdown字符串中提取纯文本
 * @param markdown Markdown字符串
 * @returns 纯文本字符串
 */
export const extractTextFromMarkdown = (markdown: string): string => {
  let text = markdown;
  
  // 移除代码块
  text = text.replace(/```[\s\S]*?```/g, (match) => {
    // 提取代码块内容，保留换行
    const codeContent = match.replace(/```\w*\n?/, '').replace(/```$/, '');
    return codeContent;
  });
  
  // 移除行内代码
  text = text.replace(/`([^`]+)`/g, '$1');
  
  // 移除链接，保留文本
  text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
  
  // 移除图片
  text = text.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1');
  
  // 移除标题标记
  text = text.replace(/^#{1,6}\s+/gm, '');
  
  // 移除粗体和斜体标记
  text = text.replace(/\*\*([^*]+)\*\*/g, '$1');
  text = text.replace(/\*([^*]+)\*/g, '$1');
  text = text.replace(/__([^_]+)__/g, '$1');
  text = text.replace(/_([^_]+)_/g, '$1');
  
  // 移除删除线
  text = text.replace(/~~([^~]+)~~/g, '$1');
  
  // 移除引用标记
  text = text.replace(/^>\s+/gm, '');
  
  // 移除列表标记
  text = text.replace(/^[\s]*[-*+]\s+/gm, '');
  text = text.replace(/^[\s]*\d+\.\s+/gm, '');
  
  // 移除水平分割线
  text = text.replace(/^[-*_]{3,}$/gm, '');
  
  // 清理多余的空白字符，但保留段落分隔
  text = text.replace(/[ \t]+/g, ' ');
  text = text.replace(/\n\s*\n/g, '\n\n');
  text = text.trim();
  
  return text;
};

/**
 * 智能提取文本内容
 * 自动检测内容类型并选择合适的提取方法
 * @param content 内容字符串
 * @returns 纯文本字符串
 */
export const extractPlainText = (content: string): string => {
  if (!content || typeof content !== 'string') {
    return '';
  }
  
  // 检测是否包含HTML标签
  const hasHTMLTags = /<[^>]+>/.test(content);
  
  if (hasHTMLTags) {
    // 如果包含HTML标签，使用HTML提取方法
    return extractTextFromHTML(content);
  } else {
    // 否则使用Markdown提取方法
    return extractTextFromMarkdown(content);
  }
};

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 复制是否成功
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    // 优先使用现代的Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级到传统方法
      return fallbackCopyToClipboard(text);
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error);
    // 如果现代API失败，尝试降级方法
    return fallbackCopyToClipboard(text);
  }
};

/**
 * 降级的复制方法（兼容旧浏览器）
 * @param text 要复制的文本
 * @returns boolean 复制是否成功
 */
const fallbackCopyToClipboard = (text: string): boolean => {
  try {
    // 创建一个临时的textarea元素
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 设置样式使其不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    
    // 添加到DOM
    document.body.appendChild(textArea);
    
    // 选择文本
    textArea.focus();
    textArea.select();
    
    // 执行复制命令
    const successful = document.execCommand('copy');
    
    // 清理
    document.body.removeChild(textArea);
    
    return successful;
  } catch (error) {
    console.error('降级复制方法失败:', error);
    return false;
  }
};

/**
 * 检查是否支持剪贴板API
 * @returns boolean 是否支持
 */
export const isClipboardSupported = (): boolean => {
  return !!(navigator.clipboard && window.isSecureContext) || 
         document.queryCommandSupported?.('copy') === true;
};
