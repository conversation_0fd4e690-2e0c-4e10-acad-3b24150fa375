import { XAiApi, IGetAiAppInfoResponse } from '../api/src/xai-api'
import { getDomainForConfigKey } from './domainUtils'
import { getCurrentLanguage, setLanguage, addLanguageChangeListener } from '../i18n/simple'
import { ApiCallDeduplicator } from './apiCallDeduplicator'

/**
 * 应用服务 - 处理应用相关的业务逻辑
 */
export class AppService {
  private static instance: AppService
  private appListCache: IGetAiAppInfoResponse[] | null = null
  private cacheTimestamp: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  private constructor() {
    // 监听语言切换事件，在语言切换时清除缓存
    addLanguageChangeListener((newLang, oldLang) => {
      if (newLang !== oldLang) {
        console.log('AppService: 语言切换，清除应用列表缓存', {
          from: oldLang,
          to: newLang
        })
        this.clearCache()
      }
    })
  }

  public static getInstance(): AppService {
    if (!AppService.instance) {
      AppService.instance = new AppService()
    }
    return AppService.instance
  }

  /**
   * 确保语言设置正确（在API调用前）
   */
  private ensureCorrectLanguage(): void {
    const currentLang = getCurrentLanguage()

    // 检查当前URL路径中的语言
    if (typeof window !== 'undefined') {
      const pathname = window.location.pathname
      const segments = pathname.split('/').filter(Boolean)
      const urlLang = segments[0]

      if ((urlLang === 'zh' || urlLang === 'en') && urlLang !== currentLang) {
        console.log('AppService: 检测到语言不匹配，更新语言设置并清除缓存', {
          currentLang,
          urlLang,
          pathname
        })
        setLanguage(urlLang)
        // 语言切换时清除缓存，确保获取正确语言环境的应用列表
        this.clearCache()
      } else if (!urlLang || (urlLang !== 'zh' && urlLang !== 'en')) {
        // 根路径或无语言路径，检查是否需要设置正确的默认语言
        const hostname = window.location.hostname
        // 检查是否为国际站：生产域名或international模式
        const isInternationalMode = import.meta.env.MODE === 'international'
        const isInternationalSite = hostname === 'medxy.ai' || hostname === 'www.medxy.ai' || isInternationalMode
        const expectedLang = isInternationalSite ? 'en' : 'zh'

        if (currentLang !== expectedLang) {
          console.log('AppService: 根路径检测，设置正确的默认语言并清除缓存', {
            currentLang,
            expectedLang,
            hostname,
            pathname
          })
          setLanguage(expectedLang)
          // 语言切换时清除缓存
          this.clearCache()
        }
      }
    }
  }

  /**
   * 获取应用列表（带缓存）
   */
  public async getAppList(forceRefresh: boolean = false): Promise<IGetAiAppInfoResponse[]> {
    const now = Date.now()

    // 如果缓存有效，直接返回缓存
    if (this.appListCache && (now - this.cacheTimestamp) < this.CACHE_DURATION) {
      return this.appListCache
    }

    try {
      // 在API调用前确保语言设置正确
      this.ensureCorrectLanguage()

      // 创建API实例
      const xAiApi = new XAiApi({
        apiBase: '/dev-api',
        user: 'anonymous',
        medxyToken: ''
      })

      // 获取应用列表（使用去重机制避免重复调用）
      const configKey = getDomainForConfigKey()
      const currentLang = getCurrentLanguage()

      console.log('AppService: 使用去重机制调用getAppByDomain', {
        configKey,
        language: currentLang,
        timestamp: new Date().toISOString()
      })

      const apps = await ApiCallDeduplicator.getAppByDomainDeduped(
        configKey,
        () => xAiApi.getAppByDomain({ configKey }),
        forceRefresh, // 不强制刷新
        currentLang // 传递语言参数确保不同语言使用不同缓存
      )
      
      // 更新缓存
      this.appListCache = apps
      this.cacheTimestamp = now
      
      console.log('AppService: 获取应用列表成功', {
        appCount: apps.length,
        apps: apps.map((app: IGetAiAppInfoResponse) => app.appNameEn)
      })
      
      return apps
    } catch (error) {
      console.error('AppService: 获取应用列表失败', error)
      
      // 如果有缓存，返回缓存（即使过期）
      if (this.appListCache) {
        console.log('AppService: 使用过期缓存')
        return this.appListCache
      }
      
      // 如果没有缓存，返回空数组
      return []
    }
  }

  /**
   * 获取第一个可用应用的名称
   */
  public async getFirstAvailableAppName(): Promise<string> {
    try {
      console.log('AppService: 开始获取第一个可用应用')
      const apps = await this.getAppList()

      console.log('AppService: 应用列表获取结果', {
        appCount: apps.length,
        apps: apps.map((app: IGetAiAppInfoResponse) => ({ name: app.appNameEn, title: app.appName }))
      })

      if (apps.length > 0) {
        const firstApp = apps[0]
        console.log('AppService: 获取第一个可用应用成功', {
          appName: firstApp.appNameEn,
          appTitle: firstApp.appName,
          totalApps: apps.length
        })
        return firstApp.appNameEn
      }

      // 根据环境决定fallback策略
      const isInternationalMode = import.meta.env.MODE === 'international'
      const fallbackApp = isInternationalMode ? null : 'novax-base'

      console.warn('AppService: 没有可用应用，使用fallback', {
        reason: '应用列表为空',
        isInternationalMode,
        fallback: fallbackApp
      })

      if (fallbackApp) {
        return fallbackApp
      } else {
        // 在国际站环境下，如果没有应用，抛出错误而不是返回不存在的应用
        throw new Error('No applications available in current language')
      }
    } catch (error) {
      // 根据环境决定fallback策略
      const isInternationalMode = import.meta.env.MODE === 'international'
      const fallbackApp = isInternationalMode ? null : 'novax-base'

      console.error('AppService: 获取第一个可用应用失败', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        isInternationalMode,
        fallback: fallbackApp
      })

      if (fallbackApp) {
        return fallbackApp
      } else {
        // 在国际站环境下，重新抛出错误
        throw error
      }
    }
  }

  /**
   * 同步获取第一个可用应用的名称（仅使用缓存）
   * 如果没有缓存，返回fallback值
   */
  public getFirstAvailableAppNameSync(): string {
    const cacheStatus = this.getCacheStatus()

    if (this.appListCache && this.appListCache.length > 0) {
      const firstApp = this.appListCache[0]
      console.log('AppService: 同步获取第一个可用应用（缓存）', {
        appName: firstApp.appNameEn,
        appTitle: firstApp.appName,
        cacheAge: `${(cacheStatus.age / 1000).toFixed(1)}秒`,
        cacheValid: cacheStatus.isValid,
        totalApps: this.appListCache.length
      })
      return firstApp.appNameEn
    }

    // 根据环境决定fallback策略
    const isInternationalMode = import.meta.env.MODE === 'international'
    const fallbackApp = isInternationalMode ? null : 'novax-base'

    console.warn('AppService: 无缓存数据，使用fallback', {
      hasCache: cacheStatus.hasCache,
      cacheAge: cacheStatus.age,
      isInternationalMode,
      fallback: fallbackApp
    })

    if (fallbackApp) {
      return fallbackApp
    } else {
      // 在国际站环境下，如果没有缓存数据，抛出错误
      throw new Error('No cached application data available')
    }
  }

  /**
   * 检查应用名称是否有效
   */
  public async isValidAppName(appName: string): Promise<boolean> {
    try {
      const apps = await this.getAppList()
      return apps.some(app => app.appNameEn === appName)
    } catch (error) {
      console.error('AppService: 验证应用名称失败', error)
      return false
    }
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.appListCache = null
    this.cacheTimestamp = 0
    console.log('AppService: 缓存已清除')
  }

  /**
   * 获取缓存状态
   */
  public getCacheStatus(): { hasCache: boolean; age: number; isValid: boolean } {
    const now = Date.now()
    const age = now - this.cacheTimestamp
    const isValid = this.appListCache !== null && age < this.CACHE_DURATION
    
    return {
      hasCache: this.appListCache !== null,
      age,
      isValid
    }
  }
}
