import Cookies from 'js-cookie'

/**
 * API调用时序管理器
 * 解决登录后API调用时序问题，确保token设置后再发起需要认证的API请求
 */
export class ApiTimingManager {
  private static tokenCheckInterval = 10 // token检查间隔（毫秒）
  private static maxWaitTime = 2000 // 最大等待时间（毫秒）
  
  /**
   * 等待token设置完成
   * @param timeout 超时时间（毫秒）
   * @returns Promise<string | null> 返回token或null
   */
  static async waitForToken(timeout: number = this.maxWaitTime): Promise<string | null> {
    const startTime = Date.now()
    
    return new Promise((resolve) => {
      const checkToken = () => {
        const token = Cookies.get('medxyToken')
        
        if (token) {
          console.log('✅ ApiTimingManager: Token已获取', { 
            tokenLength: token.length,
            waitTime: Date.now() - startTime 
          })
          resolve(token)
          return
        }
        
        // 检查是否超时
        if (Date.now() - startTime >= timeout) {
          console.log('⏰ ApiTimingManager: 等待token超时', { 
            waitTime: Date.now() - startTime 
          })
          resolve(null)
          return
        }
        
        // 继续等待
        setTimeout(checkToken, this.tokenCheckInterval)
      }
      
      checkToken()
    })
  }
  
  /**
   * 安全的API调用包装器
   * 确保在有token的情况下才调用需要认证的API
   * @param apiCall API调用函数
   * @param requireAuth 是否需要认证
   * @param retryOnAuthError 认证失败时是否重试
   * @returns Promise<T>
   */
  static async safeApiCall<T>(
    apiCall: () => Promise<T>,
    options: {
      requireAuth?: boolean
      retryOnAuthError?: boolean
      waitForToken?: boolean
      timeout?: number
    } = {}
  ): Promise<T> {
    const {
      requireAuth = true,
      retryOnAuthError = true,
      waitForToken = true,
      timeout = this.maxWaitTime
    } = options
    
    // 如果需要认证，先检查token
    if (requireAuth) {
      let token = Cookies.get('medxyToken')
      
      // 如果没有token且需要等待，则等待token设置
      if (!token && waitForToken) {
        console.log('🔄 ApiTimingManager: 等待token设置...')
        const waitedToken = await this.waitForToken(timeout)
        token = waitedToken || undefined
      }
      
      // 如果仍然没有token，抛出错误
      if (!token) {
        throw new Error('No authentication token available')
      }
    }
    
    try {
      console.log('🚀 ApiTimingManager: 执行API调用')
      return await apiCall()
    } catch (error: any) {
      // 如果是认证错误且允许重试
      if (retryOnAuthError && error?.name === 'UnauthorizedError') {
        console.log('🔄 ApiTimingManager: 认证失败，等待token后重试')
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 300))
        const newToken = await this.waitForToken(timeout)
        
        if (newToken) {
          console.log('🔄 ApiTimingManager: 重试API调用')
          return await apiCall()
        }
      }
      
      throw error
    }
  }
  
  /**
   * 专门用于订阅状态查询的安全调用
   * @param getPackageByDomainCall getPackageByDomain API调用
   * @returns Promise<any>
   */
  static async safeGetPackageByDomain(
    getPackageByDomainCall: () => Promise<any>
  ): Promise<any> {
    return this.safeApiCall(getPackageByDomainCall, {
      requireAuth: true,
      retryOnAuthError: true,
      waitForToken: true,
      timeout: 1500 // 订阅状态查询使用较短的超时时间
    })
  }
  
  /**
   * 检查当前是否有有效的认证token
   * @returns boolean
   */
  static hasValidToken(): boolean {
    const token = Cookies.get('medxyToken')
    return !!(token && token.trim() !== '')
  }
  
  /**
   * 获取当前token信息（用于调试）
   * @returns object
   */
  static getTokenInfo() {
    const token = Cookies.get('medxyToken')
    return {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      tokenPreview: token ? `${token.substring(0, 10)}...` : null,
      timestamp: Date.now()
    }
  }
  
  /**
   * 创建带有时序保护的API调用函数
   * @param originalApiCall 原始API调用函数
   * @param options 配置选项
   * @returns 包装后的API调用函数
   */
  static createTimingProtectedApiCall<T extends (...args: any[]) => Promise<any>>(
    originalApiCall: T,
    options: {
      requireAuth?: boolean
      retryOnAuthError?: boolean
      waitForToken?: boolean
      timeout?: number
    } = {}
  ): T {
    return ((...args: Parameters<T>) => {
      return this.safeApiCall(
        () => originalApiCall(...args),
        options
      )
    }) as T
  }
  
  /**
   * 在开发环境下提供调试工具
   */
  static initDebugTools() {
    if (process.env.NODE_ENV === 'development') {
      (window as any).ApiTimingManager = this
      console.log('🔧 ApiTimingManager调试工具已挂载到window对象')
      console.log('💡 使用方法:')
      console.log('  - window.ApiTimingManager.getTokenInfo() // 查看token信息')
      console.log('  - window.ApiTimingManager.hasValidToken() // 检查token有效性')
      console.log('  - window.ApiTimingManager.waitForToken() // 等待token设置')
    }
  }
}

// 自动初始化调试工具
ApiTimingManager.initDebugTools()

export default ApiTimingManager
