import Cookies from 'js-cookie'

/**
 * 快速登录状态更新工具
 * 专门用于解决用户界面更新延迟问题
 */
export class FastLoginUpdate {
  
  /**
   * 立即触发用户信息更新，无延迟
   * 用于登录成功后立即更新UI
   */
  static triggerImmediateUpdate(userInfo?: any) {
    console.log('🚀 FastLoginUpdate: 立即触发用户信息更新')
    
    // 如果没有传入用户信息，从Cookie中获取
    if (!userInfo) {
      const userInfoString = Cookies.get('userInfo')
      if (userInfoString) {
        try {
          userInfo = JSON.parse(userInfoString)
        } catch (error) {
          console.error('解析用户信息失败:', error)
          return
        }
      } else {
        console.warn('没有找到用户信息')
        return
      }
    }
    
    // 立即触发多个更新事件，确保所有组件都能收到
    const events = [
      new CustomEvent('userInfoUpdated', {
        detail: { userInfo, immediate: true }
      }),
      new CustomEvent('loginStatusChanged', {
        detail: { type: 'login', immediate: true }
      }),
      new CustomEvent('fastLoginUpdate', {
        detail: { userInfo, timestamp: Date.now() }
      })
    ]
    
    events.forEach(event => {
      window.dispatchEvent(event)
    })
    
    console.log('✅ FastLoginUpdate: 立即更新事件已触发')
  }
  
  /**
   * 强制刷新所有用户信息相关的组件
   */
  static forceRefreshUserInfo() {
    console.log('🔄 FastLoginUpdate: 强制刷新用户信息')
    
    const userInfoString = Cookies.get('userInfo')
    const medxyToken = Cookies.get('medxyToken')
    
    if (userInfoString && medxyToken) {
      try {
        const userInfo = JSON.parse(userInfoString)
        
        // 触发强制刷新事件
        window.dispatchEvent(new CustomEvent('forceUserInfoRefresh', {
          detail: { 
            userInfo, 
            token: medxyToken,
            timestamp: Date.now()
          }
        }))
        
        // 同时触发常规更新事件
        this.triggerImmediateUpdate(userInfo)
        
        console.log('✅ FastLoginUpdate: 强制刷新完成')
      } catch (error) {
        console.error('强制刷新失败:', error)
      }
    } else {
      console.warn('没有找到完整的登录信息')
    }
  }
  
  /**
   * 检查并修复用户信息显示延迟问题
   */
  static checkAndFixDisplayDelay() {
    console.log('🔍 FastLoginUpdate: 检查显示延迟问题')
    
    const userInfoString = Cookies.get('userInfo')
    const medxyToken = Cookies.get('medxyToken')
    
    if (userInfoString && medxyToken) {
      // 检查页面上是否还显示未登录状态
      const loginButtons = document.querySelectorAll('[data-testid="login-button"], button:contains("登录")')
      const userAvatars = document.querySelectorAll('[data-testid="user-avatar"], img[alt*="avatar"]')
      
      if (loginButtons.length > 0 && userAvatars.length === 0) {
        console.log('⚠️ 检测到显示延迟问题，执行修复')
        this.forceRefreshUserInfo()
        
        // 延迟再次检查
        setTimeout(() => {
          this.checkAndFixDisplayDelay()
        }, 500)
      } else {
        console.log('✅ 用户信息显示正常')
      }
    }
  }
  
  /**
   * 监听登录成功事件并立即更新UI
   */
  static initFastUpdateListener() {
    console.log('🎧 FastLoginUpdate: 初始化快速更新监听器')
    
    // 监听登录成功事件
    window.addEventListener('loginSuccess', ((event: CustomEvent) => {
      console.log('📢 收到登录成功事件，立即更新UI')

      if (event.detail?.userInfo) {
        this.triggerImmediateUpdate(event.detail.userInfo)
      } else {
        // 延迟很短时间后从Cookie获取
        setTimeout(() => {
          this.triggerImmediateUpdate()
        }, 5)
      }
    }) as EventListener)
    
    // 监听Cookie变化
    let lastUserInfo = Cookies.get('userInfo')
    let lastToken = Cookies.get('medxyToken')
    
    const checkCookieChanges = () => {
      const currentUserInfo = Cookies.get('userInfo')
      const currentToken = Cookies.get('medxyToken')
      
      if (currentUserInfo !== lastUserInfo || currentToken !== lastToken) {
        console.log('📢 检测到Cookie变化，立即更新UI')
        
        if (currentUserInfo && currentToken) {
          this.triggerImmediateUpdate()
        }
        
        lastUserInfo = currentUserInfo
        lastToken = currentToken
      }
    }
    
    // 高频检查Cookie变化（仅在登录过程中）
    const fastCheckInterval = setInterval(checkCookieChanges, 100)
    
    // 5秒后降低检查频率
    setTimeout(() => {
      clearInterval(fastCheckInterval)
      setInterval(checkCookieChanges, 1000)
    }, 5000)
    
    console.log('✅ FastLoginUpdate: 监听器初始化完成')
  }
  
  /**
   * 在开发环境下提供调试工具
   */
  static initDebugTools() {
    if (process.env.NODE_ENV === 'development') {
      (window as any).FastLoginUpdate = this
      console.log('🔧 FastLoginUpdate调试工具已挂载到window对象')
      console.log('💡 使用方法:')
      console.log('  - window.FastLoginUpdate.triggerImmediateUpdate() // 立即更新')
      console.log('  - window.FastLoginUpdate.forceRefreshUserInfo() // 强制刷新')
      console.log('  - window.FastLoginUpdate.checkAndFixDisplayDelay() // 检查修复延迟')
    }
  }
}

// 自动初始化
FastLoginUpdate.initFastUpdateListener()
FastLoginUpdate.initDebugTools()

export default FastLoginUpdate
