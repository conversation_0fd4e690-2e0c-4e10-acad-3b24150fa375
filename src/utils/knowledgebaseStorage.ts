// 知识库存储管理工具
// 这是一个本地存储的模拟实现，可以很容易替换为真实的API调用

export type ItemType = 'file' | 'folder';

export interface KnowledgebaseItem {
  id: string;
  name: string;
  type: ItemType;
  path: string[];
  size: number; // 字节数，文件夹为0
  uploadTime: number; // 时间戳
  mimeType?: string; // 文件MIME类型
  data?: string; // 文件数据（base64编码）
}

export interface UserKnowledgebase {
  [userId: string]: {
    items: KnowledgebaseItem[];
    totalSize: number;
  };
}

export class KnowledgebaseStorage {
  private static instance: KnowledgebaseStorage;
  private readonly STORAGE_KEY = 'knowledgebase_data';
  private readonly MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
  private readonly MAX_TOTAL_SIZE = 1 * 1024 * 1024 * 1024; // 1GB per user

  private constructor() {}

  public static getInstance(): KnowledgebaseStorage {
    if (!KnowledgebaseStorage.instance) {
      KnowledgebaseStorage.instance = new KnowledgebaseStorage();
    }
    return KnowledgebaseStorage.instance;
  }

  // 获取存储数据
  private getStorageData(): UserKnowledgebase {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Failed to parse knowledgebase data:', error);
      return {};
    }
  }

  // 保存存储数据
  private setStorageData(data: UserKnowledgebase): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save knowledgebase data:', error);
      throw new Error('存储空间不足，请清理一些文件后重试');
    }
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 获取用户数据
  private getUserData(userId: string) {
    const data = this.getStorageData();
    if (!data[userId]) {
      data[userId] = { items: [], totalSize: 0 };
    }
    return data[userId];
  }

  // 验证文件大小
  private validateFileSize(file: File, userId: string): void {
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`文件 ${file.name} 超过最大限制 (100MB)`);
    }

    const userData = this.getUserData(userId);
    if (userData.totalSize + file.size > this.MAX_TOTAL_SIZE) {
      throw new Error('存储空间不足，请删除一些文件后重试');
    }
  }

  // 检查路径是否存在
  private pathExists(userId: string, path: string[]): boolean {
    const userData = this.getUserData(userId);
    
    if (path.length === 0) return true; // 根目录总是存在
    
    // 检查路径中每一级文件夹是否存在
    for (let i = 0; i < path.length; i++) {
      const currentPath = path.slice(0, i + 1);
      const pathStr = currentPath.join('/');
      const folderExists = userData.items.some(item => 
        item.type === 'folder' && 
        item.path.length === currentPath.length - 1 &&
        item.path.join('/') + (item.path.length > 0 ? '/' : '') + item.name === pathStr
      );
      
      if (!folderExists) {
        return false;
      }
    }
    
    return true;
  }

  // 获取指定路径下的项目
  public getItemsInPath(userId: string, path: string[]): KnowledgebaseItem[] {
    const userData = this.getUserData(userId);
    return userData.items.filter(item => 
      item.path.length === path.length &&
      item.path.every((segment, index) => segment === path[index])
    ).sort((a, b) => {
      // 文件夹排在前面，然后按名称排序
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });
  }

  // 添加文件
  public async addFile(userId: string, path: string[], file: File): Promise<void> {
    // 验证路径
    if (!this.pathExists(userId, path)) {
      throw new Error('指定路径不存在');
    }

    // 验证文件大小
    this.validateFileSize(file, userId);

    // 检查文件名冲突
    const userData = this.getUserData(userId);
    const existingItem = userData.items.find(item => 
      item.path.length === path.length &&
      item.path.every((segment, index) => segment === path[index]) &&
      item.name === file.name
    );

    if (existingItem) {
      throw new Error(`文件 ${file.name} 已存在`);
    }

    // 读取文件内容
    const fileData = await this.fileToBase64(file);

    // 创建文件项目
    const newItem: KnowledgebaseItem = {
      id: this.generateId(),
      name: file.name,
      type: 'file',
      path: [...path],
      size: file.size,
      uploadTime: Date.now(),
      mimeType: file.type,
      data: fileData
    };

    // 更新存储
    userData.items.push(newItem);
    userData.totalSize += file.size;

    const allData = this.getStorageData();
    allData[userId] = userData;
    this.setStorageData(allData);
  }

  // 创建文件夹
  public createFolder(userId: string, path: string[], folderName: string): void {
    // 验证路径
    if (!this.pathExists(userId, path)) {
      throw new Error('指定路径不存在');
    }

    // 验证文件夹名称
    if (!folderName.trim()) {
      throw new Error('文件夹名称不能为空');
    }

    // 检查名称冲突
    const userData = this.getUserData(userId);
    const existingItem = userData.items.find(item => 
      item.path.length === path.length &&
      item.path.every((segment, index) => segment === path[index]) &&
      item.name === folderName.trim()
    );

    if (existingItem) {
      throw new Error(`${folderName} 已存在`);
    }

    // 创建文件夹项目
    const newFolder: KnowledgebaseItem = {
      id: this.generateId(),
      name: folderName.trim(),
      type: 'folder',
      path: [...path],
      size: 0,
      uploadTime: Date.now()
    };

    // 更新存储
    userData.items.push(newFolder);

    const allData = this.getStorageData();
    allData[userId] = userData;
    this.setStorageData(allData);
  }

  // 删除项目
  public deleteItem(userId: string, itemId: string): void {
    const userData = this.getUserData(userId);
    const itemIndex = userData.items.findIndex(item => item.id === itemId);
    
    if (itemIndex === -1) {
      throw new Error('项目不存在');
    }

    const item = userData.items[itemIndex];
    
    // 如果是文件夹，需要递归删除所有子项目
    if (item.type === 'folder') {
      const childPath = [...item.path, item.name];
      const childItems = userData.items.filter(childItem => 
        childItem.path.length >= childPath.length &&
        childPath.every((segment, index) => childItem.path[index] === segment)
      );
      
      // 计算要删除的文件总大小
      const deletedSize = childItems.reduce((total, childItem) => 
        total + (childItem.type === 'file' ? childItem.size : 0), 0
      );
      
      // 删除所有子项目
      userData.items = userData.items.filter(childItem => 
        !(childItem.path.length >= childPath.length &&
          childPath.every((segment, index) => childItem.path[index] === segment))
      );
      
      userData.totalSize -= deletedSize;
    } else {
      // 删除文件
      userData.totalSize -= item.size;
    }

    // 删除项目本身
    userData.items.splice(itemIndex, 1);

    const allData = this.getStorageData();
    allData[userId] = userData;
    this.setStorageData(allData);
  }

  // 重命名项目
  public renameItem(userId: string, itemId: string, newName: string): void {
    if (!newName.trim()) {
      throw new Error('名称不能为空');
    }

    const userData = this.getUserData(userId);
    const item = userData.items.find(item => item.id === itemId);
    
    if (!item) {
      throw new Error('项目不存在');
    }

    // 检查名称冲突
    const existingItem = userData.items.find(otherItem => 
      otherItem.id !== itemId &&
      otherItem.path.length === item.path.length &&
      otherItem.path.every((segment, index) => segment === item.path[index]) &&
      otherItem.name === newName.trim()
    );

    if (existingItem) {
      throw new Error(`${newName} 已存在`);
    }

    const oldName = item.name;
    item.name = newName.trim();

    // 如果是文件夹，需要更新所有子项目的路径
    if (item.type === 'folder') {
      const oldPath = [...item.path, oldName];
      const newPath = [...item.path, newName.trim()];
      
      userData.items.forEach(childItem => {
        if (childItem.path.length >= oldPath.length &&
            oldPath.every((segment, index) => childItem.path[index] === segment)) {
          // 更新子项目的路径
          childItem.path = [...newPath, ...childItem.path.slice(oldPath.length)];
        }
      });
    }

    const allData = this.getStorageData();
    allData[userId] = userData;
    this.setStorageData(allData);
  }

  // 下载文件
  public downloadFile(userId: string, itemId: string): void {
    const userData = this.getUserData(userId);
    const item = userData.items.find(item => item.id === itemId);
    
    if (!item || item.type !== 'file' || !item.data) {
      throw new Error('文件不存在或无法下载');
    }

    // 创建下载链接
    const byteCharacters = atob(item.data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: item.mimeType || 'application/octet-stream' });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = item.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // 获取用户统计信息
  public getUserStats(userId: string): { itemCount: number; totalSize: number; folderCount: number; fileCount: number } {
    const userData = this.getUserData(userId);
    const fileCount = userData.items.filter(item => item.type === 'file').length;
    const folderCount = userData.items.filter(item => item.type === 'folder').length;
    
    return {
      itemCount: userData.items.length,
      totalSize: userData.totalSize,
      fileCount,
      folderCount
    };
  }

  // 将文件转换为base64
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // 去掉data:mime;base64,前缀
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  }

  // 清空用户数据（用于测试）
  public clearUserData(userId: string): void {
    const allData = this.getStorageData();
    delete allData[userId];
    this.setStorageData(allData);
  }
}