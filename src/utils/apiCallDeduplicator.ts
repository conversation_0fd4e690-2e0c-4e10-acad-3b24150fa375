/**
 * API调用去重工具
 * 防止相同的API在短时间内被重复调用
 */
export class ApiCallDeduplicator {
  private static pendingCalls: Map<string, Promise<any>> = new Map()
  private static callHistory: Map<string, number> = new Map()
  private static resultCache: Map<string, any> = new Map()
  private static cooldownPeriod: number = 2000 // 2秒冷却时间

  /**
   * 生成API调用的唯一键
   */
  private static generateKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : ''
    return `${url}:${paramStr}`
  }

  /**
   * 检查API调用是否在冷却期内
   */
  private static isInCooldown(key: string): boolean {
    const lastCallTime = this.callHistory.get(key)
    if (!lastCallTime) return false

    const now = Date.now()
    return (now - lastCallTime) < this.cooldownPeriod
  }

  /**
   * 获取剩余冷却时间
   */
  private static getRemainingCooldown(key: string): number {
    const lastCallTime = this.callHistory.get(key)
    if (!lastCallTime) return 0

    const now = Date.now()
    const elapsed = now - lastCallTime
    return Math.max(0, this.cooldownPeriod - elapsed)
  }

  /**
   * 等待指定时间
   */
  private static waitForCooldown(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 缓存API调用结果
   */
  private static cacheResult<T>(key: string, result: T): void {
    this.resultCache.set(key, result)
  }

  /**
   * 获取缓存的API调用结果
   */
  private static getCachedResult<T>(key: string): T | null {
    return this.resultCache.get(key) || null
  }

  /**
   * 去重的API调用包装器
   */
  static async deduplicatedCall<T>(
    key: string,
    apiCall: () => Promise<T>,
    options: {
      cooldown?: number
      forceRefresh?: boolean
    } = {}
  ): Promise<T> {
    const { cooldown = this.cooldownPeriod, forceRefresh = false } = options

    // 检查是否有相同的调用正在进行
    const pendingCall = this.pendingCalls.get(key)
    if (pendingCall && !forceRefresh) {
      console.log(`🔄 ApiCallDeduplicator: 复用正在进行的API调用 - ${key}`)
      return pendingCall
    }

    // 如果不是强制刷新，检查是否在冷却期内
    // 如果在冷却期内，返回缓存的结果而不是抛出错误
    if (!forceRefresh && this.isInCooldown(key)) {
      console.log(`⏰ ApiCallDeduplicator: API调用在冷却期内，尝试返回缓存结果 - ${key}`)

      // 尝试从缓存中获取结果
      const cachedResult = this.getCachedResult<T>(key)
      if (cachedResult) {
        console.log(`📋 ApiCallDeduplicator: 返回缓存结果 - ${key}`)
        return cachedResult
      }

      // 如果没有缓存结果，等待冷却期结束后执行调用
      const remainingCooldown = this.getRemainingCooldown(key)
      console.log(`⏳ ApiCallDeduplicator: 等待冷却期结束 (${remainingCooldown}ms) - ${key}`)
      await this.waitForCooldown(remainingCooldown)
    }

    console.log(`🚀 ApiCallDeduplicator: 执行API调用 - ${key}`)

    // 执行API调用
    const promise = apiCall().then(result => {
      // 缓存结果
      this.cacheResult(key, result)
      return result
    }).finally(() => {
      // 调用完成后清除pending状态
      this.pendingCalls.delete(key)
      // 记录调用时间
      this.callHistory.set(key, Date.now())
    })

    // 记录pending状态
    this.pendingCalls.set(key, promise)

    return promise
  }

  /**
   * 专门用于对话历史记录API的去重调用
   */
  static async getConversationListDeduped(
    appId: string,
    apiCall: () => Promise<any>,
    forceRefresh: boolean = false
  ): Promise<any> {
    const key = `conversations:${appId}`

    return this.deduplicatedCall(key, apiCall, {
      cooldown: 1500, // 对话历史记录使用较短的冷却时间
      forceRefresh
    })
  }

  /**
   * 专门用于getAiWriteToken API的去重调用
   */
  static async getAiWriteTokenDeduped(
    userId: string,
    apiCall: () => Promise<any>,
    forceRefresh: boolean = false
  ): Promise<any> {
    const key = `aiWriteToken:${userId}`

    return this.deduplicatedCall(key, apiCall, {
      cooldown: 3000, // getAiWriteToken使用较长的冷却时间
      forceRefresh
    })
  }

  /**
   * 专门用于getPackageByDomain API的去重调用
   */
  static async getPackageByDomainDeduped(
    configKey: string,
    apiCall: () => Promise<any>,
    forceRefresh: boolean = false,
    language?: string
  ): Promise<any> {
    // 包含语言信息的缓存键，确保不同语言的API调用使用不同的缓存
    const key = language ? `packageByKeys:${configKey}:${language}` : `packageByKeys:${configKey}`

    return this.deduplicatedCall(key, apiCall, {
      cooldown: 2000, // getPackageByDomain使用中等冷却时间
      forceRefresh
    })
  }

  /**
   * 专门用于getAppByDomain API的去重调用
   */
  static async getAppByDomainDeduped(
    configKey: string,
    apiCall: () => Promise<any>,
    forceRefresh: boolean = false,
    language?: string
  ): Promise<any> {
    // 包含语言信息的缓存键，确保不同语言的API调用使用不同的缓存
    const key = language ? `appByConfigKeys:${configKey}:${language}` : `appByConfigKeys:${configKey}`

    return this.deduplicatedCall(key, apiCall, {
      cooldown: 2000, // getAppByDomain使用中等冷却时间
      forceRefresh
    })
  }

  /**
   * 清除特定API的调用历史
   */
  static clearCallHistory(key: string) {
    this.pendingCalls.delete(key)
    this.callHistory.delete(key)
    this.resultCache.delete(key)
    console.log(`🧹 ApiCallDeduplicator: 清除API调用历史 - ${key}`)
  }

  /**
   * 清除所有API调用历史
   */
  static clearAllHistory() {
    this.pendingCalls.clear()
    this.callHistory.clear()
    this.resultCache.clear()
    console.log('🧹 ApiCallDeduplicator: 清除所有API调用历史')
  }

  /**
   * 清除所有对话列表相关的缓存
   */
  static clearAllConversationCache() {
    const keysToDelete: string[] = []

    // 查找所有以 "conversations:" 开头的键
    this.callHistory.forEach((_, key) => {
      if (key.startsWith('conversations:')) {
        keysToDelete.push(key)
      }
    })

    // 删除找到的键
    keysToDelete.forEach(key => {
      this.pendingCalls.delete(key)
      this.callHistory.delete(key)
      this.resultCache.delete(key)
    })

    console.log(`🧹 ApiCallDeduplicator: 清除所有对话列表缓存，共清除 ${keysToDelete.length} 个缓存项`)
  }

  /**
   * 获取当前正在进行的API调用数量
   */
  static getPendingCallsCount(): number {
    return this.pendingCalls.size
  }

  /**
   * 获取调用历史统计
   */
  static getStats() {
    return {
      pendingCalls: this.pendingCalls.size,
      historyEntries: this.callHistory.size,
      cooldownPeriod: this.cooldownPeriod
    }
  }

  /**
   * 在开发环境下提供调试工具
   */
  static initDebugTools() {
    if (process.env.NODE_ENV === 'development') {
      (window as any).ApiCallDeduplicator = this
      console.log('🔧 ApiCallDeduplicator调试工具已挂载到window对象')
      console.log('💡 使用方法:')
      console.log('  - window.ApiCallDeduplicator.getStats() // 查看统计信息')
      console.log('  - window.ApiCallDeduplicator.clearAllHistory() // 清除所有历史')
    }
  }
}

// 自动初始化调试工具
ApiCallDeduplicator.initDebugTools()

export default ApiCallDeduplicator
