/**
 * 域名工具函数
 * 用于根据启动命令或环境变量判断应该使用的域名
 */

/**
 * 根据启动命令或环境判断域名
 * 优先级：环境变量 > 构建模式 > window.location.hostname
 */
export function getDomainForConfigKey(): string {
  // 1. 在浏览器环境中，优先检查Vite环境变量
  try {
  console.log('getDomainForConfigKey',import.meta)

    // @ts-ignore
    if (typeof import.meta !== 'undefined' && import.meta.env) {
      // @ts-ignore
      const envDomain = import.meta.env.REACT_APP_DOMAIN;
      if (envDomain) {
        console.log('🌐 使用Vite环境变量域名:', envDomain);
        return envDomain;
      }

      // @ts-ignore
      const buildMode = import.meta.env.REACT_APP_ENV || import.meta.env.MODE;
      if (buildMode) {
        const domain = getDomainByBuildMode(buildMode);
        if (domain) {
          console.log('🌐 根据Vite构建模式获取域名:', domain, '(模式:', buildMode, ')');
          return domain;
        }
      }
    }
  } catch (error) {
    console.log('🌐 Vite环境变量不可用，继续检查其他方式');
  }

  // 2. 检查Node.js环境变量（仅在Node.js环境中可用）
  try {
    if (typeof process !== 'undefined' && process && process.env) {
      const envDomain = process.env.REACT_APP_DOMAIN;
      if (envDomain) {
        console.log('🌐 使用Node.js环境变量域名:', envDomain);
        return envDomain;
      }

      // 检查构建模式环境变量
      const buildMode = process.env.REACT_APP_ENV || process.env.NODE_ENV;
      if (buildMode) {
        const domain = getDomainByBuildMode(buildMode);
        if (domain) {
          console.log('🌐 根据Node.js构建模式获取域名:', domain, '(模式:', buildMode, ')');
          return domain;
        }
      }
    }
  } catch (error) {
    // 在浏览器环境中，process 可能不存在，这是正常的
    console.log('🌐 Node.js环境变量不可用，这在浏览器环境中是正常的');
  }

  // 3. 在浏览器环境中，使用 window.location.hostname 作为fallback
  if (typeof window !== 'undefined' && window.location) {
    const hostname = window.location.hostname;
    console.log('🌐 使用浏览器域名:', hostname);
    return hostname;
  }

  // 4. 默认域名
  const defaultDomain = 'ai.medsci.cn';
  console.log('🌐 使用默认域名:', defaultDomain);
  return defaultDomain;
}

/**
 * 根据构建模式获取对应的域名
 */
function getDomainByBuildMode(buildMode: string): string | null {
  switch (buildMode) {
    case 'prod':
    case 'production':
      return 'ai.medsci.cn';
    case 'international':
      return 'www.medxy.ai';
    case 'development':
    case 'test':
      return 'ai.medon.com.cn';
    default:
      return null;
  }
}

/**
 * 获取当前构建模式
 */
function getCurrentBuildMode(): string | null {
  // Node.js环境
  try {
    if (typeof process !== 'undefined' && process && process.env) {
      return process.env.REACT_APP_ENV || process.env.NODE_ENV || null;
    }
  } catch (error) {
    // 在浏览器环境中，process 可能不存在，这是正常的
  }

  // Vite环境
  // @ts-ignore
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    // @ts-ignore
    return import.meta.env.REACT_APP_ENV || import.meta.env.MODE || null;
  }

  return null;
}

/**
 * 检查当前是否为特定环境
 */
export function isProductionEnv(): boolean {
  const buildMode = getCurrentBuildMode();
  return buildMode === 'prod' || buildMode === 'production';
}

export function isInternationalEnv(): boolean {
  const buildMode = getCurrentBuildMode();
  return buildMode === 'international';
}

export function isDevelopmentEnv(): boolean {
  const buildMode = getCurrentBuildMode();
  return buildMode === 'development' || buildMode === 'dev';
}

/**
 * 获取当前环境的API基础URL
 */
export function getApiBaseUrl(): string {
  if (isProductionEnv()) {
    return 'https://ai.medsci.cn';
  }
  if (isInternationalEnv()) {
    return 'https://medxy.ai';
  }
  return 'https://ai.medon.com.cn';
}
