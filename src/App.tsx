import { useState, useEffect, useCallback } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { HelmetProvider } from 'react-helmet-async'
import ChatDetail from './components/ChatDetail'
import Home from './components/Home';
import CaseExample from './components/CaseExample';
import I18nRouteWrapper from './components/I18nRouteWrapper';
import Login from './components/login/Login';
import SignUp from './components/login/SignUp';
import LoginManager from './components/LoginManager';
import TokenSyncManager from './components/TokenSyncManager';
import Knowledgebase from './components/Knowledgebase';
import NotFound from './components/NotFound';
import AppRouteGuard from './components/AppRouteGuard';
import { CompetitionProvider } from './contexts/CompetitionContext';
import GlobalCompetitionTimer from './components/GlobalCompetitionTimer';
import GlobalCompetitionCongratulation from './components/GlobalCompetitionCongratulation';
import Cookies from 'js-cookie';
import { useMount } from 'ahooks';
import { getEnvConfig } from './utils/envConfig';
import { getXAiApi, getDifyApi } from './utils/apiManager';
import { useUserInfoWithTokenApi } from './hooks/useCookieMonitor';
import { initCacheManager } from './utils/cacheManager';
import { ApiCallDeduplicator } from './utils/apiCallDeduplicator';
import { initLanguage } from './i18n/simple';

// 导入快速登录更新工具
import('./utils/fastLoginUpdate')

// 导入对话历史记录刷新管理器
import('./utils/conversationRefreshManager')

// 导入API调用去重工具
import('./utils/apiCallDeduplicator')

// 导入API时序管理器
import('./utils/apiTimingManager')

// 导入登录事件管理器
import('./utils/loginEventManager')

function App() {
  const [user, setUser] = useState<string>('nologin')
	const [userInfo, setUserInfo]:any = useState(null)
  const [currentAppUuid, setCurrentAppUuid] = useState<string>('')

  // 简化的Mermaid错误隐藏机制 - 只隐藏错误容器
  useEffect(() => {
    const hideErrorContainers = () => {
      // 查找并隐藏包含错误信息的容器
      const bodyChildren = Array.from(document.body.children);
      bodyChildren.forEach(child => {
        if (child.id !== 'root' && child.textContent) {
          const text = child.textContent;
          if (text.includes('Syntax error in text') && text.includes('mermaid version')) {
            child.classList.add('hide-mermaid-error');
          }
        }
      });
    };

    // 立即执行处理
    hideErrorContainers();

    // 监听DOM变化，处理动态添加的错误容器
    const observer = new MutationObserver(() => {
      hideErrorContainers();
    });

    // 监听body的直接子元素变化
    observer.observe(document.body, {
      childList: true,
      subtree: false
    });

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);


  // 使用API管理器获取API实例，确保token始终是最新的
  const xAiApi = getXAiApi(user)
  const difyApi = getDifyApi(user)

  // 获取环境配置
  const envConfig = getEnvConfig()

  useMount(() => {
    // 初始化语言设置
    initLanguage();

    // 初始化缓存管理器
    initCacheManager();

		// 从cookie中获取userInfo
		const userInfoString = Cookies.get('userInfo')
		if (userInfoString) {
      const userInfos = JSON.parse(userInfoString)
			setUserInfo(userInfos)
      setUser(userInfos.userName)

			const medxyToken = Cookies.get('medxyToken')
			if (medxyToken) {
				return;
			}

      console.log('🔄 App.tsx: 事件监听器触发getAiWriteToken调用')
      // 使用去重机制调用getAiWriteToken
      ApiCallDeduplicator.getAiWriteTokenDeduped(
        userInfos.userId,
        () => xAiApi.getAiWriteToken({
          userId: userInfos.userId,
          userName: userInfos.userName,
          realName: userInfos.realName,
          avatar: userInfos.avatar,
          plaintextUserId: userInfos.plaintextUserId,
          mobile: userInfos.mobile,
          email: userInfos.email
        })
      ).then((data:any) => {
        if (data?.token) {
					Cookies.set("medxyToken", data.token);
          localStorage.setItem("hasuraToken", data.htoken);
          localStorage.setItem("openid", data.openid);
          localStorage.setItem("socialUserId", data.socialUserId);
          localStorage.setItem("socialType", data.socialType);
          console.log('✅ App.tsx: 事件监听器getAiWriteToken调用成功')
        } else {
          console.error("❌ App.tsx: 事件监听器登录失败，未返回token");
        }
      }).catch((error) => {
        if (error.message === 'API call blocked by cooldown period') {
          console.log('⏰ App.tsx: 事件监听器getAiWriteToken调用被冷却机制阻止')
        } else {
          console.error('❌ App.tsx: 事件监听器getAiWriteToken调用失败:', error)
        }
      })
		}
	})

	useEffect(() => {
		if (userInfo) {
			setUser(userInfo.userName)

      const medxyToken = Cookies.get('medxyToken')
			if (medxyToken) {
				console.log('🔒 App.tsx: 已有token，跳过getAiWriteToken调用')
				return;
			}

			console.log('🔄 App.tsx: useEffect触发getAiWriteToken调用')
			// 使用去重机制调用getAiWriteToken
			ApiCallDeduplicator.getAiWriteTokenDeduped(
				userInfo.userId,
				() => xAiApi.getAiWriteToken({
					userId: userInfo.userId,
					userName: userInfo.userName,
					realName: userInfo.realName,
					avatar: userInfo.avatar,
					plaintextUserId: userInfo.plaintextUserId,
					mobile: userInfo.mobile,
					email: userInfo.email
				})
			).then((data: any) => {
				if (data?.token) {
					Cookies.set("medxyToken", data.token );
					localStorage.setItem("hasuraToken", data.htoken);
					localStorage.setItem("openid", data.openid);
					localStorage.setItem("socialUserId", data.socialUserId);
					localStorage.setItem("socialType", data.socialType);
					console.log('✅ App.tsx: getAiWriteToken调用成功')
				} else {
					console.error("❌ App.tsx: 登录失败，未返回token");
				}
			}).catch((error) => {
				if (error.message === 'API call blocked by cooldown period') {
					console.log('⏰ App.tsx: getAiWriteToken调用被冷却机制阻止')
				} else {
					console.error('❌ App.tsx: getAiWriteToken调用失败:', error)
				}
			})
		}
	}, [userInfo]) // 依赖 userInfo

	// 处理用户信息变化
	const handleUserInfoChange = useCallback((userInfoData: any | null) => {
		if (userInfoData) {
			setUserInfo(userInfoData)
			setUser(userInfoData.userName)
		} else {
			setUserInfo(null)
			setUser('nologin')
		}
	}, [])

	// 使用带有内置API调用的Hook监听userInfo cookie变化
	// 这个Hook会自动检测userInfo变化并调用getAiWriteToken接口
	useUserInfoWithTokenApi({
		onUserInfoChange: handleUserInfoChange,
		xAiApi: xAiApi
	})

	// 监听登录状态变化事件
	useEffect(() => {
		const handleUserInfoUpdate = (event: CustomEvent) => {
			console.log('App: 收到用户信息更新事件', event.detail)
			const userInfoString = Cookies.get('userInfo')
			if (userInfoString) {
				const userInfos = JSON.parse(userInfoString)
				setUserInfo(userInfos)
				setUser(userInfos.userName)
			}
		}

		window.addEventListener('userInfoUpdated', handleUserInfoUpdate as EventListener)

		return () => {
			window.removeEventListener('userInfoUpdated', handleUserInfoUpdate as EventListener)
		}
	}, [])


  return (
    <HelmetProvider>
      <CompetitionProvider>
        <Router>
          <I18nRouteWrapper>
            {/* 登录管理组件 - 处理无刷新登录 */}
            <LoginManager />
            {/* Token同步管理组件 - 确保API实例token始终最新 */}
            <TokenSyncManager />
            {/* 全局比赛倒计时组件 */}
            <GlobalCompetitionTimer />
            {/* 全局比赛祝贺弹窗组件 */}
            <GlobalCompetitionCongratulation />
          <Routes>
          {/* 根路径重定向 */}
          <Route path="/" element={<Home
            difyApi={difyApi}
            xAiApi={xAiApi}
            currentAppUuid={currentAppUuid}
            setCurrentAppUuid={setCurrentAppUuid}
            user={user}
             />} />
          
          {/* 登录和注册路由 */}
          {!envConfig.isXAi && (
            <>
              <Route path="/:lang/login" element={<Login />} />
              <Route path="/:lang/login/:socialType" element={<Login />} />
              <Route path="/:lang/sign-up" element={<SignUp />} />
            </>
          )}

          {/* 国际化路由结构: /[lang]/[app-name]/[session-id?] */}
          {/* 知识库路由 */}
          <Route path="/:lang/knowledgebase" element={<Knowledgebase 
            difyApi={difyApi}
            xAiApi={xAiApi}
            user={user} />} />

          {/* 新对话页面 - 使用特殊标识符，需要验证应用名称 */}
          <Route path="/:lang/:appName/new" element={
            <AppRouteGuard>
              <ChatDetail />
            </AppRouteGuard>
          } />

          {/* 案例页面路由 - 使用 cases 复数形式避免冲突 */}
          <Route path="/:lang/cases/:caseId" element={<CaseExample
            currentAppUuid={currentAppUuid}
            xAiApi={xAiApi} />} />

          {/* 历史对话页面 - 有具体的conversationId，需要验证应用名称 */}
          <Route path="/:lang/:appName/:sessionId" element={
            <AppRouteGuard>
              <ChatDetail />
            </AppRouteGuard>
          } />

          {/* 应用首页 - 精确匹配，不带末尾斜杠，需要验证应用名称 */}
          <Route path="/:lang/:appName" element={
            <AppRouteGuard>
              <Home
                difyApi={difyApi}
                xAiApi={xAiApi}
                currentAppUuid={currentAppUuid}
                setCurrentAppUuid={setCurrentAppUuid}
                user={user}
              />
            </AppRouteGuard>
          } />



          {/* 兼容旧路由 */}
          <Route path="/chat" element={<ChatDetail />} />
          <Route path="/chat/:appUuid" element={<ChatDetail />} />
          <Route path="/chat/:appUuid/:conversationId" element={<ChatDetail />} />

          {/* 语言根路径处理 - 避免被404捕获，包括带尾部斜杠的情况 */}
          <Route path="/en" element={<div />} />
          <Route path="/en/" element={<div />} />
          <Route path="/zh" element={<div />} />
          <Route path="/zh/" element={<div />} />

          {/* 404页面 - 必须放在最后，匹配所有未处理的路由 */}
          <Route path="*" element={<NotFound />} />
          </Routes>
        </I18nRouteWrapper>
      </Router>
      </CompetitionProvider>
    </HelmetProvider>
  );
}

export default App
