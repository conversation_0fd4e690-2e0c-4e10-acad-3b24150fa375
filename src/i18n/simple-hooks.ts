import { useState, useCallback, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  t as translate,
  getCurrentLanguage,
  setLanguage,
  initLanguage,
  addLanguageChangeListener,
  removeLanguageChangeListener,
  type SupportedLanguage
} from './simple'
import { getDefaultAppName } from '../utils/envConfig'

// 注意：为了避免循环依赖，我们将在运行时动态获取根域名访问状态
// 这个变量将在组件挂载后被设置
let getRootAccessState: (() => {
  isRootAccess: boolean
  setIsRootAccess: (value: boolean) => void
  currentLanguage: string
  currentAppName: string
} | null) | null = null

// 设置根域名访问状态获取函数（由I18nRouteWrapper调用）
export const setRootAccessStateGetter = (getter: typeof getRootAccessState) => {
  getRootAccessState = getter
}

/**
 * 从路径中提取语言代码
 */
const extractLanguageFromPath = (pathname: string): SupportedLanguage => {
  const segments = pathname.split('/').filter(Boolean)
  const firstSegment = segments[0]

  if (firstSegment && (firstSegment === 'zh' || firstSegment === 'en')) {
    return firstSegment as SupportedLanguage
  }

  // 根据环境决定默认语言
  // 在international模式下，根路径默认为英文
  const isInternationalMode = import.meta.env.MODE === 'international'
  return isInternationalMode ? 'en' : 'zh'
}

/**
 * 构建新的路径，替换语言部分
 * 支持根域名访问时的特殊处理
 */
const buildNewPath = (currentPath: string, newLang: SupportedLanguage): string => {
  const segments = currentPath.split('/').filter(Boolean)

  // 如果第一个段是语言代码，替换它
  if (segments.length > 0 && (segments[0] === 'zh' || segments[0] === 'en')) {
    segments[0] = newLang
    return '/' + segments.join('/')
  }

  // 如果没有语言代码，添加到开头
  if (segments.length > 0) {
    return '/' + newLang + '/' + segments.join('/')
  }

  // 如果是根路径，检查是否为根域名访问状态
  // 如果是根域名访问，需要添加当前应用名称到URL
  if (getRootAccessState) {
    try {
      const rootAccessState = getRootAccessState()
      if (rootAccessState && rootAccessState.isRootAccess && rootAccessState.currentAppName) {
        // 根域名访问时，语言切换需要添加完整的语言/应用后缀
        return `/${newLang}/${rootAccessState.currentAppName}`
      }
    } catch (error) {
      console.warn('无法获取根域名访问状态，使用默认逻辑')
    }
  }

  // 默认情况：返回语言首页
  return `/${newLang}/${getDefaultAppName()}`
}

/**
 * 简化的翻译Hook，集成路由功能
 */
export const useSimpleTranslation = () => {
  const navigate = useNavigate()
  const location = useLocation()

  // 从URL中获取当前语言，这是真实的语言状态
  const urlLanguage = extractLanguageFromPath(location.pathname)
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(urlLanguage)

  // 当URL路径变化时，立即更新语言状态
  useEffect(() => {
    const urlLang = extractLanguageFromPath(location.pathname)
    console.log('URL changed, detected language:', urlLang, 'from path:', location.pathname)

    // 同步URL语言到全局状态和本地状态
    setLanguage(urlLang)
    setCurrentLanguage(urlLang)
  }, [location.pathname])

  // 监听全局语言变化（用于其他组件触发的变化）
  useEffect(() => {
    const handleLanguageChange = (newLang: SupportedLanguage) => {
      console.log('Global language changed to:', newLang)
      setCurrentLanguage(newLang)
    }

    addLanguageChangeListener(handleLanguageChange)

    return () => {
      removeLanguageChangeListener(handleLanguageChange)
    }
  }, [])

  // 翻译函数，直接使用当前语言状态
  const t = useCallback((key: string): string => {
    const result = translate(key)
    // console.log(`Translating "${key}" to "${result}" in language "${currentLanguage}"`)
    return result
  }, [currentLanguage]) // 只依赖currentLanguage

  // 语言切换函数，同时更新URL和状态
  const changeLanguage = useCallback((lang: SupportedLanguage) => {
    if (lang === currentLanguage) return

    console.log('Changing language from', currentLanguage, 'to', lang)

    // 构建新的URL路径
    const newPath = buildNewPath(location.pathname, lang)
    console.log('Navigating to new path:', newPath)

    // 导航到新路径（这会触发useEffect中的路径变化处理）
    navigate(newPath, { replace: true })
  }, [currentLanguage, location.pathname, navigate])

  return {
    t,
    currentLanguage,
    changeLanguage
  }
}

/**
 * 路由感知的国际化Hook
 */
export const useI18nRouter = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { currentLanguage, changeLanguage } = useSimpleTranslation()

  // 从当前路径解析信息
  const segments = location.pathname.split('/').filter(Boolean)
  const appName = segments.length >= 2 ? segments[1] : 'novax-base'
  const sessionId = segments.length >= 3 ? segments[2] : null

  // 导航到指定应用 - 支持根域名访问时的特殊处理
  const navigateToApp = useCallback((appName: string, sessionId?: string, lang?: SupportedLanguage) => {
    const targetLang = lang || currentLanguage
    const path = sessionId ? `/${targetLang}/${appName}/${sessionId}` : `/${targetLang}/${appName}`

    // 检查是否从根路径导航到相同应用，如果是则避免导航
    const isRootPath = location.pathname === '/'
    if (isRootPath) {
      // 在根路径时，检查是否要导航到相同的应用
      // 通过检查当前页面的标题或内容来判断当前显示的应用
      try {
        // 方法1：检查页面标题中是否包含应用名称
        const pageTitle = document.title

        // 方法2：检查页面中的应用相关元素
        const appElements = document.querySelectorAll('[data-app-uuid]')
        const currentAppElement = appElements[0] // 获取第一个应用元素

        // 方法3：检查URL hash或其他标识
        const currentUrl = window.location.href

        console.log('检测根路径应用导航', {
          isRootPath,
          targetApp: appName,
          targetPath: path,
          pageTitle,
          currentUrl,
          appElementsCount: appElements.length,
          currentAppUuid: currentAppElement?.getAttribute('data-app-uuid')
        })

        // 简单的检测逻辑：如果目标路径就是当前应用的完整路径，则跳过导航
        // 这里我们需要一个更可靠的方法来检测当前显示的应用
        // 暂时使用页面内容检测
        const appDisplayName = appName.replace('-', ' ')
        const appNameInPageLower = document.querySelector('img[alt*="' + appDisplayName + '"]')
        const appNameInPageUpper = document.querySelector('img[alt*="' + appDisplayName.replace(/\b\w/g, l => l.toUpperCase()) + '"]')
        const appNameInPageExact = document.querySelector('img[alt="' + appDisplayName.replace(/\b\w/g, l => l.toUpperCase()) + '"]')

        console.log('检测应用图片元素', {
          targetApp: appName,
          appDisplayName,
          appDisplayNameUpper: appDisplayName.replace(/\b\w/g, l => l.toUpperCase()),
          foundLower: !!appNameInPageLower,
          foundUpper: !!appNameInPageUpper,
          foundExact: !!appNameInPageExact
        })

        if (appNameInPageLower || appNameInPageUpper || appNameInPageExact) {
          console.log('避免不必要的导航：根路径已显示目标应用', {
            targetApp: appName,
            reason: '页面中找到了目标应用的图片元素',
            action: '跳过导航'
          })
          return
        }
      } catch (error) {
        console.warn('检测根路径应用状态失败', error)
      }
    }

    // 检查是否为根域名访问状态，如果是，则需要更新根域名访问状态
    if (getRootAccessState) {
      try {
        const rootAccessState = getRootAccessState()
        if (rootAccessState && rootAccessState.isRootAccess) {
          // 从根域名访问切换应用时，需要退出根域名状态并添加URL后缀
          console.log('从根域名访问切换应用', {
            from: 'root access',
            to: path,
            appName,
            sessionId
          })
          rootAccessState.setIsRootAccess(false)
        }
      } catch (error) {
        console.warn('无法获取根域名访问状态，使用默认导航逻辑')
      }
    }

    navigate(path)
  }, [currentLanguage, navigate, location.pathname])

  // 导航到首页
  const navigateToHome = useCallback((appName: string = 'novax-base', lang?: SupportedLanguage) => {
    const targetLang = lang || currentLanguage
    navigate(`/${targetLang}/${appName}`)
  }, [currentLanguage, navigate])

  return {
    currentLanguage,
    currentAppName: appName,
    currentSessionId: sessionId,
    changeLanguage,
    navigateToApp,
    navigateToHome,
    pathname: location.pathname
  }
}
