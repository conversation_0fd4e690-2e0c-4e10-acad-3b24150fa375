// 简化的国际化实现
export type SupportedLanguage = 'zh' | 'en'

// 动态获取默认语言，避免循环依赖
const getDefaultLanguageForSimple = (): SupportedLanguage => {
  // 检查是否为国际站环境
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname
    if (hostname === 'medxy.ai' || hostname === 'www.medxy.ai') {
      return 'en'
    }
  }

  // 检查环境变量
  if (typeof process !== 'undefined' && process.env) {
    const buildMode = process.env.REACT_APP_ENV || process.env.NODE_ENV
    if (buildMode === 'international') {
      return 'en'
    }
  }

  // Vite环境变量检查
  // @ts-ignore
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    // @ts-ignore
    const buildMode = import.meta.env.REACT_APP_ENV || import.meta.env.MODE
    if (buildMode === 'international') {
      return 'en'
    }
  }

  return 'zh' // 默认中文
}

export const DEFAULT_LANGUAGE: SupportedLanguage = getDefaultLanguageForSimple()

// 简化的翻译数据
const translations = {
  zh: {
    // 通用
    'common.loading': '加载中...',
    'common.success': '成功',
    'common.error': '错误',
    'common.confirm': '确认',
    'common.cancel': '取消',
    'common.save': '保存',
    'common.delete': '删除',
    'common.edit': '编辑',
    'common.close': '关闭',
    'common.back': '返回',
    'common.goBack': '返回上一页',
    'common.goToHomepage': '返回首页',
    'common.next': '下一步',
    'common.submit': '提交',
    'common.search': '搜索',
    'common.refresh': '刷新',
    'common.copy': '复制',
    'common.upload': '上传',
    'common.share': '分享',
    'common.settings': '设置',
    'common.help': '帮助',
    'common.about': '关于',
    'common.expert': '专家',
    'common.file': '文件',
    'common.free': '免费体验',

    // Toast 通知
    'toast.loading': '正在加载中...',
    'toast.initializingConversationPage': '正在初始化历史对话页面...',
    'toast.loadingHistory': '正在加载历史消息...',
    'toast.loadingConversations': '正在加载对话列表...',
    
    // 登录和注册
    'login.login_to_MedSci_xAI': '登录 Medxy AI',
    'login.welcome_back_please_login_to_continue': '欢迎回来，请登录继续',
    'login.continue_with_google': '使用 Google 账号登录',
    'login.continue_with_facebook': '使用 Facebook 账号登录',
    'login.or': '或',
    'login.email': '邮箱',
    'login.password': '密码',
    'login.forgot_password': '忘记密码？',
    'login.continue': '登录',
    'login.logging_in': '登录中...',
    'login.no_account_yet': '还没有账号？',
    'login.signUp': '注册',
    'login.login': '登录',
    'login.please_enter_valid_email': '请输入有效的邮箱地址',
    'login.password_cannot_be_empty': '密码不能为空',
    'login.login_failed': '登录失败',
    'login.username': '用户名',
    'login.verification_code': '验证码',
    'login.send_verification_code': '发送验证码',
    'login.retry_after_seconds': '秒后重试',
    'login.email_does_not_exist': '邮箱不存在',
    'login.verification_code_send_failed': '验证码发送失败',
    'login.username_cannot_be_empty': '用户名不能为空',
    'login.verification_code_cannot_be_empty': '验证码不能为空',
    'login.password_must_be_at_least_8_characters': '密码长度至少为8个字符',
    'login.registersuccess': '注册成功',
    'login.registration_failed': '注册失败',
    'login.create_your_account': '创建您的账号',
    'login.registering': '注册中...',
    'login.already_have_account': '已有账号？',

    // 头部
    'header.title': 'NovaX AI',
    'header.menu': '菜单',
    'header.user': '用户',
    'header.language': '语言',
    'header.version': '版本',
    'header.subscription': '订阅',
    'header.profile': '个人资料',
    'header.switchLanguage': '切换语言',
    'header.chinese': '中文',
    'header.english': 'English',

    // 页面标题
    'pageTitle.separator': ' - ',
    'pageTitle.suffix': 'AI智能助手',
    'pageTitle.home': '首页',
    'pageTitle.chat': '对话',
    'pageTitle.newChat': '新建对话',
    'pageTitle.loading': '加载中',
    'pageTitle.error': '错误',
    'pageTitle.notFound': '页面未找到',
    'pageTitle.login': '登录',
    'pageTitle.signup': '注册',
    'pageTitle.apps.novax-base': 'NovaX Base',
    'pageTitle.apps.novax-pro': 'NovaX Pro',
    'pageTitle.apps.novax-ultra': 'NovaX Ultra',
    'pageTitle.apps.elavax-base': 'ElaVaX Base',
    'pageTitle.apps.elavax-pro': 'ElaVaX Pro',
    'pageTitle.apps.elavax-ultra': 'ElaVaX Ultra',
    'pageTitle.knowledgebase': 'DataScore 知识库',

    // 首页
    'home.welcome': '欢迎使用 NovaX AI',
    'home.welcomePrefix': '~Hi，欢迎体验',
    'home.subtitle': '您的科研灵感引擎',
    'home.description': '专为构建前瞻性研究蓝图而设计',
    'home.getStarted': '开始使用',
    'home.learnMore': '了解更多',
    'home.features': '功能特色',
    'home.services': '服务方案',
    'home.caseShowcase': '案例展示',
    'home.caseDescription': '查看其他用户使用小智智能体创作的优秀文档案例',
    'home.inspirationEngine': '您的专属科研助手 | 根据您的需求，快速生成科研创新思路、科研方向、实验思路，助力启动研究项目',
    'home.aiAgentDescription': '查看其他用户使用 AI 代理创建的优秀文档案例',
    'home.agentModeDescription': '立即开启您的创作',
    'home.analysisDescription': '精准剖析，深度洞察，赋能您的卓越创见',
    'home.litraXDescription': '提交您的研究主题，帮您轻松完成学术文献调研',

    // 案例页面
    'case.caseId': '案例 ID：',
    'case.notFound': '案例不存在',
    'case.viewResults': '查看结果',
    'case.viewReplay': '查看回放',
    'cases.noCases': '暂无案例',
    'case.demoCase': '评审的对象为虚拟数据，仅用于演示',

    // 侧边栏
    'sidebar.home': '首页',
    'sidebar.search': '搜索',
    'sidebar.noHistory': '暂无历史记录',
    'sidebar.viewAllHistory': '查看全部历史',
    'sidebar.conversationHistory': '对话历史',
    'sidebar.knowledgebase': '知识库',

    // 历史记录页面
    'history.title': '历史提问记录',
    'history.description': '查看您在此应用中的历史对话',
    'history.viewMore': '查看更多历史记录',
    'history.noMoreRecords': '没有更多记录了',
    'history.loading': '正在加载历史记录...',
    'history.loadFailed': '加载失败',
    'history.rename': '重命名',
    'history.save': '保存',
    'history.cancel': '取消',
    'history.renamePlaceholder': '输入新名称...',
    'history.renameSuccess': '会话重命名成功',
    'history.renameError': '重命名失败，请重试',

    // 时间相关
    'time.unknown': '未知时间',
    'time.justNow': '刚刚',
    'time.today': '今天',
    'time.yesterday': '昨天',
    'time.daysAgo': '{days}天前',

    // 订阅弹窗相关
    'subscription.title': '订阅服务',
    'subscription.subscribing': '已订阅',
    'subscription.subscribed': '已订阅',
    'subscription.subscriptionTime': '订阅时间：',
    'subscription.expireTime': '过期时间：',
    'subscription.expired': '已过期',
    'subscription.cancelled': '退订中',
    'subscription.notSubscribed': '未订阅',
    'subscription.freeSubscription': '免费订阅',
    'subscription.confirmPayment': '确认协议并支付',
    'subscription.subscribingProgress': '订阅中...',
    'subscription.autoSubscribing': '正在自动订阅免费套餐...',
    'subscription.subscribeNow': '立即订阅',
    'subscription.subscribeSuccess': '订阅成功',
    'subscription.paymentFailed': '支付失败',
    'subscription.cancelSubscription': '使用问题或建议反馈 欢迎点我联系',
    'subscription.cancelSuccess': '取消成功',
    'subscription.standardPrice': '标准价',
    'subscription.currentPlan': '当前套餐',
    'subscription.selectPlan': '选择套餐',
    'subscription.paymentMethod': '支付方式',
    'subscription.alipaySupport': '支持使用支付宝支付',
    'subscription.scanQrCode': '请使用支付宝扫描二维码完成支付',
    'subscription.paymentInProgress': '支付进行中，请稍候...',
    'subscription.agreeToTerms': '请先同意服务协议',
    'subscription.serviceAgreement': '服务协议',
    'subscription.privacyPolicy': '隐私政策',
    'subscription.subscriptionActive': '您已成功订阅此服务',
    'subscription.subscriptionExpired': '您的订阅已过期，请重新订阅',
    'subscription.subscriptionCancelled': '您的订阅已取消，在过期之前仍可以正常使用',
    'subscription.confirmCancel': '取消订阅在{expireTime}生效，再次使用需要重新订阅。是否确认取消？',
    'subscription.confirmTitle': '提示',
    'subscription.medsciAccount': '梅斯账号',
    'subscription.alipay': '支付宝支付',
    'subscription.readAndAgree': '请在阅读并同意协议后开通',


    // 订阅周期相关
    'subscription.weeklySubscription': '周连续订阅',
    'subscription.monthlySubscription': '月连续订阅',
    'subscription.continuousSubscription': '连续订阅',
    'subscription.free': '免费',
    'subscription.monthlySub': '连续包月',
    'subscription.quarterlySub': '连续包季',
    'subscription.yearlySub': '连续包年',
    'subscription.perMonth': '/月',
    'subscription.perWeek': '/周',
    'subscription.perDay': '天',
    'subscription.perQuarter': '/季',
    'subscription.perYear': '/年',
    'subscription.perMonthUnit': '月',
    'subscription.justBought': '刚刚订阅了',
    'subscription.member': '会员',
    'subscription.paymentProblem': '如遇到支付问题，请查看这里',

    // 应用描述相关
    'subscription.appDescription.novaxBase': '精准剖析，深度洞察，赋能您的卓越创见',
    'subscription.appDescription.elavaxBase': '精准剖析，深度洞察，赋能您的卓越创见',
    'subscription.appDescription.novaxPro': '支持个性化深度提问，全面理解评估细节与反馈',
    'subscription.appDescription.elavaxPro': '支持个性化深度提问，全面理解评估细节与反馈',

    // 功能说明相关
    'subscription.desc.elavax-base': '对构想进行单次系统评估，提供针对性优化建议，提升基础质量。',
    'subscription.desc.elavax-pro': '支持个性化深度提问，提供定制化提升方案，智能匹配目标平台与发展机遇。',
    'subscription.desc.elavax-ultra': '对标顶级标准，深度评估核心差距，激发高阶创新思维，助力高影响力成果转化。',
    'subscription.desc.novax-base': '基于研究背景，设计创新科研思路，点亮研究起点。',
    'subscription.desc.novax-pro': '基于研究基础，提供个性化研究设计与实施规划，智能匹配发展机遇。',
    'subscription.desc.novax-ultra': '规划多点联动的创新课题群，洞察未来3~5年高潜力方向，提供深度定制战略方案，引领创新。',

    // 备案信息相关
    'footer.icpLicense': '沪ICP备14018916号-1',
    'footer.copyright': '版权©上海梅斯医药科技有限公司',
    'footer.aboutUs': '关于我们',
    'footer.noFaq': '暂无FAQ数据',

    // 关于我们弹框相关
    'about.companyIntro': '公司简介',
    'about.companyDescription': '上海梅斯医药科技有限公司是一家专注于医学科研服务的创新型企业，致力于为医学研究者提供专业的AI智能助手服务，助力医学科研创新发展。',
    'about.contactInfo': '联系方式',
    'about.email': '邮箱',
    'about.website': '官网',
    'about.customerService': '客服咨询',
    'about.scanQRCode': '扫码添加客服微信',

    // 统一的文件上传组件文案
    'fileUpload.buttonText': '上传附件',
    'fileUpload.tooltip': '上传附件',
    'fileUpload.dragAndDrop': '点击或拖拽文件到此处上传',
    'fileUpload.dropToUpload': '释放文件以上传',
    'fileUpload.confirmRemove': '确定要删除文件吗？',
    'fileUpload.selectFiles': '选择文件',
    'fileUpload.uploading': '上传中',
    'fileUpload.uploadComplete': '上传完成',
    'fileUpload.uploadSuccess': '上传成功',
    'fileUpload.uploadFailed': '上传失败',
    'fileUpload.removeFile': '删除文件',
    'fileUpload.multiFileSupport': '支持多文件同时上传',
    'fileUpload.uploadingCannotDelete': '上传中，无法删除',
    'fileUpload.supportedFormats': '支持格式',
    'fileUpload.maxFiles': '最多上传: {count} 个文件',
    'fileUpload.pleaseWait': '请稍候，文件正在处理中',

    // ChatDetail组件文案
    'chat.aiDisclaimer': '以上内容由AI生成，仅供参考和借鉴',
    'chat.inputPlaceholderDetail': '你有什么需求? 请告诉我',
    'chat.sendButton': '发送',
    'chat.enterToSend': 'Enter发送',
    'chat.maxFiles': '最多只能上传 {count} 个文件',
    'chat.fileUploadWarning': '最多只能上传 {maxFiles} 个文件，已自动选择前 {limitFiles} 个文件',
    'chat.uploadFailed': '上传失败',
    'chat.uploadSuccess': '文件 {fileName} 上传成功，ID: {fileId}',
    'chat.networkError': '网络连接失败，请检查网络后重试',
    'chat.uploadTimeout': '上传超时，请重试',
    'chat.fileSizeLimit': '文件大小超过限制',
    'chat.appNotInitialized': '应用未初始化，请稍后重试',
    'chat.fileUploadNotSupported': '当前应用不支持文件上传',
    'chat.unknownFile': '未知文件',
    'chat.initializing': '正在初始化应用...',
    'chat.initializationFailed': '应用初始化失败',
    'chat.authFailed': '认证失败，请重新登录',
    'chat.noAuthToken': '未找到认证令牌，请重新登录',
    'chat.loadingHistory': '正在加载历史消息...',
    'chat.historyLoadFailed': '历史消息格式异常',
    'chat.loadHistoryFailed': '加载历史消息失败',
    'chat.sendMessageFailed': '抱歉，发送消息时出现错误，请稍后重试。',
    'chat.reload': '重新加载',
    'chat.retry': '重试',
    'chat.stopTask': '停止任务失败',
    'chat.sessionExpired': '会话数据已过期',
    'chat.parseSessionDataFailed': '解析会话数据失败',
    'chat.noAuthTokenWarning': '未找到 medxyToken，跳过获取应用信息',
    'chat.refreshInitFailed': '页面刷新后初始化应用失败',
    'chat.historyResponseFormatError': '历史消息响应格式异常',

    // 悬浮按钮
    'floating.newChat': '新建会话',
    'floating.goHome': '返回首页',

    // 场景选择
    'scene.general': '通用场景',
    'scene.category': '场景',
    'scene.selectType': '选择场景类型',
    'scene.selectTitle': '选择场景',
    'scene.selectConfirmMessage': '切换场景时会清空输入框，请确认是否切换场景？',

    // 聊天
    'chat.title': '对话',
    'chat.newChat': '新建对话',
    'chat.chatHistory': '对话历史',
    'chat.inputPlaceholder': '请输入您的问题...',
    'chat.inputPlaceholderHome': '请输入您的需求...',
    'chat.send': '发送',
    'chat.stop': '停止',
    'chat.regenerate': '重新生成',
    'chat.copy': '复制',
    'chat.delete': '删除',
    'chat.thinking': '思考中...',
    'chat.generating': '生成中...',
    'chat.searching': '搜索中...',
    'chat.browsing': '浏览中...',
    'chat.reviewing': '评审中...',
    'chat.secondReviewing': '复审中...',
    'chat.workflow': '工作流',
    'chat.workflowRunning': '工作流运行中',
    'chat.workflowFinished': '工作流已完成',
    'chat.workflowError': '工作流错误',
    'chat.thinkingProcess': '思考过程',
    'chat.searchResults': '搜索结果',
    'chat.browsingResults': '浏览结果',
    'chat.finalAnswer': '最终回答',
    'chat.clickToCollapse': '点击折叠',
    'chat.clickToExpand': '点击显示',
    'chat.clickToSync': '点击同步滚动到左侧对应步骤',
    'chat.thinkingCollapsed': '思考过程已折叠点击显示',
    'chat.browsingCollapsed': '浏览结果已折叠点击显示',
    'chat.reviewCollapsed': '评审过程已折叠点击显示',
    'chat.secondReviewCollapsed': '复审过程已折叠点击显示',
    'chat.nodeDataCollapsed': '节点数据已折叠点击显示',
    'chat.characters': '字符',
    'chat.steps': '步骤',
    'chat.sources': '来源',
    'chat.nodeLoading': '节点加载',
    'chat.expandDetails': '展开详情',
    'chat.thinkingChain': '思维链',
    'chat.collapseDetails': '折叠详情',
    'chat.thinkingProcessCollapsed': '思维过程（已折叠）',
    'chat.stepsCompleted': '步骤已完成',
    'chat.expandThinkingProcess': '展开思维过程',
    'chat.loading': '正在加载中...',
    'chat.loadingMessage': 'AI正在思考中...',
    'chat.aiThinking': '正在思考',
    'chat.aiResponding': '正在回复',
    'chat.waitingForResponse': '等待回复中',
    'chat.completed': '已完成回答',
    'chat.preparing': '准备',
    'chat.executionFailed': '执行失败',
    'chat.reviewingFinalResult': '正在评审最终结果',
    'chat.secondReviewingFinalResult': '正在复审最终结果',
    'workflow.status.finished': '已完成',
    'chat.uploadFile': '上传文件',
    'chat.fileUploaded': '文件已上传',
    'chat.fileUploadError': '文件上传失败',
    'chat.uploading': '上传中',
    'chat.uploaded': '已上传',
    'chat.maxFilesReached': '已达到最大文件数量限制 ({count} 个)',
    'chat.maxFilesExceeded': '最多只能再选择 {available} 个文件，总数不能超过 {max} 个',
    'chat.batchUploadLimit': '每次最多只能选择 {count} 个文件',
    'chat.unsupportedFileType': '文件 "{fileName}" 格式不支持，支持的格式：{formats}',
    'chat.fileSizeExceeded': '文件 "{fileName}" 超过大小限制 ({maxSize}MB)',
    'chat.someFiles': '部分文件',
    'chat.comingSoon': '即将上线，敬请期待',
    'chat.copyMessage': '复制消息',
    'chat.copySuccess': '已复制到剪贴板',
    'chat.copyFailed': '复制失败',
    'chat.copied': '已复制',

    // 比赛相关
    'competition.startCompetition': '开始比赛',
    'competition.competitionInProgress': '比赛进行中',
    'competition.timeRemaining': '剩余时间',
    'competition.timeUp': '比赛时间到！',
    'competition.competitionCompleted': '比赛已结束',
    'competition.submitWork': '提交作品',
    'competition.hours': '小时',
    'competition.minutes': '分钟',
    'competition.seconds': '秒',
    'competition.pleaseUploadFiles': '请先上传作品文件',
    'competition.submitSuccess': '作品提交成功',
    'competition.submitFailed': '作品提交失败',
    'competition.confirmSubmit': '确认提交',
    'competition.submitDescription': '请上传您的比赛作品文件，支持多种格式。',
    'competition.uploadFiles': '上传文件',
    'competition.uploadHint': '点击选择文件或拖拽文件到此处',
    'competition.supportedFormats': '支持 PDF、DOC、DOCX 格式',
    'competition.uploadedFiles': '已上传文件',
    'competition.uploading': '正在上传...',
    'competition.submissionSuccessTitle': '参赛作品上传成功',
    'competition.submissionSuccessMessage': '您的作品已成功提交，感谢您的参与！',
    'competition.surveyTitle': '邀请您填写调研问卷',
    'competition.surveyDescription': '为了更好地改进我们的服务，邀请您填写一份简短的调研问卷。',
    'competition.fillSurvey': '填写问卷',
    'competition.competitionEnded': '比赛结束',
    'competition.congratulations': '恭喜您完成创作，请填写',
    'competition.surveyLink': '调研问卷',
    'competition.congratulationsEnd': '～',
    'competition.congratulationTitle': '恭喜您完成比赛！',
    'competition.congratulationMessage': '您已成功完成本次比赛，感谢您的精彩参与！',
    'competition.contactAssistant': '如有其他问题可以联系小助手',
    'competition.gotIt': '我知道了',

    // 认证
    'auth.loginButton': '登录',
    'auth.logoutButton': '退出登录',
    'auth.loggedIn': '已登录',
    'auth.notLoggedIn': '未登录',
    'auth.loginRequired': '请先登录',
    'auth.loginSuccess': '登录成功',
    'auth.logoutSuccess': '退出成功',

    // 导航
    'navigation.feedback': '意见反馈',

    // 意见反馈
    'feedback.titleLabel': '反馈标题',
    'feedback.titlePlaceholder': '请输入反馈标题',
    'feedback.titleRequired': '请输入反馈标题',
    'feedback.titleMaxLength': '标题不能超过100个字符',
    'feedback.contentLabel': '反馈内容',
    'feedback.contentPlaceholder': '请详细描述您遇到的问题或建议，我们会认真对待每一条反馈',
    'feedback.contentRequired': '请输入反馈内容',
    'feedback.contentMinLength': '反馈内容至少需要10个字符',
    'feedback.contentMaxLength': '反馈内容不能超过1000个字符',
    'feedback.submit': '提交反馈',
    'feedback.submitSuccess': '反馈提交成功，感谢您的宝贵意见！',
    'feedback.submitError': '提交失败，请稍后重试',
    'feedback.loginRequired': '请先登录后再提交反馈',
    'feedback.uploadImages': '上传图片',
    'feedback.uploadImagesTip': '支持PNG、JPG、GIF格式，单个文件不超过10MB，最多3张',
    'feedback.dragOrClick': '拖拽或点击选择图片',
    'feedback.uploading': '上传中...',
    'feedback.fileTooLarge': '文件大小不能超过10MB',
    'feedback.maxFilesReached': '最多只能上传3张图片',
    'feedback.maxImagesReached': '已达到最大图片数量限制',
    'feedback.uploadSuccess': '成功上传{count}张图片',
    'feedback.uploadFailed': '图片上传失败',

    // 订阅（更新为完整的订阅弹窗翻译）
    'subscription.upgrade': '升级',
    'subscription.subscribe': '订阅',
    'subscription.renew': '续费',
    'subscription.active': '有效',
    'subscription.inactive': '未激活',

    // 应用
    'apps.novaxBase.name': 'NovaX Base',
    'apps.novaxBase.description': '核心思路启发',
    'apps.novaxBase.subtitle': '基于研究背景进行单次科研方向思路设计',
    'apps.novaxPro.name': 'NovaX Pro',
    'apps.novaxPro.description': '个性化研究规划',
    'apps.novaxPro.subtitle': '基于当前研究基础与可用条件进行深度匹配',
    'apps.novaxUltra.name': 'NovaX Ultra',
    'apps.novaxUltra.description': '构筑创新生态',
    'apps.novaxUltra.subtitle': '战略性课题集群，超越单一思路',

    // 工作流节点
    'workflow.nodes.start': '开始节点',
    'workflow.nodes.ifElse': '条件判断',
    'workflow.nodes.documentExtractor': '文档提取',
    'workflow.nodes.tool': '工具调用',
    'workflow.nodes.llm': 'AI分析',
    'workflow.nodes.answer': '生成回答',
    'workflow.nodes.assigner': '变量赋值',
    'workflow.nodes.variableAggregator': '数据聚合',
    'workflow.nodes.questionClassifier': '问题分类',
    'workflow.nodes.code': '代码执行',
    'workflow.nodes.templateTransform': '模板转换',
    'workflow.nodes.httpRequest': 'HTTP请求',
    'workflow.nodes.parameterExtractor': '参数提取',

    // Mermaid 图表相关
    'mermaid.rendering': '正在渲染图表...',
    'mermaid.retry': '重试',
    'mermaid.viewCode': '查看源码',
    'mermaid.hideCode': '隐藏源码',
    'mermaid.sourceCode': '源代码',
    'mermaid.copy': '复制',
    'mermaid.copied': '已复制',
    'mermaid.suggestion': '建议',
    'mermaid.viewOriginalError': '查看原始错误',
    'mermaid.hideOriginalError': '隐藏原始错误',
    'mermaid.error.syntaxError': 'Mermaid语法错误',
    'mermaid.error.syntaxErrorMessage': '图表代码存在语法错误，无法正确解析',
    'mermaid.error.syntaxErrorSuggestion': '请检查图表类型声明、节点连接语法是否正确',
    'mermaid.error.lexicalError': 'Mermaid词法错误',
    'mermaid.error.lexicalErrorMessage': '图表代码包含无法识别的字符或符号',
    'mermaid.error.lexicalErrorSuggestion': '请检查是否有拼写错误或不支持的字符',
    'mermaid.error.timeout': '渲染超时',
    'mermaid.error.timeoutMessage': '图表渲染时间过长，可能是代码过于复杂',
    'mermaid.error.timeoutSuggestion': '请尝试简化图表结构或分解为多个小图表',
    'mermaid.error.emptyCode': '代码为空',
    'mermaid.error.emptyCodeMessage': '没有提供有效的Mermaid图表代码',
    'mermaid.error.emptyCodeSuggestion': '请提供完整的Mermaid图表代码',
    'mermaid.error.renderFailed': '图表渲染失败',
    'mermaid.error.renderFailedMessage': '无法渲染此Mermaid图表',
    'mermaid.error.renderFailedSuggestion': '请检查代码格式是否正确，或尝试使用其他图表类型',

    // 错误消息
    'error.pageNotFound': '页面未找到',
    'error.pageNotFoundDescription': '抱歉，您访问的页面不存在或已被移动。请检查网址是否正确，或返回首页继续浏览。',
    'errors.networkError': '网络连接错误',
    'errors.serverError': '服务器错误',
    'errors.unauthorized': '未授权, 请重新登录',
    'errors.forbidden': '访问被禁止',
    'errors.notFound': '页面未找到',
    'errors.validationError': '输入验证失败',

    // 模式选择
    'mode.simple': '简单模式',
    'mode.detailed': 'Pubmed模式',
    'mode.selectMode': '选择模式',
    'mode.simpleDescription': '快速获得核心建议',
    'mode.detailedDescription': '深度分析与详细反馈',

    // 消息
    'messages.saveSuccess': '保存成功',
    'messages.deleteSuccess': '删除成功',
    'messages.updateSuccess': '更新成功',
    'messages.operationSuccess': '操作成功',
    'messages.operationFailed': '操作失败',
    'messages.confirmDelete': '确认删除此项目？',
    'messages.processingRequest': '正在处理请求...',
    'messages.requestCompleted': '请求已完成',
    'messages.requestFailed': '请求失败',

    // DataScore 知识库
    'knowledgebase.title': 'Hi，欢迎体验知识库',
    'knowledgebase.subtitle': '专为存储和管理临床研究数据而设计',
    'knowledgebase.uploadFile': '上传文件',
    'knowledgebase.createFolder': '新建文件夹',
    'knowledgebase.folderName': '请输入文件夹名称',
    'knowledgebase.folderNamePlaceholder': '请输入文件夹名称',
    'knowledgebase.createFolderTitle': '新建文件夹',
    'knowledgebase.create': '创建',
    'knowledgebase.preview': '预览',
    'knowledgebase.download': '下载',
    'knowledgebase.rename': '重命名',
    'knowledgebase.delete': '删除',
    'knowledgebase.confirmDelete': '确认删除',
    'knowledgebase.confirmDeleteMessage': '确定要删除这个项目吗？此操作不可撤销。',
    'knowledgebase.uploadSuccess': '成功上传 {count} 个文件',
    'knowledgebase.uploadFailed': '文件上传失败',
    'knowledgebase.folderCreateSuccess': '文件夹创建成功',
    'knowledgebase.folderCreateFailed': '文件夹创建失败',
    'knowledgebase.deleteSuccess': '删除成功',
    'knowledgebase.deleteFailed': '删除失败',
    'knowledgebase.renameSuccess': '重命名成功',
    'knowledgebase.renameFailed': '重命名失败',
    'knowledgebase.downloadSuccess': '正在下载 {name}',
    'knowledgebase.downloadFailed': '下载失败',
    'knowledgebase.backToParent': '返回',
    'knowledgebase.rootFolder': '根目录',
    'knowledgebase.folder': '文件夹',
    'knowledgebase.structuredData': '结构型',
    'knowledgebase.documentData': '文档型',
    'knowledgebase.noFiles': '暂无文件',
    'knowledgebase.noFilesDescription': '拖拽文件到此处或点击上传按钮开始上传',
    'knowledgebase.today': '今天',
    'knowledgebase.yesterday': '昨天',
    'knowledgebase.daysAgo': '{days}天前',
    'knowledgebase.previewTitle': '预览: {name}',
    'knowledgebase.fileSize': '文件大小: {size}',
    'knowledgebase.fileType': '文件类型: {type}',
    'knowledgebase.uploadTime': '上传时间: {time}',
    'knowledgebase.previewNotSupported': '此文件类型不支持预览，您可以下载文件查看完整内容。',
    'knowledgebase.downloadFile': '下载文件',
    'knowledgebase.closePreview': '关闭',
    'knowledgebase.enterFolderName': '请输入文件夹名称',
    'knowledgebase.enterNewName': '请输入新名称',

    // DataScore 文档 (Artifacts)
    'artifacts.title': '文档',
    'artifacts.noArtifacts': '暂无生成的文档',
    'artifacts.noArtifactsDescription': 'DataScore 生成的分析报告和相关文件将在此处显示',
    'artifacts.debugFillScores': '调试：随机填充分数',
    'artifacts.radar.sampleSize': '样本量',
    'artifacts.radar.dataCompleteness': '数据完整度',
    'artifacts.radar.structureLevel': '结构化程度',
    'artifacts.radar.endpointEvents': '终点与结局事件',
    'artifacts.radar.innovationPotential': '创新潜力与延展性',
    'artifacts.radarChart': '数据质量评分雷达图',
    'artifacts.categories.sampleSize': '样本量',
    'artifacts.categories.dataCompleteness': '数据完整度',
    'artifacts.categories.structureLevel': '结构化程度',
    'artifacts.categories.endpoints': '终点与结局事件',
    'artifacts.categories.innovation': '创新潜力与延展性',
    'artifacts.categoryTooltip.sampleSize': '评估数据集中样本的数量和代表性',
    'artifacts.categoryTooltip.dataCompleteness': '评估数据的完整性和缺失值情况',
    'artifacts.categoryTooltip.structureLevel': '评估数据的结构化程度和标准化水平',
    'artifacts.categoryTooltip.endpoints': '评估研究终点和结局事件的明确性',
    'artifacts.categoryTooltip.innovation': '评估数据的创新潜力和研究拓展性',
    'artifacts.dragToResize': '拖拽调整大小',
    'common.score': '评分'
  },
  en: {
    // Common
    'common.loading': 'Loading...',
    'common.success': 'Success',
    'common.error': 'Error',
    'common.confirm': 'Confirm',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.goBack': 'Go Back',
    'common.goToHomepage': 'Go to Homepage',
    'common.next': 'Next',
    'common.submit': 'Submit',
    'common.search': 'Search',
    'common.refresh': 'Refresh',
    'common.copy': 'Copy',
    'common.upload': 'Upload',
    'common.share': 'Share',
    'common.settings': 'Settings',
    'common.help': 'Help',
    'common.about': 'About',
    'common.expert': 'Expert',
    'common.file': 'File',
    'common.free': 'Explore for Free',

    // Toast notifications
    'toast.loading': 'Loading...',
    'toast.initializingConversationPage': 'Initializing conversation history page...',
    'toast.loadingHistory': 'Loading history messages...',
    'toast.loadingConversations': 'Loading conversation list...',
    
    // Login and SignUp
    'login.login_to_MedSci_xAI': 'Login to Medxy AI',
    'login.welcome_back_please_login_to_continue': 'Welcome back, please login to continue',
    'login.continue_with_google': 'Continue with Google',
    'login.continue_with_facebook': 'Continue with Facebook',
    'login.or': 'or',
    'login.email': 'Email',
    'login.password': 'Password',
    'login.forgot_password': 'Forgot password?',
    'login.continue': 'Login',
    'login.logging_in': 'Logging in...',
    'login.no_account_yet': 'No account yet?',
    'login.signUp': 'Sign Up',
    'login.login': 'Login',
    'login.please_enter_valid_email': 'Please enter a valid email address',
    'login.password_cannot_be_empty': 'Password cannot be empty',
    'login.login_failed': 'Login failed',
    'login.username': 'Username',
    'login.verification_code': 'Verification Code',
    'login.send_verification_code': 'Send Code',
    'login.retry_after_seconds': 's',
    'login.email_does_not_exist': 'Email does not exist',
    'login.verification_code_send_failed': 'Failed to send verification code',
    'login.username_cannot_be_empty': 'Username cannot be empty',
    'login.verification_code_cannot_be_empty': 'Verification code cannot be empty',
    'login.password_must_be_at_least_8_characters': 'Password must be at least 8 characters',
    'login.registersuccess': 'Registration successful',
    'login.registration_failed': 'Registration failed',
    'login.create_your_account': 'Create your account',
    'login.registering': 'Registering...',
    'login.already_have_account': 'Already have an account?',

    // Page Title
    'pageTitle.separator': ' - ',
    'pageTitle.suffix': 'AI Assistant',
    'pageTitle.home': 'Home',
    'pageTitle.chat': 'Chat',
    'pageTitle.newChat': 'New Chat',
    'pageTitle.loading': 'Loading',
    'pageTitle.error': 'Error',
    'pageTitle.notFound': 'Page Not Found',
    'pageTitle.login': 'Login',
    'pageTitle.signup': 'Sign Up',
    'pageTitle.apps.novax-base': 'NovaX Base',
    'pageTitle.apps.novax-pro': 'NovaX Pro',
    'pageTitle.apps.novax-ultra': 'NovaX Ultra',
    'pageTitle.apps.elavax-base': 'ElaVaX Base',
    'pageTitle.apps.elavax-pro': 'ElaVaX Pro',
    'pageTitle.apps.elavax-ultra': 'ElaVaX Ultra',
    'pageTitle.knowledgebase': 'DataScore Knowledge Base',

    // Header
    'header.title': 'NovaX AI',
    'header.menu': 'Menu',
    'header.user': 'User',
    'header.language': 'Language',
    'header.version': 'Version',
    'header.subscription': 'Subscription',
    'header.profile': 'Profile',
    'header.switchLanguage': 'Switch Language',
    'header.chinese': '中文',
    'header.english': 'English',

    // Home
    'home.welcome': 'Welcome to NovaX AI',
    'home.welcomePrefix': '~Hi, Welcome to Experience',
    'home.subtitle': 'Your Research Inspiration Engine',
    'home.description': 'Designed for building forward-looking research blueprints',
    'home.getStarted': 'Get Started',
    'home.learnMore': 'Learn More',
    'home.features': 'Features',
    'home.services': 'Services',
    'home.caseShowcase': 'Case Showcase',
    'home.caseDescription': 'View excellent document cases created by other users using AI agents',
    'home.inspirationEngine': 'Your dedicated research assistant | Quickly generate research ideas, directions, and experimental concepts based on your needs to help kickstart your research projects',
    'home.aiAgentDescription': 'View excellent document cases created by other users using AI agents',
    'home.analysisDescription': 'Precise analysis, deep insights, empowering your exceptional innovations',
    'home.litraXDescription': 'Submit your research topic, help you easily conduct academic literature research',
    'home.agentModeDescription': 'Start Your Creation Now',

    // Case page
    'case.caseId': 'Case ID：',
    'case.notFound': 'Case not found：',
    'case.viewResults': 'View Results',
    'case.viewReplay': 'View Replay',
    'cases.noCases': 'No cases',
    'case.demoCase': 'Illustrative Example - Not Real Research',

    // Sidebar
    'sidebar.home': 'Home',
    'sidebar.search': 'Search',
    'sidebar.noHistory': 'No history records',
    'sidebar.viewAllHistory': 'View all history',
    'sidebar.conversationHistory': 'Conversation History',
    'sidebar.knowledgebase': 'Knowledge Base',

    // History page
    'history.title': 'Historical Question Records',
    'history.description': 'View your historical conversations in this application',
    'history.viewMore': 'View more history records',
    'history.noMoreRecords': 'No more records',
    'history.loading': 'Loading history records...',
    'history.loadFailed': 'Failed to load',
    'history.rename': 'Rename',
    'history.save': 'Save',
    'history.cancel': 'Cancel',
    'history.renamePlaceholder': 'Enter new name...',
    'history.renameSuccess': 'Conversation renamed successfully',
    'history.renameError': 'Rename failed, please try again',

    // Time related
    'time.unknown': 'Unknown time',
    'time.justNow': 'Just now',
    'time.today': 'Today',
    'time.yesterday': 'Yesterday',
    'time.daysAgo': '{days} days ago',

    // Subscription modal related
    'subscription.title': 'Subscription Service',
    'subscription.subscribing': 'Subscribed',
    'subscription.subscribed': 'Subscribed',
    'subscription.subscriptionTime': 'Subscription Time: ',
    'subscription.expireTime': 'Expire Time: ',
    'subscription.expired': 'Expired',
    'subscription.cancelled': 'Cancelling',
    'subscription.notSubscribed': 'Not Subscribed',
    'subscription.freeSubscription': 'Start Free Trial',
    'subscription.confirmPayment': 'Agree to Terms and Pay',
    'subscription.subscribingProgress': 'Subscribing...',
    'subscription.autoSubscribing': 'Auto-subscribing to free plan...',
    'subscription.subscribeNow': 'Subscribe Now',
    'subscription.subscribeSuccess': 'Subscription Successful',
    'subscription.paymentFailed': 'Payment Failed',
    'subscription.cancelSubscription': 'Contact Customer Service',
    'subscription.cancelSuccess': 'Cancellation Successful',
    'subscription.standardPrice': 'Standard Price',
    'subscription.currentPlan': 'Current Plan',
    'subscription.selectPlan': 'Select Plan',
    'subscription.paymentMethod': 'Payment Method',
    'subscription.alipaySupport': 'Alipay payment supported',
    'subscription.scanQrCode': 'Please scan the QR code with Alipay to complete payment',
    'subscription.paymentInProgress': 'Payment in progress, please wait...',
    'subscription.agreeToTerms': 'Please agree to the service agreement first',
    'subscription.serviceAgreement': 'Service Agreement',
    'subscription.privacyPolicy': 'Privacy Policy',
    'subscription.subscriptionActive': 'You have successfully subscribed to this service',
    'subscription.subscriptionExpired': 'Your subscription has expired, please renew',
    'subscription.subscriptionCancelled': 'Your subscription has been cancelled, but you can still use it until expiration',
    'subscription.confirmCancel': 'Cancellation will take effect on {expireTime}. You will need to resubscribe to use again. Confirm cancellation?',
    'subscription.confirmTitle': 'Confirmation',

    // Subscription period related
    'subscription.weeklySubscription': 'Weekly Subscription',
    'subscription.monthlySubscription': 'Monthly Subscription',
    'subscription.continuousSubscription': 'Continuous Subscription',
    'subscription.free': 'Free Plan',
    'subscription.monthlySub': 'Monthly Subscription',
    'subscription.quarterlySub': 'Quarterly Subscription',
    'subscription.yearlySub': 'Yearly Subscription',
    'subscription.perMonth': '/month',
    'subscription.perWeek': '/week',
    'subscription.perDay': ' days',
    'subscription.perQuarter': '/quarter',
    'subscription.perYear': '/year',
    'subscription.perMonthUnit': ' months',
    'subscription.medsciAccount': 'MedSci Account',
    'subscription.justBought': 'just bought',
    'subscription.member': 'member',
    'subscription.paymentProblem': 'Payment issues? Check here',
    'subscription.readAndAgree': 'Please read and agree to the agreement before activation',
    'subscription.alipay': 'Alipay',

    // App description related
    'subscription.appDescription.novaxBase': 'Precise analysis, deep insights, empowering your excellence',
    'subscription.appDescription.elavaxBase': 'Precise analysis, deep insights, empowering your excellence',
    'subscription.appDescription.novaxPro': 'Support personalized in-depth questions, comprehensive understanding of evaluation details and feedback',
    'subscription.appDescription.elavaxPro': 'Support personalized in-depth questions, comprehensive understanding of evaluation details and feedback',

    // Feature description related
    'subscription.desc.elavax-base': 'Evaluate your ideas systematically, offering targeted optimization suggestions to enhance quality.',
    'subscription.desc.elavax-pro': 'Enable in-depth questions, deliver customized improvement plans, and match target platforms and opportunities.',
    'subscription.desc.elavax-ultra': 'Align with top standards, identify critical gaps, and inspire innovative thinking for high-impact research outcomes.',
    'subscription.desc.novax-base': 'Design innovative research ideas based on your background, sparking your research journey.',
    'subscription.desc.novax-pro': 'Provide tailored research designs and plans based on your foundation, matching platforms and opportunities.',
    'subscription.desc.novax-ultra': 'Plan linked research clusters, forecast high-potential directions for 3-5 years, and deliver customized strategic solutions.',

    // Footer information related
    'footer.icpLicense': 'ICP License: 沪ICP备14018916号-1',
    'footer.copyright': 'Copyright © MedSci Healthcare Holdings Limited.',

    // About us modal related
    'about.companyIntro': 'Company Introduction',
    'about.companyDescription': 'MedSci Healthcare Holdings Limited is an innovative company focused on medical research services, dedicated to providing professional AI assistant services for medical researchers and promoting innovative development in medical research.',
    'about.contactInfo': 'Contact Information',
    'about.email': 'Email',
    'about.website': 'Website',
    'about.customerService': 'Customer Service',
    'about.scanQRCode': 'Scan QR code to add customer service WeChat',
    'footer.aboutUs': 'About Us',
    'footer.noFaq': 'No FAQ yet',

    // Unified file upload component text
    'fileUpload.buttonText': 'Upload attachment',
    'fileUpload.tooltip': 'Upload attachment',
    'fileUpload.dragAndDrop': 'Click or drag files here to upload',
    'fileUpload.dropToUpload': 'Drop files to upload',
    'fileUpload.confirmRemove': 'Are you sure you want to delete this file?',
    'fileUpload.selectFiles': 'Select files',
    'fileUpload.uploading': 'Uploading',
    'fileUpload.uploadComplete': 'Upload complete',
    'fileUpload.uploadSuccess': 'Upload successful',
    'fileUpload.uploadFailed': 'Upload failed',
    'fileUpload.removeFile': 'Delete file',
    'fileUpload.multiFileSupport': 'Multiple files supported',
    'fileUpload.uploadingCannotDelete': 'Uploading, cannot delete',
    'fileUpload.supportedFormats': 'Supported formats',
    'fileUpload.maxFiles': 'Max upload: {count} files',
    'fileUpload.pleaseWait': 'Please wait, file is being processed',

    // ChatDetail component text
    'chat.aiDisclaimer': 'The above content is generated by AI, for reference only',
    'chat.inputPlaceholderDetail': 'What do you need? Please tell me',
    'chat.sendButton': 'Send',
    'chat.enterToSend': 'Enter to send',
    'chat.maxFiles': 'Maximum {count} files can be uploaded',
    'chat.fileUploadWarning': 'Maximum {maxFiles} files can be uploaded, automatically selected the first {limitFiles} files',
    'chat.uploadFailed': 'Upload failed',
    'chat.uploadSuccess': 'File {fileName} uploaded successfully, ID: {fileId}',
    'chat.networkError': 'Network connection failed, please check your network and try again',
    'chat.uploadTimeout': 'Upload timeout, please try again',
    'chat.fileSizeLimit': 'File size exceeds limit',
    'chat.appNotInitialized': 'Application not initialized, please try again later',
    'chat.fileUploadNotSupported': 'Current application does not support file upload',
    'chat.unknownFile': 'Unknown file',
    'chat.initializing': 'Initializing application...',
    'chat.initializationFailed': 'Application initialization failed',
    'chat.authFailed': 'Authentication failed, please login again',
    'chat.noAuthToken': 'Authentication token not found, please login again',
    'chat.loadingHistory': 'Loading history messages...',
    'chat.historyLoadFailed': 'History message format error',
    'chat.loadHistoryFailed': 'Failed to load history messages',
    'chat.sendMessageFailed': 'Sorry, an error occurred while sending the message, please try again later.',
    'chat.reload': 'Reload',
    'chat.retry': 'Retry',
    'chat.stopTask': 'Failed to stop task',
    'chat.sessionExpired': 'Session data has expired',
    'chat.parseSessionDataFailed': 'Failed to parse session data',
    'chat.noAuthTokenWarning': 'medxyToken not found, skipping app info retrieval',
    'chat.refreshInitFailed': 'Failed to initialize app after page refresh',
    'chat.historyResponseFormatError': 'History message response format error',

    // Floating buttons
    'floating.newChat': 'New Chat',
    'floating.goHome': 'Go Home',

    // Scene selection
    'scene.general': 'General Scene',
    'scene.category': 'Scene',
    'scene.selectType': 'Select Scene Type',
    'scene.selectTitle': 'Select Scene',
    'scene.selectConfirmMessage': 'Switching scenes will clear the input box. Are you sure you want to switch scenes?',

    // Chat
    'chat.title': 'Chat',
    'chat.newChat': 'New Chat',
    'chat.chatHistory': 'Chat History',
    'chat.inputPlaceholder': 'Please enter your question...',
    'chat.inputPlaceholderHome': 'Please enter your requirements...',
    'chat.send': 'Send',
    'chat.stop': 'Stop',
    'chat.regenerate': 'Regenerate',
    'chat.copy': 'Copy',
    'chat.delete': 'Delete',
    'chat.thinking': 'Thinking...',
    'chat.generating': 'Generating...',
    'chat.searching': 'Searching...',
    'chat.browsing': 'Browsing...',
    'chat.reviewing': 'Reviewing...',
    'chat.secondReviewing': 'Second Reviewing...',
    'chat.workflow': 'Workflow',
    'chat.workflowRunning': 'Workflow Running',
    'chat.workflowFinished': 'Workflow Finished',
    'chat.workflowError': 'Workflow Error',
    'chat.thinkingProcess': 'Thinking Process',
    'chat.searchResults': 'Search Results',
    'chat.browsingResults': 'Browsing Results',
    'chat.finalAnswer': 'Final Answer',
    'chat.clickToCollapse': 'Click to collapse',
    'chat.clickToExpand': 'Click to expand',
    'chat.clickToSync': 'Click to sync scroll to corresponding step on the left',
    'chat.thinkingCollapsed': 'Thinking process collapsed, click to show',
    'chat.browsingCollapsed': 'Browsing results collapsed, click to show',
    'chat.reviewCollapsed': 'Review process collapsed, click to show',
    'chat.secondReviewCollapsed': 'Second review process collapsed, click to show',
    'chat.nodeDataCollapsed': 'Node data collapsed, click to show',
    'chat.characters': 'characters',
    'chat.steps': 'steps',
    'chat.sources': 'sources',
    'chat.nodeLoading': 'Node Loading',
    'chat.expandDetails': 'Expand Details',
    'chat.thinkingChain': 'Thinking Chain',
    'chat.collapseDetails': 'Collapse Details',
    'chat.thinkingProcessCollapsed': 'Thinking process (collapsed)',
    'chat.stepsCompleted': 'steps completed',
    'chat.expandThinkingProcess': 'Expand thinking process',
    'chat.loading': 'Loading...',
    'chat.loadingMessage': 'AI is thinking...',
    'chat.aiThinking': 'Thinking',
    'chat.aiResponding': 'Responding',
    'chat.waitingForResponse': 'Waiting for response',
    'chat.completed': 'Answer completed',
    'chat.preparing': 'Preparing',
    'chat.executionFailed': 'execution failed',
    'chat.reviewingFinalResult': 'is reviewing the final result',
    'chat.secondReviewingFinalResult': 'is conducting second review of the final result',
    'workflow.status.finished': 'Finished',
    'chat.uploadFile': 'Upload File',
    'chat.fileUploaded': 'File Uploaded',
    'chat.fileUploadError': 'File Upload Failed',
    'chat.uploading': 'Uploading',
    'chat.uploaded': 'Uploaded',
    'chat.maxFilesReached': 'Maximum file limit reached ({count} files)',
    'chat.maxFilesExceeded': 'Can only select {available} more files, total cannot exceed {max} files',
    'chat.batchUploadLimit': 'Can only select up to {count} files at a time',
    'chat.unsupportedFileType': 'File "{fileName}" format not supported, supported formats: {formats}',
    'chat.fileSizeExceeded': 'File "{fileName}" exceeds size limit ({maxSize}MB)',
    'chat.someFiles': 'some files',
    'chat.comingSoon': 'Get ready! Something amazing is coming soon!',
    'chat.copyMessage': 'Copy message',
    'chat.copySuccess': 'Copied to clipboard',
    'chat.copyFailed': 'Copy failed',
    'chat.copied': 'Copied',

    // Competition related
    'competition.startCompetition': 'Start Competition',
    'competition.competitionInProgress': 'Competition in Progress',
    'competition.timeRemaining': 'Time Remaining',
    'competition.timeUp': 'Time\'s Up!',
    'competition.competitionCompleted': 'Competition Completed',
    'competition.submitWork': 'Submit Work',
    'competition.hours': 'hours',
    'competition.minutes': 'min',
    'competition.seconds': 'sec',
    'competition.pleaseUploadFiles': 'Please upload your work files first',
    'competition.submitSuccess': 'Work submitted successfully',
    'competition.submitFailed': 'Failed to submit work',
    'competition.confirmSubmit': 'Confirm Submit',
    'competition.submitDescription': 'Please upload your competition work files. Multiple formats are supported.',
    'competition.uploadFiles': 'Upload Files',
    'competition.uploadHint': 'Click to select files or drag files here',
    'competition.supportedFormats': 'Supports PDF, DOC, DOCX formats',
    'competition.uploadedFiles': 'Uploaded Files',
    'competition.uploading': 'Uploading...',
    'competition.submissionSuccessTitle': 'Competition Work Uploaded Successfully',
    'competition.submissionSuccessMessage': 'Your work has been submitted successfully. Thank you for your participation!',
    'competition.surveyTitle': 'Invitation to Fill Out Survey',
    'competition.surveyDescription': 'To better improve our services, we invite you to fill out a brief survey.',
    'competition.fillSurvey': 'Fill Survey',
    'competition.competitionEnded': 'Competition Ended',
    'competition.congratulations': 'Congratulations on completing your creation, please fill out the ',
    'competition.surveyLink': 'survey',
    'competition.congratulationsEnd': '～',
    'competition.congratulationTitle': 'Congratulations on Completing the Competition!',
    'competition.congratulationMessage': 'You have successfully completed this competition. Thank you for your wonderful participation!',
    'competition.contactAssistant': 'If you have any other questions, please contact our assistant',
    'competition.gotIt': 'Got it',

    // Auth
    'auth.loginButton': 'Login',
    'auth.logoutButton': 'Logout',
    'auth.loggedIn': 'Logged In',
    'auth.notLoggedIn': 'Not Logged In',
    'auth.loginRequired': 'Please login first',
    'auth.loginSuccess': 'Login Successful',
    'auth.logoutSuccess': 'Logout Successful',

    // Navigation
    'navigation.feedback': 'Feedback',

    // Feedback
    'feedback.titleLabel': 'Feedback Title',
    'feedback.titlePlaceholder': 'Please enter feedback title',
    'feedback.titleRequired': 'Please enter feedback title',
    'feedback.titleMaxLength': 'Title cannot exceed 100 characters',
    'feedback.contentLabel': 'Feedback Content',
    'feedback.contentPlaceholder': 'Please describe the issue or suggestion in detail. We value every feedback.',
    'feedback.contentRequired': 'Please enter feedback content',
    'feedback.contentMinLength': 'Feedback content must be at least 10 characters',
    'feedback.contentMaxLength': 'Feedback content cannot exceed 1000 characters',
    'feedback.submit': 'Submit Feedback',
    'feedback.submitSuccess': 'Feedback submitted successfully. Thank you for your valuable input!',
    'feedback.submitError': 'Submission failed, please try again later',
    'feedback.loginRequired': 'Please login first to submit feedback',
    'feedback.uploadImages': 'Upload Images',
    'feedback.uploadImagesTip': 'Supports PNG, JPG, GIF formats, max 10MB per file, up to 3 images',
    'feedback.dragOrClick': 'Drag or click to select images',
    'feedback.uploading': 'Uploading...',
    'feedback.fileTooLarge': 'File size cannot exceed 10MB',
    'feedback.maxFilesReached': 'Maximum 3 images allowed',
    'feedback.maxImagesReached': 'Maximum image limit reached',
    'feedback.uploadSuccess': 'Successfully uploaded {count} images',
    'feedback.uploadFailed': 'Image upload failed',

    // Subscription（更新为完整的订阅弹窗翻译）
    'subscription.upgrade': 'Upgrade',
    'subscription.subscribe': 'Subscribe',
    'subscription.renew': 'Renew',
    'subscription.active': 'Active',
    'subscription.inactive': 'Inactive',

    // Apps
    'apps.novaxBase.name': 'NovaX Base',
    'apps.novaxBase.description': 'Core Idea Inspiration',
    'apps.novaxBase.subtitle': 'Single research direction design based on research background',
    'apps.novaxPro.name': 'NovaX Pro',
    'apps.novaxPro.description': 'Personalized Research Planning',
    'apps.novaxPro.subtitle': 'Deep matching based on current research foundation and available conditions',
    'apps.novaxUltra.name': 'NovaX Ultra',
    'apps.novaxUltra.description': 'Building Innovation Ecosystem',
    'apps.novaxUltra.subtitle': 'Strategic topic clusters, beyond single ideas',

    // Workflow nodes
    'workflow.nodes.start': 'Start Node',
    'workflow.nodes.ifElse': 'Conditional Judgment',
    'workflow.nodes.documentExtractor': 'Document Extraction',
    'workflow.nodes.tool': 'Tool Call',
    'workflow.nodes.llm': 'AI Analysis',
    'workflow.nodes.answer': 'Generate Answer',
    'workflow.nodes.assigner': 'Variable Assignment',
    'workflow.nodes.variableAggregator': 'Data Aggregation',
    'workflow.nodes.questionClassifier': 'Question Classification',
    'workflow.nodes.code': 'Code Execution',
    'workflow.nodes.templateTransform': 'Template Transform',
    'workflow.nodes.httpRequest': 'HTTP Request',
    'workflow.nodes.parameterExtractor': 'Parameter Extraction',

    // Mermaid diagram related
    'mermaid.rendering': 'Rendering diagram...',
    'mermaid.retry': 'Retry',
    'mermaid.viewCode': 'View Source',
    'mermaid.hideCode': 'Hide Source',
    'mermaid.sourceCode': 'Source Code',
    'mermaid.copy': 'Copy',
    'mermaid.copied': 'Copied',
    'mermaid.suggestion': 'Suggestion',
    'mermaid.viewOriginalError': 'View Original Error',
    'mermaid.hideOriginalError': 'Hide Original Error',
    'mermaid.error.syntaxError': 'Mermaid Syntax Error',
    'mermaid.error.syntaxErrorMessage': 'The diagram code contains syntax errors and cannot be parsed correctly',
    'mermaid.error.syntaxErrorSuggestion': 'Please check if the diagram type declaration and node connection syntax are correct',
    'mermaid.error.lexicalError': 'Mermaid Lexical Error',
    'mermaid.error.lexicalErrorMessage': 'The diagram code contains unrecognizable characters or symbols',
    'mermaid.error.lexicalErrorSuggestion': 'Please check for spelling errors or unsupported characters',
    'mermaid.error.timeout': 'Rendering Timeout',
    'mermaid.error.timeoutMessage': 'Diagram rendering took too long, possibly due to overly complex code',
    'mermaid.error.timeoutSuggestion': 'Please try simplifying the diagram structure or breaking it into smaller diagrams',
    'mermaid.error.emptyCode': 'Empty Code',
    'mermaid.error.emptyCodeMessage': 'No valid Mermaid diagram code provided',
    'mermaid.error.emptyCodeSuggestion': 'Please provide complete Mermaid diagram code',
    'mermaid.error.renderFailed': 'Diagram Rendering Failed',
    'mermaid.error.renderFailedMessage': 'Unable to render this Mermaid diagram',
    'mermaid.error.renderFailedSuggestion': 'Please check if the code format is correct, or try using other diagram types',

    // Error messages
    'error.pageNotFound': 'Page Not Found',
    'error.pageNotFoundDescription': 'Sorry, the page you are looking for does not exist or has been moved. Please check the URL or return to the homepage to continue browsing.',
    'errors.networkError': 'Network connection error',
    'errors.serverError': 'Server error',
    'errors.unauthorized': 'Unauthorized, please login again',
    'errors.forbidden': 'Access forbidden',
    'errors.notFound': 'Page not found',
    'errors.validationError': 'Input validation failed',

    // Mode selection
    'mode.simple': 'Simple Mode',
    'mode.detailed': 'Pubmed Mode',
    'mode.selectMode': 'Select Mode',
    'mode.simpleDescription': 'Quick core suggestions',
    'mode.detailedDescription': 'In-depth analysis and detailed feedback',

    // Messages
    'messages.saveSuccess': 'Save successful',
    'messages.deleteSuccess': 'Delete successful',
    'messages.updateSuccess': 'Update successful',
    'messages.operationSuccess': 'Operation successful',
    'messages.operationFailed': 'Operation failed',
    'messages.confirmDelete': 'Confirm to delete this item?',
    'messages.processingRequest': 'Processing request...',
    'messages.requestCompleted': 'Request completed',
    'messages.requestFailed': 'Request failed',

    // DataScore Knowledge Base
    'knowledgebase.title': 'Hi, Welcome to Knowledge Base',
    'knowledgebase.subtitle': 'Designed for storing and managing clinical research data',
    'knowledgebase.uploadFile': 'Upload File',
    'knowledgebase.createFolder': 'Create Folder',
    'knowledgebase.folderName': 'Please enter folder name',
    'knowledgebase.folderNamePlaceholder': 'Please enter folder name',
    'knowledgebase.createFolderTitle': 'Create Folder',
    'knowledgebase.create': 'Create',
    'knowledgebase.preview': 'Preview',
    'knowledgebase.download': 'Download',
    'knowledgebase.rename': 'Rename',
    'knowledgebase.delete': 'Delete',
    'knowledgebase.confirmDelete': 'Confirm Delete',
    'knowledgebase.confirmDeleteMessage': 'Are you sure you want to delete this item? This action cannot be undone.',
    'knowledgebase.uploadSuccess': 'Successfully uploaded {count} files',
    'knowledgebase.uploadFailed': 'File upload failed',
    'knowledgebase.folderCreateSuccess': 'Folder created successfully',
    'knowledgebase.folderCreateFailed': 'Folder creation failed',
    'knowledgebase.deleteSuccess': 'Delete successful',
    'knowledgebase.deleteFailed': 'Delete failed',
    'knowledgebase.renameSuccess': 'Rename successful',
    'knowledgebase.renameFailed': 'Rename failed',
    'knowledgebase.downloadSuccess': 'Downloading {name}',
    'knowledgebase.downloadFailed': 'Download failed',
    'knowledgebase.backToParent': 'Back',
    'knowledgebase.rootFolder': 'Root Folder',
    'knowledgebase.folder': 'Folder',
    'knowledgebase.structuredData': 'Structured',
    'knowledgebase.documentData': 'Document',
    'knowledgebase.noFiles': 'No files',
    'knowledgebase.noFilesDescription': 'Drag files here or click upload button to start uploading',
    'knowledgebase.today': 'Today',
    'knowledgebase.yesterday': 'Yesterday',
    'knowledgebase.daysAgo': '{days} days ago',
    'knowledgebase.previewTitle': 'Preview: {name}',
    'knowledgebase.fileSize': 'File Size: {size}',
    'knowledgebase.fileType': 'File Type: {type}',
    'knowledgebase.uploadTime': 'Upload Time: {time}',
    'knowledgebase.previewNotSupported': 'File preview is not supported. You can download the file to view its full content.',
    'knowledgebase.downloadFile': 'Download File',
    'knowledgebase.closePreview': 'Close',
    'knowledgebase.enterFolderName': 'Please enter folder name',
    'knowledgebase.enterNewName': 'Please enter new name',

    // DataScore Artifacts
    'artifacts.title': 'Artifacts',
    'artifacts.noArtifacts': 'No generated artifacts',
    'artifacts.noArtifactsDescription': 'Analysis reports and related files generated by DataScore will appear here',
    'artifacts.debugFillScores': 'Debug: Fill Random Scores',
    'artifacts.radar.sampleSize': 'Sample Size',
    'artifacts.radar.dataCompleteness': 'Data Completeness',
    'artifacts.radar.structureLevel': 'Structure Level',
    'artifacts.radar.endpointEvents': 'Endpoint & Outcome Events',
    'artifacts.radar.innovationPotential': 'Innovation Potential & Extension',
    'artifacts.radarChart': 'Data Quality Scoring Radar Chart',
    'artifacts.categories.sampleSize': 'Sample Size',
    'artifacts.categories.dataCompleteness': 'Data Completeness',
    'artifacts.categories.structureLevel': 'Structure Level',
    'artifacts.categories.endpoints': 'Endpoint & Outcome Events',
    'artifacts.categories.innovation': 'Innovation Potential & Extension',
    'artifacts.categoryTooltip.sampleSize': 'Evaluate the quantity and representativeness of samples in the dataset',
    'artifacts.categoryTooltip.dataCompleteness': 'Evaluate data integrity and missing value situations',
    'artifacts.categoryTooltip.structureLevel': 'Evaluate the level of data structure and standardization',
    'artifacts.categoryTooltip.endpoints': 'Evaluate the clarity of research endpoints and outcome events',
    'artifacts.categoryTooltip.innovation': 'Evaluate the innovation potential and research extensibility of data',
    'artifacts.dragToResize': 'Drag to resize',
    'common.score': 'Score'
  }
}

// 当前语言状态 - 智能初始化
const initializeCurrentLanguage = (): SupportedLanguage => {
  // 1. 优先从URL路径中获取语言
  if (typeof window !== 'undefined') {
    const pathname = window.location.pathname
    const segments = pathname.split('/').filter(Boolean)
    const firstSegment = segments[0]

    if (firstSegment === 'zh' || firstSegment === 'en') {
      console.log('Simple i18n: 从URL路径初始化语言', { path: pathname, language: firstSegment })
      return firstSegment
    }
  }

  // 2. 从localStorage获取保存的语言偏好
  try {
    const saved = localStorage.getItem('language') as SupportedLanguage
    if (saved && (saved === 'zh' || saved === 'en')) {
      console.log('Simple i18n: 从localStorage初始化语言', { language: saved })
      return saved
    }
  } catch (error) {
    console.warn('Simple i18n: localStorage访问失败', error)
  }

  // 3. 使用环境默认语言
  console.log('Simple i18n: 使用环境默认语言', { language: DEFAULT_LANGUAGE })
  return DEFAULT_LANGUAGE
}

let currentLanguage: SupportedLanguage = initializeCurrentLanguage()

// 语言变更监听器
type LanguageChangeListener = (newLang: SupportedLanguage, oldLang: SupportedLanguage) => void
const listeners: LanguageChangeListener[] = []

// 添加语言变更监听器
export const addLanguageChangeListener = (listener: LanguageChangeListener): void => {
  listeners.push(listener)
}

// 移除语言变更监听器
export const removeLanguageChangeListener = (listener: LanguageChangeListener): void => {
  const index = listeners.indexOf(listener)
  if (index > -1) {
    listeners.splice(index, 1)
  }
}

// 触发语言变更事件
const notifyLanguageChange = (newLang: SupportedLanguage, oldLang: SupportedLanguage): void => {
  listeners.forEach(listener => {
    try {
      listener(newLang, oldLang)
    } catch (error) {
      console.error('Language change listener error:', error)
    }
  })
}

// 获取当前语言
export const getCurrentLanguage = (): SupportedLanguage => {
  // 添加调试日志，特别是在API调用时
  if (typeof window !== 'undefined') {
    const pathname = window.location.pathname
    const isApiCall = new Error().stack?.includes('baseRequest') || new Error().stack?.includes('XAiApi')

    if (isApiCall) {
      console.log('🔍 getCurrentLanguage (API调用)', {
        currentLanguage,
        pathname,
        timestamp: new Date().toISOString(),
        stack: new Error().stack?.split('\n').slice(1, 4).join('\n')
      })
    }
  }

  return currentLanguage
}

// 设置语言
export const setLanguage = (lang: SupportedLanguage): void => {
  if (lang === currentLanguage) return

  const oldLang = currentLanguage
  currentLanguage = lang
  localStorage.setItem('language', lang)

  console.log('🌐 setLanguage: 语言已更新', {
    from: oldLang,
    to: lang,
    pathname: typeof window !== 'undefined' ? window.location.pathname : 'N/A',
    timestamp: new Date().toISOString()
  })

  // 通知所有监听器
  notifyLanguageChange(lang, oldLang)
}

// 翻译函数
export const t = (key: string): string => {
  const langTranslations = translations[currentLanguage]
  const result = langTranslations[key as keyof typeof langTranslations] || key
  // console.log(`[i18n] Translating "${key}" to "${result}" (lang: ${currentLanguage})`)
  return result
}

// 初始化语言 - 改进版本
export const initLanguage = (): void => {
  const newLanguage = initializeCurrentLanguage()
  if (newLanguage !== currentLanguage) {
    console.log('Simple i18n: 重新初始化语言', {
      from: currentLanguage,
      to: newLanguage
    })
    currentLanguage = newLanguage
  }
}

// 自动初始化（在模块加载时）
if (typeof window !== 'undefined') {
  // 在浏览器环境中自动初始化
  initLanguage()
}

// 语言配置
export const LANGUAGE_CONFIG = {
  zh: {
    code: 'zh' as const,
    name: '中文',
    flag: '🇨🇳'
  },
  en: {
    code: 'en' as const,
    name: 'EN',
    flag: '🇺🇸'
  }
}

export const SUPPORTED_LANGUAGES = ['zh', 'en'] as const
