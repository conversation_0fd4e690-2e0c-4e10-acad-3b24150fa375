import { SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE, APP_NAME_CONFIG } from './index'
import { getDefaultAppName } from '../utils/envConfig'
import type { SupportedLanguage } from './index'

/**
 * 验证语言代码是否有效
 */
export const isValidLanguage = (lang: string): lang is SupportedLanguage => {
  return SUPPORTED_LANGUAGES.includes(lang as SupportedLanguage)
}

/**
 * 获取有效的语言代码，如果无效则返回默认语言
 */
export const getValidLanguage = (lang?: string): SupportedLanguage => {
  if (!lang) return DEFAULT_LANGUAGE
  return isValidLanguage(lang) ? lang : DEFAULT_LANGUAGE
}

/**
 * 从URL路径中提取语言代码
 * 路径格式: /[lang]/[app-name]/[session-id?]
 */
export const extractLanguageFromPath = (pathname: string): SupportedLanguage => {
  const segments = pathname.split('/').filter(Boolean)
  const firstSegment = segments[0]
  
  if (firstSegment && isValidLanguage(firstSegment)) {
    return firstSegment
  }
  
  return DEFAULT_LANGUAGE
}

/**
 * 从URL路径中提取应用名称
 * 路径格式: /[lang]/[app-name]/[session-id?]
 */
export const extractAppNameFromPath = (pathname: string): string => {
  const segments = pathname.split('/').filter(Boolean)
  
  // 如果第一个段是语言代码，应用名称在第二个段
  if (segments.length >= 2 && isValidLanguage(segments[0])) {
    return segments[1]
  }
  
  // 如果第一个段不是语言代码，应用名称在第一个段
  if (segments.length >= 1) {
    return segments[0]
  }
  
  return getDefaultAppName() // 默认应用名称
}

/**
 * 从URL路径中提取会话ID
 * 路径格式: /[lang]/[app-name]/[session-id?]
 */
export const extractSessionIdFromPath = (pathname: string): string | null => {
  const segments = pathname.split('/').filter(Boolean)
  
  // 如果第一个段是语言代码，会话ID在第三个段
  if (segments.length >= 3 && isValidLanguage(segments[0])) {
    return segments[2]
  }
  
  // 如果第一个段不是语言代码，会话ID在第二个段
  if (segments.length >= 2 && !isValidLanguage(segments[0])) {
    return segments[1]
  }
  
  return null
}

/**
 * 构建国际化路径
 * @param lang 语言代码
 * @param appName 应用名称
 * @param sessionId 会话ID（可选）
 */
export const buildI18nPath = (
  lang: SupportedLanguage,
  appName: string,
  sessionId?: string
): string => {
  const basePath = `/${lang}/${appName}`
  return sessionId ? `${basePath}/${sessionId}` : basePath
}

/**
 * 构建聊天路径
 * @param lang 语言代码
 * @param appName 应用名称
 * @param sessionId 会话ID（可选）
 */
export const buildChatPath = (
  lang: SupportedLanguage,
  appName: string,
  sessionId?: string
): string => {
  return buildI18nPath(lang, appName, sessionId)
}

/**
 * 构建应用首页路径
 * @param lang 语言代码
 * @param appName 应用名称
 */
export const buildHomePath = (
  lang: SupportedLanguage,
  appName: string = getDefaultAppName()
): string => {
  return buildI18nPath(lang, appName)
}

/**
 * 获取当前路径的语言切换版本
 * @param currentPath 当前路径
 * @param targetLang 目标语言
 */
export const getLanguageSwitchPath = (
  currentPath: string,
  targetLang: SupportedLanguage
): string => {
  const currentLang = extractLanguageFromPath(currentPath)
  const appName = extractAppNameFromPath(currentPath)
  const sessionId = extractSessionIdFromPath(currentPath)

  // 如果当前路径已经包含目标语言，直接返回
  if (currentLang === targetLang) {
    return currentPath
  }

  return buildI18nPath(targetLang, appName, sessionId || undefined)
}

/**
 * 标准化路径，确保包含语言代码
 * @param pathname 原始路径
 * @param defaultLang 默认语言
 */
export const normalizePath = (
  pathname: string,
  defaultLang: SupportedLanguage = DEFAULT_LANGUAGE
): string => {
  const segments = pathname.split('/').filter(Boolean)
  
  // 如果路径为空或只有根路径，返回默认语言的首页
  if (segments.length === 0) {
    return buildHomePath(defaultLang)
  }
  
  // 如果第一个段不是语言代码，添加默认语言
  if (!isValidLanguage(segments[0])) {
    return `/${defaultLang}${pathname}`
  }
  
  return pathname
}

/**
 * 获取应用的本地化名称
 * @param appKey 应用键名
 * @param lang 语言代码
 */
export const getLocalizedAppName = (
  appKey: string,
  lang: SupportedLanguage
): string => {
  const config = APP_NAME_CONFIG[appKey as keyof typeof APP_NAME_CONFIG]
  return config ? config[lang] : appKey
}

/**
 * 从localStorage获取保存的语言偏好
 */
export const getSavedLanguage = (): SupportedLanguage => {
  try {
    const saved = localStorage.getItem('i18nextLng')
    return getValidLanguage(saved || undefined)
  } catch {
    return DEFAULT_LANGUAGE
  }
}

/**
 * 保存语言偏好到localStorage
 */
export const saveLanguagePreference = (lang: SupportedLanguage): void => {
  try {
    localStorage.setItem('i18nextLng', lang)
  } catch (error) {
    console.warn('Failed to save language preference:', error)
  }
}

/**
 * 检测浏览器语言偏好
 */
export const detectBrowserLanguage = (): SupportedLanguage => {
  try {
    const browserLang = navigator.language.split('-')[0]
    return getValidLanguage(browserLang)
  } catch {
    return DEFAULT_LANGUAGE
  }
}
