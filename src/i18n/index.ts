import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import { getDefaultLanguage } from '../utils/envConfig'

// 导入翻译资源
import zhCN from './locales/zh-CN.json'
import enUS from './locales/en-US.json'

// 支持的语言列表
export const SUPPORTED_LANGUAGES = ['zh', 'en'] as const
export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number]

// 语言配置
export const LANGUAGE_CONFIG = {
  zh: {
    code: 'zh',
    name: '中文',
    flag: '🇨🇳',
    locale: 'zh-CN'
  },
  en: {
    code: 'en', 
    name: 'English',
    flag: '🇺🇸',
    locale: 'en-US'
  }
} as const

// 默认语言 - 根据环境动态设置
// 国际站环境默认为英文，其他环境默认为中文
export const DEFAULT_LANGUAGE: SupportedLanguage = getDefaultLanguage()

// 应用名称配置
export const APP_NAME_CONFIG = {
  'novax-base': {
    zh: 'novax-base',
    en: 'novax-base'
  }
} as const

// 简化的语言配置

// 初始化i18n
i18n
  .use(initReactI18next)
  .init({
    // 翻译资源
    resources: {
      zh: {
        translation: zhCN
      },
      en: {
        translation: enUS
      }
    },

    // 默认语言
    fallbackLng: DEFAULT_LANGUAGE,

    // 支持的语言
    supportedLngs: SUPPORTED_LANGUAGES,

    // 调试模式（开发环境开启）
    debug: false,

    // 插值配置
    interpolation: {
      escapeValue: false // React已经处理了XSS
    },

    // 键分隔符
    keySeparator: '.',

    // 返回对象而不是字符串
    returnObjects: false,

    // 返回空字符串而不是key
    returnEmptyString: false,

    // 返回null而不是key
    returnNull: false
  })

export default i18n
