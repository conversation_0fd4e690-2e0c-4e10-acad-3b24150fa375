import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate, useLocation } from 'react-router-dom'
import type { SupportedLanguage } from './index'
import {
  extractLanguageFromPath,
  extractAppNameFromPath,
  extractSessionIdFromPath,
  getLanguageSwitchPath,
  buildI18nPath,
  getValidLanguage,
  saveLanguagePreference,
  getSavedLanguage
} from './utils'
import { getDefaultAppName } from '../utils/envConfig'

/**
 * 国际化路由Hook
 * 提供语言切换、路径解析等功能
 */
export const useI18nRouter = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { i18n } = useTranslation()

  // 从当前路径解析信息
  const currentLanguage = extractLanguageFromPath(location.pathname)
  const currentAppName = extractAppNameFromPath(location.pathname)
  const currentSessionId = extractSessionIdFromPath(location.pathname)

  /**
   * 切换语言
   * @param targetLang 目标语言
   * @param preservePath 是否保持当前路径结构
   */
  const switchLanguage = useCallback(
    (targetLang: SupportedLanguage, preservePath: boolean = true) => {
      // 更新i18n语言
      i18n.changeLanguage(targetLang)
      
      // 保存语言偏好
      saveLanguagePreference(targetLang)

      if (preservePath) {
        // 保持当前路径结构，只切换语言
        const newPath = getLanguageSwitchPath(location.pathname, targetLang)
        navigate(newPath, { replace: true })
      } else {
        // 跳转到目标语言的首页
        navigate(`/${targetLang}/${getDefaultAppName()}`, { replace: true })
      }
    },
    [i18n, location.pathname, navigate]
  )

  /**
   * 导航到指定应用
   * @param appName 应用名称
   * @param sessionId 会话ID（可选）
   * @param lang 语言（可选，默认使用当前语言）
   */
  const navigateToApp = useCallback(
    (appName: string, sessionId?: string, lang?: SupportedLanguage) => {
      const targetLang = lang || currentLanguage
      const path = buildI18nPath(targetLang, appName, sessionId)
      navigate(path)
    },
    [currentLanguage, navigate]
  )

  /**
   * 导航到聊天页面
   * @param appName 应用名称
   * @param sessionId 会话ID（可选）
   * @param lang 语言（可选，默认使用当前语言）
   */
  const navigateToChat = useCallback(
    (appName: string, sessionId?: string, lang?: SupportedLanguage) => {
      navigateToApp(appName, sessionId, lang)
    },
    [navigateToApp]
  )

  /**
   * 导航到应用首页
   * @param appName 应用名称（可选，默认novax-base）
   * @param lang 语言（可选，默认使用当前语言）
   */
  const navigateToHome = useCallback(
    (appName: string = 'novax-base', lang?: SupportedLanguage) => {
      navigateToApp(appName, undefined, lang)
    },
    [navigateToApp]
  )

  return {
    // 当前状态
    currentLanguage,
    currentAppName,
    currentSessionId,
    
    // 导航方法
    switchLanguage,
    navigateToApp,
    navigateToChat,
    navigateToHome,
    
    // 路径信息
    pathname: location.pathname,
    search: location.search,
    hash: location.hash
  }
}

/**
 * 语言状态Hook
 * 管理语言状态和偏好设置
 */
export const useLanguage = () => {
  const { i18n } = useTranslation()
  const [isChanging, setIsChanging] = useState(false)

  // 当前语言
  const currentLanguage = getValidLanguage(i18n.language) as SupportedLanguage

  /**
   * 切换语言（仅更新i18n，不处理路由）
   * @param lang 目标语言
   */
  const changeLanguage = useCallback(
    async (lang: SupportedLanguage) => {
      if (lang === currentLanguage) return

      setIsChanging(true)
      try {
        await i18n.changeLanguage(lang)
        saveLanguagePreference(lang)
      } catch (error) {
        console.error('Failed to change language:', error)
      } finally {
        setIsChanging(false)
      }
    },
    [currentLanguage, i18n]
  )

  /**
   * 初始化语言设置
   */
  const initializeLanguage = useCallback(() => {
    const savedLang = getSavedLanguage()
    if (savedLang !== currentLanguage) {
      changeLanguage(savedLang)
    }
  }, [currentLanguage, changeLanguage])

  return {
    currentLanguage,
    isChanging,
    changeLanguage,
    initializeLanguage
  }
}

/**
 * 路径同步Hook
 * 确保URL路径与i18n语言状态同步
 */
export const usePathLanguageSync = () => {
  const { i18n } = useTranslation()
  const location = useLocation()
  const navigate = useNavigate()

  useEffect(() => {
    const pathLang = extractLanguageFromPath(location.pathname)
    const currentLang = getValidLanguage(i18n.language) as SupportedLanguage

    // 如果路径中的语言与i18n当前语言不一致，更新i18n
    if (pathLang !== currentLang) {
      i18n.changeLanguage(pathLang)
      saveLanguagePreference(pathLang)
    }
  }, [location.pathname, i18n])
}

/**
 * 翻译Hook的增强版本
 * 提供额外的翻译工具方法
 */
export const useI18nTranslation = (namespace?: string) => {
  const { t, i18n } = useTranslation(namespace)

  /**
   * 安全的翻译方法，如果key不存在则返回key本身
   * @param key 翻译key
   * @param options 翻译选项
   */
  const safeT = useCallback(
    (key: string, options?: any): string => {
      const result = t(key, options)
      // 如果翻译结果等于key，说明没有找到翻译
      return typeof result === 'string' ? result : key
    },
    [t]
  )

  /**
   * 获取当前语言的翻译
   * @param key 翻译key
   * @param fallback 备用文本
   */
  const getTranslation = useCallback(
    (key: string, fallback?: string): string => {
      const result = t(key)
      const resultStr = typeof result === 'string' ? result : key
      return resultStr === key ? (fallback || key) : resultStr
    },
    [t]
  )

  /**
   * 检查翻译key是否存在
   * @param key 翻译key
   */
  const hasTranslation = useCallback(
    (key: string): boolean => {
      const result = t(key)
      return typeof result === 'string' && result !== key
    },
    [t]
  )

  return {
    t: safeT,
    i18n,
    getTranslation,
    hasTranslation,
    currentLanguage: getValidLanguage(i18n.language) as SupportedLanguage
  }
}
