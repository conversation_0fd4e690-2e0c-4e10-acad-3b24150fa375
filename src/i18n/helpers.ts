import type { SupportedLanguage } from './index'
import { SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE, LANGUAGE_CONFIG } from './index'

/**
 * 格式化数字（根据语言环境）
 */
export const formatNumber = (
  value: number,
  lang: SupportedLanguage,
  options?: Intl.NumberFormatOptions
): string => {
  const locale = LANGUAGE_CONFIG[lang].locale
  return new Intl.NumberFormat(locale, options).format(value)
}

/**
 * 格式化货币（根据语言环境）
 */
export const formatCurrency = (
  value: number,
  lang: SupportedLanguage,
  currency: string = 'CNY',
  options?: Intl.NumberFormatOptions
): string => {
  const locale = LANGUAGE_CONFIG[lang].locale
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    ...options
  }).format(value)
}

/**
 * 格式化日期（根据语言环境）
 */
export const formatDate = (
  date: Date | string | number,
  lang: SupportedLanguage,
  options?: Intl.DateTimeFormatOptions
): string => {
  const locale = LANGUAGE_CONFIG[lang].locale
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  
  return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(dateObj)
}

/**
 * 格式化相对时间（根据语言环境）
 */
export const formatRelativeTime = (
  date: Date | string | number,
  lang: SupportedLanguage,
  options?: Intl.RelativeTimeFormatOptions
): string => {
  const locale = LANGUAGE_CONFIG[lang].locale
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((dateObj.getTime() - now.getTime()) / 1000)
  
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto', ...options })
  
  // 计算合适的时间单位
  const absDiff = Math.abs(diffInSeconds)
  
  if (absDiff < 60) {
    return rtf.format(diffInSeconds, 'second')
  } else if (absDiff < 3600) {
    return rtf.format(Math.floor(diffInSeconds / 60), 'minute')
  } else if (absDiff < 86400) {
    return rtf.format(Math.floor(diffInSeconds / 3600), 'hour')
  } else if (absDiff < 2592000) {
    return rtf.format(Math.floor(diffInSeconds / 86400), 'day')
  } else if (absDiff < 31536000) {
    return rtf.format(Math.floor(diffInSeconds / 2592000), 'month')
  } else {
    return rtf.format(Math.floor(diffInSeconds / 31536000), 'year')
  }
}

/**
 * 获取语言的文本方向
 */
export const getTextDirection = (lang: SupportedLanguage): 'ltr' | 'rtl' => {
  // 目前支持的语言都是从左到右
  return 'ltr'
}

/**
 * 获取语言的显示名称（本地化）
 */
export const getLanguageDisplayName = (
  lang: SupportedLanguage,
  displayLang: SupportedLanguage
): string => {
  try {
    const displayLocale = LANGUAGE_CONFIG[displayLang].locale
    const targetLocale = LANGUAGE_CONFIG[lang].locale
    
    const displayNames = new Intl.DisplayNames([displayLocale], { type: 'language' })
    return displayNames.of(targetLocale) || LANGUAGE_CONFIG[lang].name
  } catch {
    return LANGUAGE_CONFIG[lang].name
  }
}

/**
 * 检查是否为RTL语言
 */
export const isRTLLanguage = (lang: SupportedLanguage): boolean => {
  return getTextDirection(lang) === 'rtl'
}

/**
 * 获取语言的字体族建议
 */
export const getFontFamily = (lang: SupportedLanguage): string => {
  switch (lang) {
    case 'zh':
      return '"PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif'
    case 'en':
      return '"Inter", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif'
    default:
      return 'system-ui, sans-serif'
  }
}

/**
 * 获取语言的排序规则
 */
export const getCollator = (
  lang: SupportedLanguage,
  options?: Intl.CollatorOptions
): Intl.Collator => {
  const locale = LANGUAGE_CONFIG[lang].locale
  return new Intl.Collator(locale, options)
}

/**
 * 根据语言排序字符串数组
 */
export const sortStrings = (
  strings: string[],
  lang: SupportedLanguage,
  options?: Intl.CollatorOptions
): string[] => {
  const collator = getCollator(lang, options)
  return [...strings].sort(collator.compare)
}

/**
 * 获取复数规则
 */
export const getPluralRule = (
  count: number,
  lang: SupportedLanguage
): Intl.LDMLPluralRule => {
  const locale = LANGUAGE_CONFIG[lang].locale
  const pr = new Intl.PluralRules(locale)
  return pr.select(count)
}

/**
 * 格式化文件大小（根据语言环境）
 */
export const formatFileSize = (
  bytes: number,
  lang: SupportedLanguage,
  decimals: number = 2
): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = lang === 'zh' 
    ? ['字节', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    : ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const value = parseFloat((bytes / Math.pow(k, i)).toFixed(dm))
  
  return `${formatNumber(value, lang)} ${sizes[i]}`
}

/**
 * 获取语言环境的小数分隔符
 */
export const getDecimalSeparator = (lang: SupportedLanguage): string => {
  const locale = LANGUAGE_CONFIG[lang].locale
  const formatter = new Intl.NumberFormat(locale)
  const parts = formatter.formatToParts(1.1)
  const decimalPart = parts.find(part => part.type === 'decimal')
  return decimalPart?.value || '.'
}

/**
 * 获取语言环境的千位分隔符
 */
export const getThousandsSeparator = (lang: SupportedLanguage): string => {
  const locale = LANGUAGE_CONFIG[lang].locale
  const formatter = new Intl.NumberFormat(locale)
  const parts = formatter.formatToParts(1000)
  const groupPart = parts.find(part => part.type === 'group')
  return groupPart?.value || ','
}

/**
 * 检查字符串是否包含特定语言的字符
 */
export const containsLanguageChars = (text: string, lang: SupportedLanguage): boolean => {
  switch (lang) {
    case 'zh':
      // 检查是否包含中文字符
      return /[\u4e00-\u9fff]/.test(text)
    case 'en':
      // 检查是否包含英文字符
      return /[a-zA-Z]/.test(text)
    default:
      return false
  }
}

/**
 * 自动检测文本语言
 */
export const detectTextLanguage = (text: string): SupportedLanguage => {
  // 简单的语言检测逻辑
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
  const englishChars = (text.match(/[a-zA-Z]/g) || []).length
  
  if (chineseChars > englishChars) {
    return 'zh'
  } else if (englishChars > 0) {
    return 'en'
  }
  
  return DEFAULT_LANGUAGE
}

/**
 * 获取语言环境的首选字体大小
 */
export const getPreferredFontSize = (lang: SupportedLanguage): string => {
  switch (lang) {
    case 'zh':
      return '14px' // 中文通常需要稍大的字体
    case 'en':
      return '13px'
    default:
      return '14px'
  }
}

/**
 * 获取语言环境的行高建议
 */
export const getPreferredLineHeight = (lang: SupportedLanguage): number => {
  switch (lang) {
    case 'zh':
      return 1.6 // 中文需要更大的行高
    case 'en':
      return 1.5
    default:
      return 1.5
  }
}
