import React, { createContext, useContext, useState, useRef, useEffect, ReactNode } from 'react';
import { useCompetitionPermission } from '../hooks/useCompetitionPermission';
import Cookies from 'js-cookie';

// 比赛状态接口
interface CompetitionState {
  competitionStarted: boolean;
  competitionTimeRemaining: number;
  competitionCompleted: boolean;
  competitionDuration: number; // 比赛总时长（秒）
}

// Context接口
interface CompetitionContextType {
  // 状态
  competitionStarted: boolean;
  competitionTimeRemaining: number;
  competitionCompleted: boolean;
  competitionDuration: number;
  isStateInitialized: boolean; // 状态是否已初始化

  // 权限状态
  hasCompetitionPermission: boolean;
  isPermissionLoading: boolean;

  // 操作函数
  startCompetition: (duration?: number) => void;
  endCompetition: () => void;
  resetCompetition: () => void;

  // 状态查询
  getProgress: () => number; // 返回0-100的进度百分比

  // 时间到期回调
  setTimeExpiredCallback: (callback: () => void) => void;
}

// 创建Context
const CompetitionContext = createContext<CompetitionContextType | undefined>(undefined);

// Provider组件的Props
interface CompetitionProviderProps {
  children: ReactNode;
}

// 默认比赛时长（90分钟）
const DEFAULT_COMPETITION_DURATION = 90 * 60;

// 获取用户信息
const getUserInfo = () => {
  try {
    const userInfoString = Cookies.get('userInfo');
    const medxyToken = Cookies.get('medxyToken');

    if (!userInfoString || !medxyToken) {
      return null;
    }

    return JSON.parse(userInfoString);
  } catch (error) {
    console.error('解析用户信息失败:', error);
    return null;
  }
};

// 获取用户ID
const getUserId = () => {
  const userInfo = getUserInfo();
  if (!userInfo) return 'anonymous';

  return userInfo?.socialUserId || userInfo?.plaintextUserId || userInfo?.userId || 'anonymous';
};

// 生成带用户ID的缓存键名
const getCompetitionCacheKey = (baseKey: string) => {
  const userId = getUserId();
  return `${baseKey}_${userId}`;
};

// 缓存工具函数 - 使用Cookie存储，时效为一天
const saveToCache = (key: string, value: any) => {
  try {
    Cookies.set(key, JSON.stringify(value), { expires: 1 }); // 1天过期
  } catch (error) {
    console.error('保存到Cookie失败:', error);
  }
};

const getFromCache = (key: string) => {
  try {
    const value = Cookies.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error('从Cookie读取失败:', error);
    return null;
  }
};

const removeFromCache = (key: string) => {
  try {
    Cookies.remove(key);
  } catch (error) {
    console.error('从Cookie删除失败:', error);
  }
};

// Provider组件
export const CompetitionProvider: React.FC<CompetitionProviderProps> = ({ children }) => {
  const [competitionStarted, setCompetitionStarted] = useState(false);
  const [competitionTimeRemaining, setCompetitionTimeRemaining] = useState(DEFAULT_COMPETITION_DURATION);
  const [competitionCompleted, setCompetitionCompleted] = useState(false);
  const [competitionDuration, setCompetitionDuration] = useState(DEFAULT_COMPETITION_DURATION);
  const [isStateInitialized, setIsStateInitialized] = useState(false); // 状态是否已初始化

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const timeExpiredCallbackRef = useRef<(() => void) | null>(null);

  // 使用权限检查Hook
  const { hasCompetitionPermission, isPermissionLoading } = useCompetitionPermission();

  // 清理比赛缓存
  const clearCompetitionCache = () => {
    removeFromCache(getCompetitionCacheKey('competition_start_time'));
    removeFromCache(getCompetitionCacheKey('competition_duration'));
    removeFromCache(getCompetitionCacheKey('competition_remaining_time'));
    removeFromCache(getCompetitionCacheKey('competition_completed'));
  };

  // 启动定时器的函数
  const startTimer = (initialTime: number) => {
    // 清除之前的定时器
    clearTimer();

    // 开始新的定时器
    timerRef.current = setInterval(() => {
      setCompetitionTimeRemaining(prev => {
        const newTime = prev - 1;

        if (newTime <= 0) {
          // 时间到，结束比赛
          clearTimer();
          setCompetitionCompleted(true);
          // 保存完成状态到缓存，而不是清理缓存
          saveToCache(getCompetitionCacheKey('competition_completed'), true);
          console.log('比赛时间到！保存完成状态到缓存');

          // 调用时间到期回调
          if (timeExpiredCallbackRef.current) {
            timeExpiredCallbackRef.current();
          }

          return 0;
        }

        // 更新缓存中的剩余时间
        saveToCache(getCompetitionCacheKey('competition_remaining_time'), newTime);
        return newTime;
      });
    }, 1000);
  };

  // 初始化比赛状态
  const initializeCompetitionState = () => {
    // 防止重复初始化
    if (isStateInitialized) {
      console.log('⚠️ CompetitionContext: 状态已初始化，跳过重复初始化');
      return;
    }

    console.log('🚀 CompetitionContext: 开始初始化比赛状态');

    const userInfo = getUserInfo();
    const userId = getUserId();

    console.log('👤 CompetitionContext: 用户信息检查', {
      hasUserInfo: !!userInfo,
      userId,
      isAnonymous: userId === 'anonymous'
    });

    // 如果用户信息还没加载完成，延迟初始化
    if (!userInfo || userId === 'anonymous') {
      console.log('⏳ CompetitionContext: 用户信息未完全加载，等待用户信息加载');
      // 不要设置 setIsStateInitialized(true)，让备用机制处理
      return;
    }

    const startTimeKey = getCompetitionCacheKey('competition_start_time');
    const durationKey = getCompetitionCacheKey('competition_duration');
    const completedKey = getCompetitionCacheKey('competition_completed');

    const startTime = getFromCache(startTimeKey);
    const duration = getFromCache(durationKey);
    const isCompleted = getFromCache(completedKey);

    console.log('📦 CompetitionContext: 缓存数据检查', {
      startTimeKey,
      durationKey,
      completedKey,
      startTime,
      duration,
      isCompleted
    });

    // 如果缓存中标记为已完成，直接恢复完成状态
    if (isCompleted) {
      setCompetitionStarted(true);
      setCompetitionCompleted(true);
      setCompetitionTimeRemaining(0);
      setIsStateInitialized(true);
      console.log('✅ 从缓存恢复比赛完成状态');
      return;
    }

    if (startTime && duration) {
      const now = Date.now();
      const elapsed = Math.floor((now - startTime) / 1000);
      const remaining = Math.max(0, duration - elapsed);

      console.log('⏰ CompetitionContext: 时间计算', {
        startTime: new Date(startTime).toLocaleString(),
        duration: `${duration}秒`,
        elapsed: `${elapsed}秒`,
        remaining: `${remaining}秒`
      });

      if (remaining > 0) {
        // 比赛还在进行中，恢复状态
        setCompetitionStarted(true);
        setCompetitionTimeRemaining(remaining);
        setCompetitionDuration(duration);
        setCompetitionCompleted(false);
        setIsStateInitialized(true);

        // 重新启动定时器
        startTimer(remaining);

        console.log('✅ 从缓存恢复比赛状态，剩余时间:', remaining);
      } else {
        // 比赛时间已到，标记为完成并保存到缓存
        setCompetitionStarted(true);
        setCompetitionCompleted(true);
        setCompetitionTimeRemaining(0);
        setIsStateInitialized(true);
        saveToCache(getCompetitionCacheKey('competition_completed'), true);
        console.log('✅ 比赛时间已到，标记为完成');
      }
    } else {
      // 没有比赛状态需要恢复
      setIsStateInitialized(true);
      console.log('✅ 没有比赛状态需要恢复，初始化完成');
    }
  };

  // 组件初始化时立即尝试初始化状态
  useEffect(() => {
    initializeCompetitionState();
  }, []);

  // 监听用户信息变化，重新初始化状态（只执行一次）
  useEffect(() => {
    const handleUserInfoUpdate = () => {
      console.log('🔄 CompetitionContext: 用户信息更新，重新初始化状态');
      // 只有在未初始化时才重新初始化
      if (!isStateInitialized) {
        initializeCompetitionState();
      }
    };

    // 监听用户信息更新事件
    window.addEventListener('userInfoUpdated', handleUserInfoUpdate);

    // 定期检查用户信息是否已加载（备用机制）
    const checkUserInfoInterval = setInterval(() => {
      const userInfo = getUserInfo();
      if (userInfo && getUserId() !== 'anonymous' && !isStateInitialized) {
        console.log('🔄 CompetitionContext: 定期检查发现用户信息已加载，重新初始化');
        initializeCompetitionState();
      }
    }, 1000); // 每1秒检查一次

    // 10秒后停止检查
    const timeoutId = setTimeout(() => {
      clearInterval(checkUserInfoInterval);
      if (!isStateInitialized) {
        console.log('⚠️ CompetitionContext: 10秒后仍未初始化，强制初始化（可能用户未登录）');
        // 强制标记为已初始化，避免无限等待
        setIsStateInitialized(true);
      }
    }, 10000);

    return () => {
      window.removeEventListener('userInfoUpdated', handleUserInfoUpdate);
      clearInterval(checkUserInfoInterval);
      clearTimeout(timeoutId);
    };
  }, []); // ✅ 移除依赖，只执行一次

  // 清除定时器
  const clearTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // 开始比赛
  const startCompetition = (duration: number = DEFAULT_COMPETITION_DURATION) => {
    const startTime = Date.now();

    setCompetitionStarted(true);
    setCompetitionCompleted(false);
    setCompetitionDuration(duration);
    setCompetitionTimeRemaining(duration);
    setIsStateInitialized(true); // 开始比赛时状态已初始化

    // 保存到缓存
    saveToCache(getCompetitionCacheKey('competition_start_time'), startTime);
    saveToCache(getCompetitionCacheKey('competition_duration'), duration);
    saveToCache(getCompetitionCacheKey('competition_remaining_time'), duration);

    console.log('比赛开始，保存到缓存:', { startTime, duration });

    // 启动定时器
    startTimer(duration);
  };

  // 结束比赛
  const endCompetition = () => {
    clearTimer();
    setCompetitionCompleted(true);
    // 保存完成状态到缓存
    saveToCache(getCompetitionCacheKey('competition_completed'), true);
    console.log('手动结束比赛，保存完成状态到缓存');
  };

  // 重置比赛
  const resetCompetition = () => {
    clearTimer();
    setCompetitionStarted(false);
    setCompetitionCompleted(false);
    setCompetitionTimeRemaining(competitionDuration);
    setIsStateInitialized(true); // 重置后状态已初始化
    clearCompetitionCache();
    console.log('重置比赛，清理缓存');
  };



  // 获取进度百分比
  const getProgress = () => {
    if (competitionDuration === 0) return 0;
    return Math.max(0, (competitionTimeRemaining / competitionDuration) * 100);
  };

  // 设置时间到期回调
  const setTimeExpiredCallback = (callback: () => void) => {
    timeExpiredCallbackRef.current = callback;
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearTimer();
    };
  }, []);



  const contextValue: CompetitionContextType = {
    // 状态
    competitionStarted,
    competitionTimeRemaining,
    competitionCompleted,
    competitionDuration,
    isStateInitialized,

    // 权限状态
    hasCompetitionPermission,
    isPermissionLoading,

    // 操作函数
    startCompetition,
    endCompetition,
    resetCompetition,

    // 状态查询
    getProgress,

    // 时间到期回调
    setTimeExpiredCallback,
  };

  return (
    <CompetitionContext.Provider value={contextValue}>
      {children}
    </CompetitionContext.Provider>
  );
};

// Hook for using the competition context
export const useCompetition = (): CompetitionContextType => {
  const context = useContext(CompetitionContext);
  if (context === undefined) {
    throw new Error('useCompetition must be used within a CompetitionProvider');
  }
  return context;
};

export default CompetitionContext;
