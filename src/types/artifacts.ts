export interface DataScoreRadarChartProps {
  scores?: {
    sampleSize: number
    dataCompleteness: number
    structureLevel: number
    endpoints: number
    innovation: number
  }
  isLoading?: boolean
}

export interface RadarChartData {
  category: string
  value: number
  fullMark: number
  key: string
}

export type CategoryTooltipKey = 
  | 'artifacts.categoryTooltip.sampleSize'
  | 'artifacts.categoryTooltip.dataCompleteness'
  | 'artifacts.categoryTooltip.structureLevel'
  | 'artifacts.categoryTooltip.endpoints'
  | 'artifacts.categoryTooltip.innovation'