import { useState, useEffect, useMemo } from 'react';
import Cookies from 'js-cookie';
import { XAiApi } from '../api/src/xai-api';
import { getApiConfig } from '../utils/apiConfig';

/**
 * 比赛权限检查Hook
 * 
 * 功能：
 * 1. 检查用户登录状态
 * 2. 获取授权用户列表
 * 3. 验证当前用户是否有比赛权限
 */
export const useCompetitionPermission = () => {
  const [hasCompetitionPermission, setHasCompetitionPermission] = useState(false);
  const [isPermissionLoading, setIsPermissionLoading] = useState(true);
  const [gameUsers, setGameUsers] = useState<number[]>([]);

  // 创建 XAiApi 实例
  const xAiApi = useMemo(() => {
    const apiConfig = getApiConfig();
    return new XAiApi({
      user: apiConfig.user,
      apiBase: apiConfig.apiBase,
      medxyToken: apiConfig.medxyToken
    });
  }, []);

  // 获取用户信息
  const getUserInfo = () => {
    try {
      const userInfoString = Cookies.get('userInfo');
      const medxyToken = Cookies.get('medxyToken');
      
      if (!userInfoString || !medxyToken) {
        return null;
      }
      
      return JSON.parse(userInfoString);
    } catch (error) {
      console.error('解析用户信息失败:', error);
      return null;
    }
  };

  // 获取授权用户列表
  const fetchGameUsers = async () => {
    try {
      console.log('🔍 CompetitionPermission: 开始获取游戏用户列表');
      
      const response = await xAiApi.getConfigByKeyFromCache({
        configKey: 'game_users'
      });

      console.log('📋 CompetitionPermission: 获取到的配置数据:', response);

      if (response && response && response.game_users) {
        const users = response.game_users;
        console.log('✅ CompetitionPermission: 解析到的游戏用户列表:', users);
        setGameUsers(users);
        return users;
      } else {
        console.warn('⚠️ CompetitionPermission: 配置数据格式不正确或为空');
        setGameUsers([]);
        return [];
      }
    } catch (error) {
      console.error('❌ CompetitionPermission: 获取游戏用户列表失败:', error);
      setGameUsers([]);
      return [];
    }
  };

  // 检查用户权限
  const checkPermission = async () => {
    setIsPermissionLoading(true);
    
    try {
      // 1. 检查用户登录状态
      const userInfo = getUserInfo();
      
      if (!userInfo) {
        console.log('🚫 CompetitionPermission: 用户未登录，隐藏比赛功能');
        setHasCompetitionPermission(false);
        setIsPermissionLoading(false);
        return;
      }

      console.log('👤 CompetitionPermission: 当前用户信息:', {
        userName: userInfo.userName,
        socialUserId: userInfo?.socialUserId || userInfo?.plaintextUserId
      });

      // 2. 获取授权用户列表
      const users = await fetchGameUsers();

      // 3. 检查当前用户是否在授权列表中
      const currentSocialUserId = userInfo?.socialUserId || userInfo?.plaintextUserId;
      
      if (!currentSocialUserId) {
        console.warn('⚠️ CompetitionPermission: 用户socialUserId为空');
        setHasCompetitionPermission(false);
        setIsPermissionLoading(false);
        return;
      }

      // 将socialUserId转换为数字进行比较
      const currentUserId = Number(currentSocialUserId);
      const hasPermission = users.includes(currentUserId);

      console.log('🔐 CompetitionPermission: 权限检查结果:', {
        currentUserId,
        gameUsers: users,
        hasPermission
      });

      setHasCompetitionPermission(hasPermission);
      
    } catch (error) {
      console.error('❌ CompetitionPermission: 权限检查失败:', error);
      setHasCompetitionPermission(false);
    } finally {
      setIsPermissionLoading(false);
    }
  };

  // 监听登录状态变化
  useEffect(() => {
    // 初始检查
    checkPermission();

    // 监听用户信息更新事件
    const handleUserInfoUpdate = () => {
      console.log('🔄 CompetitionPermission: 检测到用户信息更新，重新检查权限');
      checkPermission();
    };

    // 监听登录状态变化事件
    const handleLoginStatusChange = () => {
      console.log('🔄 CompetitionPermission: 检测到登录状态变化，重新检查权限');
      checkPermission();
    };

    // 添加事件监听器
    window.addEventListener('userInfoUpdated', handleUserInfoUpdate);
    window.addEventListener('loginStatusChanged', handleLoginStatusChange);

    // 清理事件监听器
    return () => {
      window.removeEventListener('userInfoUpdated', handleUserInfoUpdate);
      window.removeEventListener('loginStatusChanged', handleLoginStatusChange);
    };
  }, []);

  // 手动刷新权限
  const refreshPermission = () => {
    console.log('🔄 CompetitionPermission: 手动刷新权限');
    checkPermission();
  };

  return {
    hasCompetitionPermission,
    isPermissionLoading,
    gameUsers,
    refreshPermission
  };
};

export default useCompetitionPermission;
