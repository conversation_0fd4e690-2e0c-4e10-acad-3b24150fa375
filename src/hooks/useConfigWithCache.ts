import { useCallback } from 'react';
import { XAiApi } from '../api/src/xai-api';
import { useConfigCache } from './useConfigCache';

/**
 * 配置请求参数
 */
interface ConfigRequestParams {
  configKey: string;
  locale: string;
  [key: string]: any;
}

/**
 * 带缓存的配置获取Hook
 * 
 * 封装了 getConfigByKeyFromCache 接口调用，并提供缓存功能
 * 
 * @param xAiApi XAiApi实例
 * @param appEN 应用英文名称
 * @returns 带缓存的配置获取方法
 */
export const useConfigWithCache = (xAiApi: XAiApi, appEN: string) => {
  const {
    getCache,
    setCache,
    clearCache,
    getCacheStats
  } = useConfigCache({
    enabled: true // 启用缓存
  });

  /**
   * 获取配置数据（带缓存）
   */
  const getConfigByKeyFromCache = useCallback(async <T = any>(
    params: ConfigRequestParams
  ): Promise<T> => {
    const { configKey, locale } = params;

    // 尝试从缓存获取数据
    const cacheResult = getCache<T>(appEN, locale, configKey);
    
    if (cacheResult.hit && cacheResult.data !== null) {
      return cacheResult.data;
    }

    // 缓存未命中，调用API
    console.log('🔄 ConfigWithCache - 缓存未命中，请求API', {
      appEN,
      locale,
      configKey,
      timestamp: new Date().toISOString()
    });

    try {
      const response = await xAiApi.getConfigByKeyFromCache(params);
      
      // 将响应数据写入缓存
      setCache(appEN, locale, configKey, response);
      
      console.log('✅ ConfigWithCache - API请求成功并已缓存', {
        appEN,
        locale,
        configKey,
        hasData: !!response
      });

      return response;
    } catch (error) {
      console.error('❌ ConfigWithCache - API请求失败:', error);
      throw error;
    }
  }, [xAiApi, appEN, getCache, setCache]);

  /**
   * 预加载配置数据
   * 
   * 可以在应用切换时预先加载常用配置，提升用户体验
   */
  const preloadConfigs = useCallback(async (
    locale: string,
    configKeys: string[] = ['suggested_questions', 'case_examples']
  ): Promise<void> => {
    console.log('🚀 ConfigWithCache - 开始预加载配置', {
      appEN,
      locale,
      configKeys
    });

    const preloadPromises = configKeys.map(async (configKey) => {
      try {
        // 检查缓存是否已存在
        const cacheResult = getCache(appEN, locale, configKey);
        if (cacheResult.hit) {
          console.log('📦 ConfigWithCache - 预加载跳过（已缓存）', { configKey });
          return;
        }

        // 预加载数据
        await getConfigByKeyFromCache({
          configKey,
          locale
        });
        
        console.log('✅ ConfigWithCache - 预加载完成', { configKey });
      } catch (error) {
        console.warn('⚠️ ConfigWithCache - 预加载失败', { configKey, error });
      }
    });

    await Promise.allSettled(preloadPromises);
    
    console.log('🎉 ConfigWithCache - 预加载全部完成', {
      appEN,
      locale,
      configKeys
    });
  }, [appEN, getConfigByKeyFromCache, getCache]);

  /**
   * 清除当前应用的缓存
   */
  const clearAppCache = useCallback((locale?: string): number => {
    return clearCache(appEN, locale);
  }, [appEN, clearCache]);

  /**
   * 清除指定配置的缓存
   */
  const clearConfigCache = useCallback((locale: string, configKey: string): number => {
    return clearCache(appEN, locale, configKey);
  }, [appEN, clearCache]);

  /**
   * 获取当前应用的缓存统计
   */
  const getAppCacheStats = useCallback(() => {
    const stats = getCacheStats();
    if (!stats) return null;

    // 计算当前应用相关的缓存统计
    let appCacheKeys = 0;
    let appCacheSize = 0;

    try {
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith(`config_cache_${appEN}_`)) {
          appCacheKeys++;
          const value = sessionStorage.getItem(key);
          if (value) {
            appCacheSize += value.length;
          }
        }
      }
    } catch (error) {
      console.error('📊 ConfigWithCache - 获取应用缓存统计失败:', error);
    }

    return {
      ...stats,
      appCacheKeys,
      appCacheSize,
      appEN
    };
  }, [appEN, getCacheStats]);

  return {
    getConfigByKeyFromCache,
    preloadConfigs,
    clearAppCache,
    clearConfigCache,
    getAppCacheStats
  };
};

export default useConfigWithCache;
