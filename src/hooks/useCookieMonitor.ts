import { useEffect, useCallback } from 'react'
import Cookies from 'js-cookie'
import { ApiCallDeduplicator } from '../utils/apiCallDeduplicator'

interface CookieMonitorOptions {
  cookieName: string
  onCookieChange: (newValue: string | undefined, oldValue: string | undefined) => void
  interval?: number // 检查间隔，默认500ms
  onTokenFetch?: (userInfo: any) => Promise<void> // 获取token的回调函数
}

/**
 * 自定义Hook：监听指定cookie的变化
 * @param options 监听配置选项
 */
export const useCookieMonitor = (options: CookieMonitorOptions) => {
  const { cookieName, onCookieChange, interval = 500 } = options

  useEffect(() => {
    let lastValue = Cookies.get(cookieName)
    
    const checkCookieChanges = () => {
      const currentValue = Cookies.get(cookieName)
      
      if (currentValue !== lastValue) {
        console.log(`检测到${cookieName}变化:`, { 
          previous: lastValue, 
          current: currentValue 
        })
        
        onCookieChange(currentValue, lastValue)
        lastValue = currentValue
      }
    }
    
    // 定期检查cookie变化
    const intervalId = setInterval(checkCookieChanges, interval)
    
    return () => clearInterval(intervalId)
  }, [cookieName, onCookieChange, interval])
}

interface UserInfoMonitorOptions {
  onUserInfoChange: (userInfo: any | null) => void
  onTokenFetch?: (userInfo: any) => Promise<void>
}

/**
 * 专门监听userInfo cookie的Hook，带有自动token获取功能
 * @param options 监听配置选项
 */
export const useUserInfoMonitor = (options: UserInfoMonitorOptions) => {
  const { onUserInfoChange, onTokenFetch } = options

  const handleUserInfoChange = useCallback(async (newValue: string | undefined) => {
    if (newValue) {
      try {
        const userInfoData = JSON.parse(newValue)
        console.log('🔍 检测到userInfo变化，解析到用户信息:', userInfoData)

        // 通知用户信息变化
        onUserInfoChange(userInfoData)

        // 检查是否已经有medxyToken
        const existingToken = Cookies.get('medxyToken')
        if (existingToken) {
          console.log('✅ medxyToken已存在，跳过获取:', existingToken)
          return
        }

        // 自动调用token获取函数
        if (onTokenFetch) {
          console.log('🚀 开始自动获取medxyToken...')
          try {
            await onTokenFetch(userInfoData)
            console.log('✅ medxyToken获取完成')
          } catch (error) {
            console.error('❌ 获取medxyToken失败:', error)
          }
        } else {
          console.log('⚠️ 未提供token获取函数')
        }

      } catch (error) {
        console.error('❌ 解析userInfo失败:', error)
        onUserInfoChange(null)
      }
    } else {
      // userInfo被清除
      console.log('🗑️ userInfo已被清除')
      onUserInfoChange(null)
    }
  }, [onUserInfoChange, onTokenFetch])

  useCookieMonitor({
    cookieName: 'userInfo',
    onCookieChange: (newValue) => handleUserInfoChange(newValue)
  })
}

interface UserInfoWithApiOptions {
  onUserInfoChange: (userInfo: any | null) => void
  xAiApi: any // XAiApi实例
}

/**
 * 带有内置API调用的userInfo监听Hook
 * @param options 监听配置选项，包含API实例
 */
export const useUserInfoWithTokenApi = (options: UserInfoWithApiOptions) => {
  const { onUserInfoChange, xAiApi } = options

  // 内置的token获取函数（使用去重机制）
  const fetchToken = useCallback(async (userInfoData: any) => {
    try {
      console.log('📡 useCookieMonitor: 调用getAiWriteToken接口...')

      // 使用去重机制调用getAiWriteToken
      const response = await ApiCallDeduplicator.getAiWriteTokenDeduped(
        userInfoData.userId,
        () => xAiApi.getAiWriteToken({
          userId: userInfoData.userId,
          userName: userInfoData.userName,
          realName: userInfoData.realName,
          avatar: userInfoData.avatar,
          plaintextUserId: userInfoData.plaintextUserId,
          mobile: userInfoData.mobile,
          email: userInfoData.email
        })
      )

      if ((response as any)?.token) {
        const token = (response as any).token
        console.log('💾 useCookieMonitor: 存储medxyToken:', token)

        // 存储token和相关信息
        Cookies.set("medxyToken", token)
        localStorage.setItem("hasuraToken", (response as any).htoken || '')
        localStorage.setItem("openid", (response as any).openid || '')
        localStorage.setItem("socialUserId", (response as any).socialUserId || '')
        localStorage.setItem("socialType", (response as any).socialType || '')

        console.log('✅ useCookieMonitor: medxyToken存储成功')
      } else {
        console.error('❌ useCookieMonitor: API返回数据中没有token字段')
      }
    } catch (error: any) {
      if (error.message === 'API call blocked by cooldown period') {
        console.log('⏰ useCookieMonitor: getAiWriteToken调用被冷却机制阻止')
      } else {
        console.error('❌ useCookieMonitor: 调用getAiWriteToken接口失败:', error)
        throw error
      }
    }
  }, [xAiApi])

  // 使用基础的userInfo监听Hook
  useUserInfoMonitor({
    onUserInfoChange,
    onTokenFetch: fetchToken
  })
}
