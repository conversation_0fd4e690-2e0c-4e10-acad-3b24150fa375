import { useCallback, useMemo } from 'react';

/**
 * 缓存配置项
 */
interface CacheConfig {
  /** 缓存有效期（毫秒），默认为会话期间有效 */
  expiryMs?: number;
  /** 是否启用缓存，默认为true */
  enabled?: boolean;
}

/**
 * 缓存数据结构
 */
interface CacheData<T = any> {
  /** 缓存的数据 */
  data: T;
  /** 缓存时间戳 */
  timestamp: number;
  /** 缓存版本，用于数据结构变更时的兼容性 */
  version: string;
}

/**
 * 缓存操作结果
 */
interface CacheResult<T = any> {
  /** 是否命中缓存 */
  hit: boolean;
  /** 缓存的数据，如果未命中则为null */
  data: T | null;
  /** 缓存年龄（毫秒） */
  age?: number;
}

/**
 * 配置缓存Hook
 * 
 * 用于缓存 getConfigByKeyFromCache 接口的响应数据
 * 缓存策略：使用sessionStorage，浏览器关闭时自动清空
 * 缓存key格式：config_cache_${appEN}_${language}_${configKey}
 * 
 * @param config 缓存配置
 * @returns 缓存操作方法
 */
export const useConfigCache = (config: CacheConfig = {}) => {
  const {
    expiryMs = Number.MAX_SAFE_INTEGER, // 默认会话期间有效
    enabled = true
  } = config;

  // 当前缓存版本
  const CACHE_VERSION = '1.0.0';

  /**
   * 生成缓存key
   */
  const generateCacheKey = useCallback((appEN: string, language: string, configKey: string): string => {
    return `config_cache_${appEN}_${language}_${configKey}`;
  }, []);

  /**
   * 检查缓存是否有效
   */
  const isCacheValid = useCallback((cacheData: CacheData, expiryMs: number): boolean => {
    const now = Date.now();
    const isVersionValid = cacheData.version === CACHE_VERSION;
    const isTimeValid = (now - cacheData.timestamp) < expiryMs;
    
    return isVersionValid && isTimeValid;
  }, [CACHE_VERSION]);

  /**
   * 从缓存中读取数据
   */
  const getCache = useCallback(<T = any>(
    appEN: string, 
    language: string, 
    configKey: string
  ): CacheResult<T> => {
    if (!enabled) {
      return { hit: false, data: null };
    }

    try {
      const cacheKey = generateCacheKey(appEN, language, configKey);
      const cachedString = sessionStorage.getItem(cacheKey);
      
      if (!cachedString) {
        return { hit: false, data: null };
      }

      const cachedData: CacheData<T> = JSON.parse(cachedString);
      
      if (!isCacheValid(cachedData, expiryMs)) {
        // 缓存已过期，删除并返回未命中
        sessionStorage.removeItem(cacheKey);
        return { hit: false, data: null };
      }

      const age = Date.now() - cachedData.timestamp;
      
      console.log('📦 ConfigCache - 缓存命中', {
        appEN,
        language,
        configKey,
        age: `${(age / 1000).toFixed(1)}秒`,
        cacheKey
      });

      return {
        hit: true,
        data: cachedData.data,
        age
      };
    } catch (error) {
      console.error('📦 ConfigCache - 读取缓存失败:', error);
      return { hit: false, data: null };
    }
  }, [enabled, generateCacheKey, isCacheValid, expiryMs]);

  /**
   * 将数据写入缓存
   */
  const setCache = useCallback(<T = any>(
    appEN: string,
    language: string,
    configKey: string,
    data: T
  ): boolean => {
    if (!enabled) {
      return false;
    }

    try {
      const cacheKey = generateCacheKey(appEN, language, configKey);
      const cacheData: CacheData<T> = {
        data,
        timestamp: Date.now(),
        version: CACHE_VERSION
      };

      sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));
      
      console.log('💾 ConfigCache - 数据已缓存', {
        appEN,
        language,
        configKey,
        cacheKey,
        dataSize: JSON.stringify(data).length
      });

      return true;
    } catch (error) {
      console.error('💾 ConfigCache - 写入缓存失败:', error);
      return false;
    }
  }, [enabled, generateCacheKey, CACHE_VERSION]);

  /**
   * 清除指定的缓存
   */
  const clearCache = useCallback((
    appEN?: string,
    language?: string,
    configKey?: string
  ): number => {
    try {
      let clearedCount = 0;

      if (appEN && language && configKey) {
        // 清除特定缓存
        const cacheKey = generateCacheKey(appEN, language, configKey);
        const existed = sessionStorage.getItem(cacheKey) !== null;
        sessionStorage.removeItem(cacheKey);
        clearedCount = existed ? 1 : 0;

        console.log('🗑️ ConfigCache - 已清除特定缓存', {
          appEN,
          language,
          configKey,
          existed,
          cacheKey
        });
      } else {
        // 清除匹配条件的配置缓存
        const keysToRemove: string[] = [];

        // 收集所有sessionStorage的键
        const allKeys: string[] = [];
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key) {
            allKeys.push(key);
          }
        }

        // 过滤出需要删除的键
        allKeys.forEach(key => {
          if (!key.startsWith('config_cache_')) {
            return; // 不是配置缓存，跳过
          }

          // 解析缓存键：config_cache_${appEN}_${language}_${configKey}
          const parts = key.split('_');
          if (parts.length < 4) {
            return; // 格式不正确，跳过
          }

          const keyAppEN = parts[2];
          const keyLanguage = parts[3];
          // configKey可能包含下划线，所以需要重新组合
          const keyConfigKey = parts.slice(4).join('_');

          // 应用过滤条件
          let shouldRemove = true;

          if (appEN && keyAppEN !== appEN) {
            shouldRemove = false;
          }

          if (language && keyLanguage !== language) {
            shouldRemove = false;
          }

          if (configKey && keyConfigKey !== configKey) {
            shouldRemove = false;
          }

          if (shouldRemove) {
            keysToRemove.push(key);
          }
        });

        // 执行删除
        keysToRemove.forEach(key => {
          sessionStorage.removeItem(key);
          clearedCount++;
        });

        console.log('🗑️ ConfigCache - 已清除缓存', {
          count: clearedCount,
          filter: { appEN, language, configKey },
          removedKeys: keysToRemove
        });
      }

      return clearedCount;
    } catch (error) {
      console.error('🗑️ ConfigCache - 清除缓存失败:', error);
      return 0;
    }
  }, [generateCacheKey]);

  /**
   * 获取缓存统计信息
   */
  const getCacheStats = useCallback(() => {
    try {
      const stats = {
        totalKeys: 0,
        configCacheKeys: 0,
        totalSize: 0,
        oldestCache: null as Date | null,
        newestCache: null as Date | null
      };

      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key) {
          stats.totalKeys++;
          
          if (key.startsWith('config_cache_')) {
            stats.configCacheKeys++;
            
            try {
              const value = sessionStorage.getItem(key);
              if (value) {
                stats.totalSize += value.length;
                
                const cacheData: CacheData = JSON.parse(value);
                const cacheDate = new Date(cacheData.timestamp);
                
                if (!stats.oldestCache || cacheDate < stats.oldestCache) {
                  stats.oldestCache = cacheDate;
                }
                if (!stats.newestCache || cacheDate > stats.newestCache) {
                  stats.newestCache = cacheDate;
                }
              }
            } catch (error) {
              // 忽略解析错误的缓存项
            }
          }
        }
      }

      return stats;
    } catch (error) {
      console.error('📊 ConfigCache - 获取统计信息失败:', error);
      return null;
    }
  }, []);

  // 返回缓存操作方法
  return useMemo(() => ({
    getCache,
    setCache,
    clearCache,
    getCacheStats,
    generateCacheKey
  }), [getCache, setCache, clearCache, getCacheStats, generateCacheKey]);
};

export default useConfigCache;
