import { useState, useCallback } from 'react'
import { message } from 'antd'
import { XAiApi } from '../api/src/xai-api'
import { getFileTypeByName, getFileExtByName } from '../api/src/utils/file-type'
import { useSimpleTranslation } from '../i18n/simple-hooks'

export interface CompetitionFileUploadOptions {
  onFilesSelected?: (files: File[]) => void
  onFileRemoved?: (files: File[]) => void
}

export interface CompetitionFileUploadReturn {
  selectedFiles: File[]
  handleFilesSelected: (files: File[]) => void
  removeFile: (index: number) => void
  clearFiles: () => void
}

export const useCompetitionFileUpload = ({
  onFilesSelected,
  onFileRemoved
}: CompetitionFileUploadOptions): CompetitionFileUploadReturn => {
  const { t } = useSimpleTranslation()
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])

  // 验证文件
  const validateFile = (file: File): string | null => {
    // 文件大小限制 (50MB)
    const maxSize = 50 * 1024 * 1024
    if (file.size > maxSize) {
      return t('fileUpload.fileTooLarge')
    }

    // 支持的文件扩展名
    const allowedExtensions = ['pdf', 'doc', 'docx']
    const fileExtension = getFileExtByName(file.name)?.toLowerCase()

    console.log('文件名:', file.name)
    console.log('文件扩展名:', fileExtension)
    console.log('文件类型分类:', getFileTypeByName(file.name))

    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return t('fileUpload.unsupportedFileType')
    }

    return null
  }

  // 处理文件选择（不上传）
  const handleFilesSelected = useCallback((files: File[]) => {
    console.log('选择比赛文件:', files)

    const validFiles: File[] = []

    // 验证每个文件
    for (const file of files) {
      const validationError = validateFile(file)
      if (validationError) {
        message.error(`${file.name}: ${validationError}`)
        continue
      }
      validFiles.push(file)
    }

    if (validFiles.length > 0) {
      // 添加到已选择的文件列表
      setSelectedFiles(prevFiles => [...prevFiles, ...validFiles])
      onFilesSelected?.(validFiles)
      message.success(`已选择 ${validFiles.length} 个文件`)
    }
  }, [onFilesSelected, t])

  // 移除文件
  const removeFile = useCallback((index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index)
    setSelectedFiles(newFiles)
    onFileRemoved?.(newFiles)
  }, [selectedFiles, onFileRemoved])

  // 清空所有文件
  const clearFiles = useCallback(() => {
    setSelectedFiles([])
    onFileRemoved?.([])
  }, [onFileRemoved])

  return {
    selectedFiles,
    handleFilesSelected,
    removeFile,
    clearFiles
  }
}
