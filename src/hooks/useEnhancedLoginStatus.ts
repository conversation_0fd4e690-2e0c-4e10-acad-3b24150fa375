import { useState, useEffect, useCallback } from 'react'
import Cookies from 'js-cookie'
import { apiManager } from '../utils/apiManager'

interface UserInfo {
  userId: string
  userName: string
  realName: string
  avatar: string
  plaintextUserId: string
  mobile: string
  email: string
}

/**
 * 增强的登录状态管理 Hook
 * 提供统一的登录状态管理、事件监听和API实例同步
 */
export const useEnhancedLoginStatus = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [isTokenReady, setIsTokenReady] = useState(false)

  // 检查用户信息和token状态
  const checkUserInfo = useCallback(() => {
    const userInfoString = Cookies.get('userInfo')
    const medxyToken = Cookies.get('medxyToken')

    console.log('🔍 检查登录状态:', { 
      hasUserInfo: !!userInfoString, 
      hasToken: !!medxyToken,
      tokenLength: medxyToken?.length || 0
    })

    if (userInfoString && medxyToken) {
      try {
        const parsedInfo = JSON.parse(userInfoString)
        
        // 只有当用户信息真正发生变化时才更新状态
        setUserInfo(prevUserInfo => {
          if (!prevUserInfo || JSON.stringify(prevUserInfo) !== JSON.stringify(parsedInfo)) {
            console.log('✅ 用户信息已更新:', parsedInfo.userName)
            return parsedInfo
          }
          return prevUserInfo
        })
        
        setIsLoggedIn(true)
        setIsTokenReady(true)
        
        // 更新API管理器的token
        apiManager.updateToken(medxyToken, parsedInfo.userName)
        
        return parsedInfo
      } catch (error) {
        console.error('❌ 解析用户信息失败:', error)
        setUserInfo(null)
        setIsLoggedIn(false)
        setIsTokenReady(false)
      }
    } else {
      setUserInfo(prevUserInfo => {
        if (prevUserInfo !== null) {
          console.log('🚪 用户已退出登录')
          return null
        }
        return prevUserInfo
      })
      setIsLoggedIn(false)
      setIsTokenReady(false)
      
      // 清除API管理器实例
      apiManager.clearInstances()
    }
    return null
  }, [])

  // 监听登录状态变化事件
  useEffect(() => {
    const handleUserInfoUpdated = (event: CustomEvent) => {
      console.log('📢 收到用户信息更新事件:', event.detail)
      
      // 减少延迟，加快响应速度
      setTimeout(() => {
        checkUserInfo()
      }, 20)
    }

    const handleLoginStatusChanged = (event: CustomEvent) => {
      console.log('📢 收到登录状态变化事件:', event.detail)
      
      if (event.detail.type === 'logout') {
        setUserInfo(null)
        setIsLoggedIn(false)
        setIsTokenReady(false)
        apiManager.clearInstances()
      } else if (event.detail.type === 'login') {
        // 减少延迟，加快响应速度
        setTimeout(() => {
          checkUserInfo()
        }, 20)
      }
    }

    // 添加事件监听器
    window.addEventListener('userInfoUpdated', handleUserInfoUpdated as EventListener)
    window.addEventListener('loginStatusChanged', handleLoginStatusChanged as EventListener)

    // 初始检查
    checkUserInfo()

    // 定期检查（备用方案）
    const interval = setInterval(checkUserInfo, 5000)

    return () => {
      window.removeEventListener('userInfoUpdated', handleUserInfoUpdated as EventListener)
      window.removeEventListener('loginStatusChanged', handleLoginStatusChanged as EventListener)
      clearInterval(interval)
    }
  }, [checkUserInfo])

  // 手动刷新登录状态
  const refreshLoginStatus = useCallback(() => {
    console.log('🔄 手动刷新登录状态')
    checkUserInfo()
  }, [checkUserInfo])

  // 退出登录
  const logout = useCallback(() => {
    console.log('🚪 执行退出登录')
    
    // 清除Cookie和localStorage
    Cookies.remove('medxyToken')
    Cookies.remove('userInfo')
    localStorage.removeItem('hasuraToken')
    localStorage.removeItem('openid')
    localStorage.removeItem('socialUserId')
    localStorage.removeItem('socialType')
    
    // 更新状态
    setUserInfo(null)
    setIsLoggedIn(false)
    setIsTokenReady(false)
    
    // 清除API管理器实例
    apiManager.clearInstances()
    
    // 触发登录状态变化事件
    window.dispatchEvent(new CustomEvent('loginStatusChanged', {
      detail: { type: 'logout' }
    }))
  }, [])

  return {
    userInfo,
    isLoggedIn,
    isTokenReady, // 新增：表示token是否已准备好
    refreshLoginStatus,
    logout,
    checkUserInfo
  }
}

export default useEnhancedLoginStatus
