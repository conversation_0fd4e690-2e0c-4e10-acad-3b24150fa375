import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, message } from 'antd';
import { useSimpleTranslation } from '../../i18n/simple-hooks';
import CompetitionFileSelector from './CompetitionFileSelector';
import { useCompetitionFileUpload } from '../../hooks/useCompetitionFileUpload';
import { XAiApi } from '../../api/src/xai-api';

interface CompetitionSubmissionModalProps {
  open: boolean;
  onClose: () => void;
  onSubmitSuccess: () => void;
  xAiApi: XAiApi;
}

const CompetitionSubmissionModal: React.FC<CompetitionSubmissionModalProps> = ({
  open,
  onClose,
  onSubmitSuccess,
  xAiApi
}) => {
  const { t } = useSimpleTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 使用比赛文件选择Hook
  const {
    selectedFiles,
    handleFilesSelected,
    removeFile,
    clearFiles
  } = useCompetitionFileUpload({
    onFilesSelected: (files) => {
      console.log('已选择比赛文件:', files);
    },
    onFileRemoved: (files) => {
      console.log('剩余文件:', files);
    }
  });

  // 处理提交
  const handleSubmit = async () => {
    if (selectedFiles.length === 0) {
      message.warning(t('competition.pleaseUploadFiles'));
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('开始提交比赛作品，文件数量:', selectedFiles.length);

      // 逐个上传文件到比赛接口
      const uploadPromises = selectedFiles.map(async (file) => {
        console.log(`上传文件: ${file.name}`);
        return await xAiApi.uploadToCompetition(file);
      });

      const results = await Promise.all(uploadPromises);
      console.log('所有文件上传完成:', results);

      message.success(t('competition.submitSuccess'));
      onSubmitSuccess();
      handleClose();
    } catch (error) {
      console.error('提交失败:', error);
      message.error(t('competition.submitFailed'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理关闭
  const handleClose = () => {
    clearFiles();
    onClose();
  };



  return (
    <Modal
      title={t('competition.submitWork')}
      open={open}
      onCancel={handleClose}
      width={600}
      centered
      footer={[
        <Button key="cancel" onClick={handleClose} disabled={isSubmitting}>
          {t('common.cancel')}
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          loading={isSubmitting}
          disabled={selectedFiles.length === 0}
        >
          {t('competition.confirmSubmit')}
        </Button>
      ]}
    >
      <div className="space-y-4">
        {/* 说明文字 */}
        <div className="text-gray-600 text-sm">
          {t('competition.submitDescription')}
        </div>

        {/* 文件选择区域 */}
        <CompetitionFileSelector
          onFilesSelected={handleFilesSelected}
          allowedFileTypes={['pdf', 'doc', 'docx']}
          disabled={isSubmitting}
        />

        {/* 已选择的文件 */}
        {selectedFiles.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-700">
              {t('competition.uploadedFiles')} ({selectedFiles.length})
            </div>

            {/* 已选择的文件列表 */}
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700 truncate max-w-xs">{file.name}</span>
                  <span className="text-xs text-gray-500">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700 p-1"
                  disabled={isSubmitting}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </Modal>
  );
};

export default CompetitionSubmissionModal;
