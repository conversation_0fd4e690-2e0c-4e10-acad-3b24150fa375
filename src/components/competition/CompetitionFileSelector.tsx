import React, { useRef } from 'react';
import { useSimpleTranslation } from '../../i18n/simple-hooks';

interface CompetitionFileSelectorProps {
  onFilesSelected: (files: File[]) => void;
  disabled?: boolean;
  allowedFileTypes?: string[];
}

const CompetitionFileSelector: React.FC<CompetitionFileSelectorProps> = ({
  onFilesSelected,
  disabled = false,
  allowedFileTypes = ['pdf', 'doc', 'docx']
}) => {
  const { t } = useSimpleTranslation();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      onFilesSelected(fileArray);
    }
    // 清空input值，允许重复选择同一文件
    if (event.target) {
      event.target.value = '';
    }
  };

  // 处理拖拽上传
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (disabled) return;

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      onFilesSelected(fileArray);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  // 触发文件选择
  const triggerFileSelect = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 生成accept属性
  const acceptTypes = allowedFileTypes.map(type => `.${type}`).join(',');

  return (
    <div>
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptTypes}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {/* 文件选择区域 */}
      <div 
        className={`
          border-2 border-dashed border-gray-300 rounded-lg p-8 text-center 
          transition-all duration-200 cursor-pointer
          ${disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:border-blue-400 hover:bg-blue-50'
          }
        `}
        onClick={triggerFileSelect}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
      >
        <div className="flex flex-col items-center space-y-3">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <div>
            <div className="text-lg font-medium text-gray-900 mb-1">
              {t('competition.uploadFiles')}
            </div>
            <div className="text-sm text-gray-500">
              {t('competition.uploadHint')}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              {t('competition.supportedFormats')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompetitionFileSelector;
