import React from 'react';
import { Modal } from 'antd';
import { useSimpleTranslation } from '../../i18n/simple-hooks';

interface CompetitionCongratulationModalProps {
  open: boolean;
  onClose: () => void;
}

const CompetitionCongratulationModal: React.FC<CompetitionCongratulationModalProps> = ({
  open,
  onClose
}) => {
  const { t } = useSimpleTranslation();

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      width={500}
      centered
      closable={false}
      maskClosable={true}
      className="competition-congratulation-modal"
    >
      <div className="text-center py-8 px-6">
        {/* 庆祝图标 */}
        <div className="mb-6">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br bg-blue-500 to-purple-600 rounded-full shadow-lg mb-4">
            <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          {/* 装饰性元素 */}
          <div className="relative">
            <div className="absolute -top-2 -left-4 text-blue-400 text-2xl animate-bounce">🎉</div>
            <div className="absolute -top-1 -right-4 text-purple-400 text-xl animate-bounce delay-100">✨</div>
            <div className="absolute -bottom-1 left-2 text-blue-500 text-lg animate-bounce delay-200">🎊</div>
            <div className="absolute -bottom-2 right-1 text-purple-500 text-xl animate-bounce delay-300">⭐</div>
          </div>
        </div>

        {/* 标题 */}
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          🎊 {t('competition.congratulationTitle')}
        </h2>

        {/* 主要消息 */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-[8px] p-6 mb-6">
          <p className="text-md text-gray-700 leading-relaxed mb-4">
            {t('competition.congratulationMessage')}
          </p>

          {/* 联系信息 */}
          <div className="bg-white rounded-[8px] p-4 border border-gray-200">
            <div className="flex items-center justify-center space-x-2 text-blue-600">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
              </svg>
              <span className="font-medium">{t('competition.contactAssistant')}</span>
            </div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-[4px] bg-gradient-to-r bg-blue-500 to-purple-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out"
        >
          {t('competition.gotIt')}
        </button>
      </div>
    </Modal>
  );
};

export default CompetitionCongratulationModal;
