import React, { useState, useEffect, useMemo } from 'react'
import HistoryQuestionsList from './ConversationList'
import { DifyApi } from '../api/src/dify-api';
import { IGetAiAppInfoResponse } from '../api/src/xai-api';
import Cookies from 'js-cookie';
import { useSimpleTranslation } from '../i18n/simple-hooks';
import { message } from 'antd';

interface HistoryQuestionsPageProps {
  onClose?: () => void
  difyApi: DifyApi
  currentApp: IGetAiAppInfoResponse
}

const ConversationsPage = ({ onClose, difyApi, currentApp }: HistoryQuestionsPageProps) => {
  const { t } = useSimpleTranslation();
  const [appSpecificDifyApi, setAppSpecificDifyApi] = useState<DifyApi | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);

  // 获取packageKey，优先使用appNameEn，其次使用appUuid
  const packageKey = useMemo(() => {
    return currentApp?.appNameEn || currentApp?.appUuid || 'novax-base';
  }, [currentApp]);

  // 初始化应用特定的DifyApi实例
  useEffect(() => {
    const initializeAppSpecificApi = async (retryCount = 0) => {
      let loadingMessage: any = null;
      try {
        setIsInitializing(true);
        setInitError(null);

        // 显示toast加载提示
        loadingMessage = message.loading(t('toast.initializingConversationPage'), 0);

        const token = Cookies.get('medxyToken');
        if (!token) {
          throw new Error(t('chat.noAuthToken'));
        }

        // 使用currentApp的dAppUuid创建专用的DifyApi实例
        if (currentApp?.dAppUuid) {
          const appDifyApi = new DifyApi({
            user: difyApi.options.user,
            apiBase: difyApi.options.apiBase,
            medxyToken: token,
            appId: currentApp.dAppUuid
          });

          setAppSpecificDifyApi(appDifyApi);
          console.log('历史对话页面：成功初始化应用特定的DifyApi，appId:', currentApp.dAppUuid);
        } else {
          throw new Error('应用信息中缺少dAppUuid');
        }

      } catch (error: any) {
        console.error('初始化应用特定的DifyApi失败:', error);

        // 如果是API冷却错误且重试次数少于3次，则等待后重试
        if (error.message &&
            error.message.includes('API call blocked by cooldown period') &&
            retryCount < 3) {
          console.log(`🔄 历史对话页面：API冷却中，${1000 * (retryCount + 1)}ms后重试 (${retryCount + 1}/3)`);

          if (loadingMessage) {
            loadingMessage();
            loadingMessage = null;
          }

          setTimeout(() => {
            initializeAppSpecificApi(retryCount + 1);
          }, 1000 * (retryCount + 1)); // 递增延迟：1s, 2s, 3s
          return;
        }

        setInitError(error.message || '初始化失败');
      } finally {
        setIsInitializing(false);
        // 隐藏toast加载提示
        if (loadingMessage) {
          loadingMessage();
        }
      }
    };

    if (currentApp) {
      initializeAppSpecificApi();
    }

    // 监听重试事件
    const handleRetry = () => {
      if (currentApp) {
        initializeAppSpecificApi();
      }
    };

    window.addEventListener('retryConversationPageInit', handleRetry);
    return () => {
      window.removeEventListener('retryConversationPageInit', handleRetry);
    };
  }, [currentApp, difyApi.options, t]);

  return (
    <div className="w-full h-full flex flex-col bg-white rounded-2xl overflow-hidden">
      {/* 页面头部 */}
      <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 flex items-center justify-center">
            <img src={currentApp.appIcon} alt={currentApp.appName} className="w-8 h-8" />
          </div>
          <div>
            <h1 className="text-xl md:text-2xl font-bold text-gray-800">{currentApp.appName} - {t('history.title')}</h1>
            <p className="text-sm text-gray-600 mt-1">{t('history.description')}</p>
          </div>
        </div>

        {/* 关闭按钮 */}
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-lg transition-colors"
          >
            ✕
          </button>
        )}
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto p-4 md:p-6">
        {initError ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <p className="text-red-600 mb-2">{t('history.loadFailed')}</p>
              <p className="text-gray-500 text-sm mb-4">{initError}</p>
              <button
                onClick={() => {
                  setInitError(null);
                  setIsInitializing(true);
                  // 触发重新初始化
                  const event = new CustomEvent('retryConversationPageInit');
                  window.dispatchEvent(event);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                重试
              </button>
            </div>
          </div>
        ) : appSpecificDifyApi ? (
          <HistoryQuestionsList
            difyApi={appSpecificDifyApi}
            appNameEn={currentApp.appNameEn}
            onClose={onClose}
          />
        ) : null}
      </div>
    </div>
  )
}

export default ConversationsPage