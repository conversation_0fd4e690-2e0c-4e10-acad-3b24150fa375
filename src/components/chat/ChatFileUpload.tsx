import React, { useState, useCallback, useRef, forwardRef, useImperativeHandle } from 'react'
import { message,Modal } from 'antd'
import { formatSize } from '../../api/src/utils/file-type'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import '../file-upload/FileUpload.css'

// 文件上传项类型定义
export interface ChatUploadFileItem {
  uid: string
  name: string
  status: 'uploading' | 'done' | 'error'
  size: number
  type: 'document' | 'image' | 'audio' | 'video'
  originFileObj?: { uid: string }
  percent: number
  transfer_method: 'local_file'
  upload_file_id?: string
  error?: string
  url?: string
}

interface ChatFileUploadProps {
  files: ChatUploadFileItem[]
  onFilesChange: (files: ChatUploadFileItem[]) => void
  onUpload: (files: File[]) => Promise<void>
  allowedFileTypes?: string[]
  disabled?: boolean
  isUploading?: boolean
  maxFiles?: number
  maxFileSize?: number // 最大文件大小（MB）
  onValidationError?: (error: string) => void
  enablePreview?: boolean // 是否启用文件预览
}

// 导出一个触发文件选择的函数类型
export interface ChatFileUploadRef {
  triggerFileSelect: () => void
}

const ChatFileUpload = forwardRef<ChatFileUploadRef, ChatFileUploadProps>(({
  files,
  onFilesChange,
  onUpload,
  allowedFileTypes = [],
  disabled = false,
  isUploading = false,
  maxFiles = 10,
  maxFileSize = 50, // 默认50MB
  onValidationError,
  enablePreview = true
}, ref) => {
  const { t } = useSimpleTranslation()
  const [isDragOver, setIsDragOver] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)
  const [previewFile, setPreviewFile] = useState<ChatUploadFileItem | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    triggerFileSelect: () => {
      if (!disabled && fileInputRef.current) {
        fileInputRef.current.click()
      }
    }
  }), [disabled])

  const validateFiles = useCallback((newFiles: File[]): { validFiles: File[], errors: string[] } => {
    const validFiles: File[] = []
    const errors: string[] = []

    // 检查当前已上传文件数量 + 新选择文件数量是否超过限制
    const currentFileCount = files.length // 当前已上传的文件数量（从props传入）
    const newFileCount = newFiles.length // 新选择的文件数量
    const availableSlots = maxFiles - currentFileCount



    if (availableSlots <= 0) {
      errors.push(t('chat.maxFilesReached').replace('{count}', maxFiles.toString()))
      return { validFiles: [], errors }
    }

    if (newFileCount > availableSlots) {
      errors.push(t('chat.maxFilesExceeded')
        .replace('{available}', availableSlots.toString())
        .replace('{max}', maxFiles.toString()))
      return { validFiles: [], errors }
    }

    // 限制批量选择文件数量（使用动态配置值）
    if (newFileCount > maxFiles) {
      errors.push(t('chat.batchUploadLimit').replace('{count}', maxFiles.toString()))
      return { validFiles: [], errors }
    }

    for (const file of newFiles) {

      // 检查文件大小
      const fileSizeMB = file.size / (1024 * 1024)
      if (fileSizeMB > maxFileSize) {
        errors.push(t('chat.fileSizeExceeded')
          .replace('{fileName}', file.name)
          .replace('{maxSize}', maxFileSize.toString()))
        continue
      }

      // 检查文件类型
      if (allowedFileTypes.length > 0) {
        const fileExtension = file.name.split('.').pop()?.toLowerCase()
        if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
          errors.push(t('chat.unsupportedFileType')
            .replace('{fileName}', file.name)
            .replace('{formats}', allowedFileTypes.join(', ')))
          continue
        }
      }

      // 检查文件名
      if (!file.name || file.name.trim() === '') {
        errors.push(t('fileUpload.uploadFailed'))
        continue
      }

      validFiles.push(file)
    }

    return { validFiles, errors }
  }, [allowedFileTypes, maxFiles, maxFileSize])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (disabled) return

    setDragCounter(prev => prev + 1)
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true)
    }
  }, [disabled])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (disabled) return

    setDragCounter(prev => {
      const newCounter = prev - 1
      if (newCounter === 0) {
        setIsDragOver(false)
      }
      return newCounter
    })
  }, [disabled])

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (disabled) return

    setIsDragOver(false)
    setDragCounter(0)

    const droppedFiles = Array.from(e.dataTransfer.files)
    if (droppedFiles.length > 0) {
      const { validFiles, errors } = validateFiles(droppedFiles)

      if (errors.length > 0) {
        errors.forEach(error => {
          message.error(error)
          onValidationError?.(error)
        })
      }

      if (validFiles.length > 0) {
        await onUpload(validFiles)
      }
    }
  }, [onUpload, validateFiles, disabled, onValidationError])

  const handleFileSelect = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return

    const selectedFiles = Array.from(e.target.files || [])
    if (selectedFiles.length > 0) {
      const { validFiles, errors } = validateFiles(selectedFiles)

      if (errors.length > 0) {
        errors.forEach(error => {
          message.error(error)
          onValidationError?.(error)
        })
      }

      if (validFiles.length > 0) {
        await onUpload(validFiles)
      }
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = ''
  }, [onUpload, validateFiles, disabled, onValidationError])

  const removeFile = useCallback((uid: string, fileName: string) => {
    Modal.confirm({
      title: t('fileUpload.removeFile'),
      content: t('fileUpload.confirmRemove'),
      onOk: () => {
        const updatedFiles = files.filter(file => file.uid !== uid)
        onFilesChange(updatedFiles)
      },
      onCancel: () => {},
    });
  }, [files, onFilesChange])

  const acceptString = allowedFileTypes.length > 0
    ? allowedFileTypes.map(type => `.${type}`).join(',')
    : '.pdf,.doc,.docx,.txt,.md,.xlsx,.xls,.pptx,.ppt,.csv,.html,.xml,.epub,.json,.jpg,.jpeg,.png,.gif,.webp,.svg,.mp3,.m4a,.wav,.webm,.amr,.mp4,.mov,.mpeg,.mpga'

  // 预览文件
  const handlePreview = useCallback((file: ChatUploadFileItem) => {
    if (enablePreview && file.type === 'image') {
      setPreviewFile(file)
    }
  }, [enablePreview])

  // 关闭预览
  const closePreview = useCallback(() => {
    setPreviewFile(null)
  }, [])

  return (
    <div className="space-y-3">
      {/* 文件列表显示 */}
      {files.length > 0 && (
        <div className="space-y-2">
          {files.map((file) => (
            <div
              key={file.uid}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
            >
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div
                  className={`flex-shrink-0 ${enablePreview && file.type === 'image' ? 'cursor-pointer' : ''}`}
                  onClick={() => enablePreview && file.type === 'image' && handlePreview(file)}
                >
                  {file.type === 'image' ? '🖼️' :
                   file.type === 'document' ? '📄' :
                   file.type === 'audio' ? '🎵' :
                   file.type === 'video' ? '🎬' : '📎'}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                    {enablePreview && file.type === 'image' && (
                      <span className="text-xs text-blue-500 ml-2">点击预览</span>
                    )}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatSize(file.size)}
                    {file.status === 'uploading' && ` • 上传中 ${file.percent}%`}
                    {file.status === 'error' && file.error && ` • ${file.error}`}
                    {file.status === 'done' && ` • 上传完成`}
                  </p>
                </div>
              </div>
              
              {/* 状态指示器和进度条 */}
              <div className="flex items-center space-x-2">
                {file.status === 'uploading' && (
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${file.percent}%` }}
                    ></div>
                  </div>
                )}

                {file.status === 'done' && (
                  <div className="text-green-500">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}

                {file.status === 'error' && (
                  <div className="text-red-500">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                )}

                {/* 删除按钮 */}
                <button
                  onClick={() => removeFile(file.uid, file.name)}
                  className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors"
                  disabled={file.status === 'uploading'}
                  title={file.status === 'uploading' ? '上传中，无法删除' : '删除文件'}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 拖拽上传区域 */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-4 transition-all duration-300 ${
          isDragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <div className="text-center">
          <div className="text-2xl mb-2 file-icon-bounce">📎</div>
          <p className="text-sm text-gray-600">
            {isDragOver ? t('fileUpload.dropToUpload') : t('fileUpload.dragAndDrop')}
          </p>
          {allowedFileTypes.length > 0 && (
            <p className="text-xs text-gray-400 mt-1">
              {t('fileUpload.supportedFormats')}: {allowedFileTypes.join(', ')}
            </p>
          )}
        </div>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          multiple={maxFiles - files.length > 1} // 只有剩余槽位大于1时才允许多选
          accept={acceptString}
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled || files.length >= maxFiles}
        />
      </div>

      {/* 文件预览模态框 */}
      {previewFile && previewFile.type === 'image' && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={closePreview}
        >
          <div className="relative max-w-4xl max-h-4xl p-4">
            <button
              onClick={closePreview}
              className="absolute top-2 right-2 text-white hover:text-gray-300 z-10"
            >
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div className="bg-white rounded-lg p-4">
              <h3 className="text-lg font-medium mb-2">{previewFile.name}</h3>
              <div className="text-sm text-gray-500 mb-4">
                {formatSize(previewFile.size)} • {previewFile.status === 'done' ? '已上传' : '上传中'}
              </div>
              {/* 这里可以添加实际的图片预览，需要获取文件的URL */}
              <div className="flex items-center justify-center h-64 bg-gray-100 rounded">
                <p className="text-gray-500">图片预览功能需要文件URL支持</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
})

ChatFileUpload.displayName = 'ChatFileUpload'

export default ChatFileUpload
