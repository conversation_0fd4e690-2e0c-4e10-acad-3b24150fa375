import { useState, useEffect, useCallback, useRef } from 'react';
import { message, Modal, Input, Button, Dropdown, Menu, Spin } from 'antd';
import { 
  UploadOutlined, 
  FolderAddOutlined, 
  MoreOutlined,
  FolderOutlined,
  FileOutlined,
  DownloadOutlined,
  EditOutlined,
  DeleteOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON>eftOutlined,
  AppstoreOutlined,
  BarsOutlined
} from '@ant-design/icons';
import FileUpload, { FileUploadRef } from '../file-upload/FileUpload';
import { useSimpleTranslation, useI18nRouter } from '../../i18n/simple-hooks';
import { useLoginStatus } from '../../hooks/useLoginStatus';
import FilePreviewModal from './FilePreviewModal';
import { KnowledgebaseItem, ItemType } from '../../utils/knowledgebaseStorage';
import { KnowledgebaseApiService } from '../../utils/knowledgebaseApiService';
import { XAiApi } from '../../api/src/xai-api';

interface KnowledgebaseContentProps {
  user: string;
  xAiApi: XAiApi;
}

const KnowledgebaseContent: React.FC<KnowledgebaseContentProps> = ({ user, xAiApi }) => {
  const { t } = useSimpleTranslation();
  const { currentLanguage } = useI18nRouter();
  const { isLoggedIn, checkUserInfo } = useLoginStatus();
  const [loginStatusChecked, setLoginStatusChecked] = useState(false);
  const [items, setItems] = useState<KnowledgebaseItem[]>([]);
  const [currentPath, setCurrentPath] = useState<string[]>([]);
  const [currentDirId, setCurrentDirId] = useState<number>(0); // 维护当前目录的 ID
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [previewItem, setPreviewItem] = useState<KnowledgebaseItem | null>(null);
  const [renamingItem, setRenamingItem] = useState<string | null>(null);
  const [newItemName, setNewItemName] = useState('');
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid'); // 视图模式状态
  
  const fileUploadRef = useRef<FileUploadRef>(null);
  const apiService = KnowledgebaseApiService.getInstance(xAiApi);

  // 跳转到登录页
  const navigateToLogin = useCallback(() => {
    window.sessionStorage.setItem('redirectUrl', window.location.href);
    if (!currentLanguage || currentLanguage === 'zh') {
      // 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
      if (window.location.origin.includes('medxy.ai')) {
        (window as any).top.location.href = location.origin + '/' + currentLanguage + "/login"
      } else {
        (window as any).addLoginDom?.()
      }
    } else {
      (window as any).top.location.href = location.origin + '/' + currentLanguage + "/login"
    }
  }, [currentLanguage]);

  // 加载当前路径下的项目
  const loadItems = useCallback(async () => {
    setLoading(true);
    try {
      const currentItems = await apiService.getItemsByParentId(user, currentDirId);
      setItems(currentItems);
    } catch (error) {
      console.error('加载知识库列表失败:', error);
      message.error(t('knowledgebase.loadFailed') || '加载失败');
    } finally {
      setLoading(false);
    }
  }, [user, currentDirId, apiService]);

  // 初始化时检查登录状态
  useEffect(() => {
    const initializeLoginStatus = async () => {
      // 主动检查一次登录状态
      checkUserInfo();
      setLoginStatusChecked(true);
    };
    initializeLoginStatus();
  }, [checkUserInfo]);

  // 登录状态确定后，根据状态决定是否跳转或加载内容
  useEffect(() => {
    if (!loginStatusChecked) return; // 等待登录状态检查完成
    
    // 检测是否为预渲染环境
    const isPrerendering = typeof window !== 'undefined' && (
      // 检测 Puppeteer 或无头浏览器
      window.navigator.userAgent.includes('HeadlessChrome') ||
      window.navigator.userAgent.includes('Puppeteer') ||
      // 检测是否存在预渲染标记
      document.querySelector('[data-prerendered]') !== null ||
      // 检测是否为服务端渲染环境
      typeof window.navigator === 'undefined' ||
      // 检测特定的预渲染环境变量
      (window as any).__PRERENDER__ === true
    );
    
    console.log('KnowledgebaseContent: 环境检测', {
      userAgent: window.navigator.userAgent,
      isPrerendering,
      hasPrerenderedElement: document.querySelector('[data-prerendered]') !== null,
      isLoggedIn,
      loginStatusChecked
    });
    
    if (!isLoggedIn && !isPrerendering) {
      navigateToLogin();
      return;
    }
    
    // 在预渲染环境中，跳过实际的数据加载，只渲染静态结构
    if (!isPrerendering) {
      loadItems();
    }
  }, [loginStatusChecked, isLoggedIn, navigateToLogin, loadItems]);

  // 处理文件上传
  const handleFilesUploaded = useCallback(async (files: File[]) => {
    // 检查登录状态
    if (!isLoggedIn) {
      navigateToLogin();
      return;
    }
    
    setIsUploading(true);
    try {
      for (const file of files) {
        await apiService.addFile(user, currentDirId, file);
      }
      await loadItems();
      message.success(t('knowledgebase.uploadSuccess').replace('{count}', files.length.toString()));
    } catch (error) {
      console.error('文件上传失败:', error);
      message.error((error as Error).message || t('knowledgebase.uploadFailed'));
    } finally {
      setIsUploading(false);
    }
  }, [user, currentDirId, loadItems, apiService, isLoggedIn, navigateToLogin]);

  // 创建新文件夹
  const handleCreateFolder = useCallback(async () => {
    if (!newFolderName.trim()) {
      message.error(t('knowledgebase.enterFolderName'));
      return;
    }

    try {
      await apiService.createFolder(user, currentDirId, newFolderName.trim());
      await loadItems();
      setIsCreatingFolder(false);
      setNewFolderName('');
      message.success(t('knowledgebase.folderCreateSuccess'));
    } catch (error) {
      console.error('创建文件夹失败:', error);
      message.error((error as Error).message || t('knowledgebase.folderCreateFailed'));

    }
  }, [user, currentDirId, newFolderName, loadItems, apiService]);

  // 处理创建文件夹按钮点击
  const handleCreateFolderClick = useCallback(() => {
    // 检查登录状态
    if (!isLoggedIn) {
      navigateToLogin();
      return;
    }
    setIsCreatingFolder(true);
  }, [isLoggedIn, navigateToLogin]);

  // 处理上传文件按钮点击
  const handleUploadClick = useCallback(() => {
    // 检查登录状态
    if (!isLoggedIn) {
      navigateToLogin();
      return;
    }
    fileUploadRef.current?.triggerFileSelect();
  }, [isLoggedIn, navigateToLogin]);

  // 删除项目
  const handleDeleteItem = useCallback((itemId: string) => {
    Modal.confirm({
      title: t('knowledgebase.confirmDelete'),
      content: t('knowledgebase.confirmDeleteMessage'),
      okText: t('knowledgebase.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: async () => {
        try {
          await apiService.deleteItem(user, itemId);
          await loadItems();
          message.success(t('knowledgebase.deleteSuccess'));
        } catch (error) {
          console.error('删除失败:', error);
          message.error(t('knowledgebase.deleteFailed'));
        }
      }
    });
  }, [user, loadItems, apiService]);

  // 重命名项目
  const handleRenameItem = useCallback(async (itemId: string) => {
    if (!newItemName.trim()) {
      message.error(t('knowledgebase.enterNewName'));
      return;
    }

    // 找到当前重命名的项目
    const currentItem = items.find(item => item.id === itemId);
    let finalName = newItemName.trim();
    
    // 如果是文件且用户输入的名称没有后缀，自动补全原文件的后缀
    if (currentItem && currentItem.type === 'file') {
      const originalExtension = currentItem.name.split('.').pop();
      const inputHasExtension = finalName.includes('.');
      
      if (!inputHasExtension && originalExtension) {
        finalName = `${finalName}.${originalExtension}`;
      }
    }

    try {
      await apiService.renameItem(user, itemId, finalName);
      await loadItems();
      setRenamingItem(null);
      setNewItemName('');
      message.success(t('knowledgebase.renameSuccess'));
    } catch (error) {
      console.error('重命名失败:', error);
      message.error(t('knowledgebase.renameFailed'));
    }
  }, [user, newItemName, loadItems, apiService]);

  // 下载文件
  const handleDownloadFile = useCallback(async (item: KnowledgebaseItem) => {
    try {
      await apiService.downloadFile(user, item);
      message.success(t('knowledgebase.downloadSuccess').replace('{name}', item.name));
    } catch (error) {
      console.error('下载失败:', error);
      message.error(t('knowledgebase.downloadFailed'));
    }
  }, [user, apiService]);

  // 打开文件夹
  const handleOpenFolder = useCallback((folderName: string) => {
    // 找到对应的文件夹项目
    const folderItem = items.find(item => item.type === 'folder' && item.name === folderName);
    if (folderItem) {
      setCurrentPath([...currentPath, folderName]);
      setCurrentDirId(parseInt(folderItem.id)); // 更新当前目录ID
    }
  }, [currentPath, items]);

  // 返回上级目录
  const handleGoBack = useCallback(async () => {
    if (currentPath.length > 0) {
      const newPath = currentPath.slice(0, -1);
      setCurrentPath(newPath);
      
      // 根据新路径计算目录ID
      try {
        const newDirId = newPath.length === 0 ? 0 : await apiService.getCurrentDirIdByPath(newPath);
        setCurrentDirId(newDirId);
      } catch (error) {
        console.error('获取上级目录ID失败:', error);
        setCurrentDirId(0); // 出错时回到根目录
      }
    }
  }, [currentPath, apiService]);

  // 处理面包屑点击跳转
  const handleBreadcrumbClick = useCallback(async (index: number) => {
    if (index === 0) {
      // 点击根目录
      setCurrentPath([]);
      setCurrentDirId(0);
    } else {
      // 点击其他目录层级
      const targetPath = currentPath.slice(0, index);
      setCurrentPath(targetPath);
      
      try {
        const targetDirId = await apiService.getCurrentDirIdByPath(targetPath);
        setCurrentDirId(targetDirId);
      } catch (error) {
        console.error('获取目标目录ID失败:', error);
        setCurrentDirId(0);
        setCurrentPath([]);
      }
    }
  }, [currentPath, apiService]);

  // 生成面包屑导航
  const breadcrumbs = [t('knowledgebase.rootFolder'), ...currentPath];

  // 获取文件图标
  const getFileIcon = (item: KnowledgebaseItem) => {
    if (item.type === 'folder') {
      return <FolderOutlined className="text-blue-600 text-3xl" />;
    }
    
    const ext = item.name.split('.').pop()?.toLowerCase();
    const iconClass = "text-gray-600 text-3xl";
    
    // 获取图标颜色类名
    const getIconColorClass = () => {
      switch (ext) {
        case 'pdf':
          return "text-red-600 text-3xl";
        case 'docx':
          return "text-blue-600 text-3xl";
        case 'xls':
        case 'xlsx':
          return "text-green-600 text-3xl";
        case 'ppt':
        case 'pptx':
          return "text-orange-600 text-3xl";
        case 'csv':
          return "text-purple-600 text-3xl";
        case 'json':
          return "text-yellow-600 text-3xl";
        case 'txt':
        case 'md':
          return "text-gray-600 text-3xl";
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'svg':
          return "text-pink-600 text-3xl";
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
        case 'mkv':
        case 'flv':
          return "text-indigo-600 text-3xl";
        case 'mp3':
        case 'wav':
        case 'flac':
        case 'aac':
          return "text-green-600 text-3xl";
        case 'zip':
        case 'rar':
        case '7z':
        case 'tar':
        case 'gz':
          return "text-orange-600 text-3xl";
        case 'sql':
        case 'db':
        case 'sqlite':
          return "text-blue-600 text-3xl";
        case 'html':
        case 'htm':
        case 'css':
        case 'js':
        case 'ts':
          return "text-yellow-700 text-2xl";
        default:
          return iconClass;
      }
    };
    
    // 返回带有扩展名文本的图标
    return (
      <div className="relative inline-block">
        <FileOutlined className={getIconColorClass()} />
        {ext && (
           <div className="absolute inset-0 flex items-center justify-center">
             <span className="text-[7px] font-bold text-gray-600">
               {ext.toUpperCase()}
             </span>
           </div>
         )}
      </div>
    );
  };

  // 获取文件分类
  const getFileCategory = (item: KnowledgebaseItem) => {
    if (item.type === 'folder') {
      return t('knowledgebase.folder');
    }
    
    const ext = item.name.split('.').pop()?.toLowerCase();
    const structuredTypes = ['csv', 'xlsx', 'xls', 'json', 'xml', 'sql', 'db', 'tsv', 'parquet', 'avro'];
    
    if (structuredTypes.includes(ext || '')) {
      return t('knowledgebase.structuredData');
    } else {
      return t('knowledgebase.documentData');
    }
  };

  // 格式化日期
  const formatDate = (timestamp: number) => {
    const now = new Date();
    const date = new Date(timestamp);
    // 获取今天的开始时间（00:00:00）
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    // 获取文件日期的开始时间（00:00:00）
    const fileDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    // 计算日期差（基于日期而不是时间差）
    const diffMs = today.getTime() - fileDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return `${t('knowledgebase.today')} ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 1) {
      return `${t('knowledgebase.yesterday')} ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays < 7) {
      return `${t('knowledgebase.daysAgo').replace('{days}', diffDays.toString())} ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) + 
             ` ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    }
  };

  // 获取文件名（不包含扩展名）
  const getFileNameWithoutExtension = (fileName: string) => {
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 检查文件是否为图片格式
  const isImageFile = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
    return imageExtensions.includes(ext || '');
  };

  // 获取项目操作菜单项
  const getItemMenuItems = (item: KnowledgebaseItem) => {
    const items = [];
    
    if (item.type === 'file') {
      // 只有图片文件才显示预览按钮
      if (isImageFile(item.name)) {
        items.push({
          key: 'preview',
          icon: <EyeOutlined />,
          label: t('knowledgebase.preview'),
          onClick: (e: any) => {
            e.domEvent.stopPropagation();
            setPreviewItem(item);
          }
        });
      }
      
      items.push({
        key: 'download',
        icon: <DownloadOutlined />,
        label: t('knowledgebase.download'),
        onClick: (e: any) => {
          e.domEvent.stopPropagation();
          handleDownloadFile(item);
        }
      });
    }
    
    items.push(
      {
        key: 'rename',
        icon: <EditOutlined />,
        label: t('knowledgebase.rename'),
        onClick: (e: any) => {
          e.domEvent.stopPropagation();
          setRenamingItem(item.id);
          setNewItemName(item.name);
        }
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: t('knowledgebase.delete'),
        danger: true,
        onClick: (e: any) => {
          e.domEvent.stopPropagation();
          handleDeleteItem(item.id);
        }
      }
    );
    
    return items;
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      {/* 工具栏 */}
      <div className="flex flex-col gap-4 mb-6">
        {/* 面包屑导航 - 灵活换行 */}
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex flex-wrap items-center gap-2">
            {currentPath.length > 0 && (
              <Button 
                type="text" 
                icon={<ArrowLeftOutlined />} 
                onClick={handleGoBack}
                className="p-1 flex-shrink-0"
              >
                {t('knowledgebase.backToParent')}
              </Button>
            )}
            <div className="flex flex-wrap items-center gap-1 min-w-0">
              {breadcrumbs.map((crumb, index) => (
                <span key={index} className="flex items-center flex-shrink-0">
                  {index > 0 && <span className="mx-2 text-gray-400">/</span>}
                  <span 
                    className={`${index === breadcrumbs.length - 1 
                      ? 'font-medium text-gray-800 bg-blue-50 border border-blue-200' 
                      : 'text-gray-600 bg-gray-50 hover:bg-gray-100 cursor-pointer'} 
                                     px-3 py-1 rounded-lg transition-colors duration-200 text-xs`}
                    onClick={() => index < breadcrumbs.length - 1 && handleBreadcrumbClick(index)}
                  >
                    {crumb}
                  </span>
                </span>
              ))}
            </div>
          </div>

          {/* 视图切换按钮 - 移到右侧 */}
          <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
            <Button
              type={viewMode === 'grid' ? 'primary' : 'text'}
              size="small"
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
              className="!border-none"
            />
            <Button
              type={viewMode === 'list' ? 'primary' : 'text'}
              size="small"
              icon={<BarsOutlined />}
              onClick={() => setViewMode('list')}
              className="!border-none"
            />
          </div>
        </div>
        
        {/* 操作按钮行 */}
        <div className="flex flex-wrap gap-2 justify-end">
          {breadcrumbs.indexOf("参赛作品") == -1?(
            <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={handleUploadClick}
            loading={isUploading}
          >
            {t('knowledgebase.uploadFile')}
          </Button>
          ):null}
          {breadcrumbs.indexOf("参赛作品") == -1?<Button
            icon={<FolderAddOutlined />}
            onClick={handleCreateFolderClick}
          >
            {t('knowledgebase.createFolder')}
          </Button>:null}
        </div>
      </div>

      {/* 文件上传区域 */}
      <FileUpload
        ref={fileUploadRef}
        onFilesUploaded={handleFilesUploaded}
        allowedFileTypes={['txt', 'md', 'mdx', 'markdown', 'pdf', 'html', 'xlsx', 'xls', 'docx', 'csv', 'eml', 'msg', 'pptx', 'ppt', 'xml', 'epub', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']}
        disabled={isUploading}
      >
        {/* 文件/文件夹展示区域 - 固定高度和滚动条 */}
        <div className="h-[450px] overflow-y-auto">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12 h-full">
              <Spin size="large" />
              <p className="text-gray-500 mt-4">{t('common.loading')}</p>
            </div>
          ) : items.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500 h-full">
              <FolderOutlined className="text-6xl mb-4 text-gray-300" />
              <p className="text-lg mb-2">{t('knowledgebase.noFiles')}</p>
              <p className="text-sm">{t('knowledgebase.noFilesDescription')}</p>
            </div>
          ) : viewMode === 'grid' ? (
            /* 网格视图 */
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 p-4">
            {items.map((item) => (
              <div
                key={item.id}
                className="bg-white border border-gray-200 rounded-xl p-6 cursor-pointer group relative
                          hover:shadow-xl hover:shadow-blue-100 hover:border-blue-300 hover:-translate-y-2 
                          hover:scale-105 transition-all duration-300 ease-out transform
                          active:scale-95 active:translate-y-0 min-h-[150px]"
                onClick={() => {
                  if (item.type === 'folder') {
                    handleOpenFolder(item.name);
                  } else if (isImageFile(item.name)) {
                    setPreviewItem(item);
                  }
                }}
              >
                {/* 项目内容 - 左对齐布局 */}
                <div className="flex flex-col items-start h-full">
                  {renamingItem === item.id ? (
                    <div className="w-full" onClick={(e) => e.stopPropagation()}>
                      <Input
                        value={newItemName}
                        onChange={(e) => setNewItemName(e.target.value)}
                        onPressEnter={(e) => {
                          e.stopPropagation();
                          handleRenameItem(item.id);
                        }}
                        onBlur={() => handleRenameItem(item.id)}
                        size="small"
                        autoFocus
                      />
                    </div>
                  ) : (
                    <>
                      {/* 日期 */}
                      <div className="text-xs text-gray-500 mb-4">
                        {formatDate(item.uploadTime)}
                      </div>

                      {/* 图标 */}
                      <div className="mb-2 group-hover:scale-110 transition-transform duration-200">
                        {getFileIcon(item)}
                      </div>

                      {/* 文件名（不含扩展名） */}
                      <div className="text-sm font-medium text-gray-800 mb-4 truncate w-full leading-tight" 
                           title={item.type === 'folder' ? item.name : getFileNameWithoutExtension(item.name)}>
                        {item.type === 'folder' ? item.name : getFileNameWithoutExtension(item.name)}
                      </div>

                      {/* 文件类型、分类、大小 - 单行显示 */}
                      <div className="text-xs text-gray-500 truncate w-full">
                        {item.type === 'folder' ? (
                          <span>{getFileCategory(item)}</span>
                        ) : (
                          <span>
                            {getFileCategory(item)} • {item.name.split('.').pop()?.toUpperCase()} • {formatFileSize(item.size)}
                          </span>
                        )}
                      </div>
                    </>
                  )}
                </div>

                {/* 操作菜单 */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Dropdown menu={{ items: getItemMenuItems(item) }} trigger={['click']} placement="bottomRight">
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<MoreOutlined />} 
                      onClick={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡，防止触发文件项的点击事件
                      }}
                    />
                  </Dropdown>
                </div>
              </div>
            ))}
          </div>
        ) : (
              /* 列表视图 */
              <div className="space-y-2 p-4">
            {items.map((item) => (
              <div
                key={item.id}
                className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer group relative
                          hover:shadow-md hover:shadow-blue-100 hover:border-blue-300 
                          transition-all duration-200 ease-out"
                onClick={() => {
                  if (item.type === 'folder') {
                    handleOpenFolder(item.name);
                  } else if (isImageFile(item.name)) {
                    setPreviewItem(item);
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1 min-w-0">
                    {/* 图标 */}
                    <div className="flex-shrink-0">
                      {getFileIcon(item)}
                    </div>
                    
                    {/* 文件信息 */}
                    <div className="flex-1 min-w-0">
                      {renamingItem === item.id ? (
                        <div className="w-full max-w-md" onClick={(e) => e.stopPropagation()}>
                          <Input
                            value={newItemName}
                            onChange={(e) => setNewItemName(e.target.value)}
                            onPressEnter={(e) => {
                              e.stopPropagation();
                              handleRenameItem(item.id);
                            }}
                            onBlur={() => handleRenameItem(item.id)}
                            size="small"
                            autoFocus
                          />
                        </div>
                      ) : (
                        <>
                          {/* 文件名 */}
                          <div className="text-sm font-medium text-gray-800 truncate" 
                               title={item.type === 'folder' ? item.name : getFileNameWithoutExtension(item.name)}>
                            {item.type === 'folder' ? item.name : getFileNameWithoutExtension(item.name)}
                          </div>
                          
                          {/* 文件详情 */}
                          <div className="text-xs text-gray-500 mt-1">
                            {item.type === 'folder' ? (
                              <span>{getFileCategory(item)} • {formatDate(item.uploadTime)}</span>
                            ) : (
                              <span>
                                {getFileCategory(item)} • {item.name.split('.').pop()?.toUpperCase()} • {formatFileSize(item.size)} • {formatDate(item.uploadTime)}
                              </span>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                  
                  {/* 操作菜单 */}
                  <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Dropdown menu={{ items: getItemMenuItems(item) }} trigger={['click']} placement="bottomRight">
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<MoreOutlined />} 
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止事件冒泡，防止触发文件项的点击事件
                        }}
                      />
                    </Dropdown>
                  </div>
                </div>
              </div>
            ))}
          </div>
            )}
        </div>
      </FileUpload>

      {/* 创建文件夹模态框 */}
      <Modal
        title={t('knowledgebase.createFolderTitle')}
        open={isCreatingFolder}
        onOk={handleCreateFolder}
        onCancel={() => {
          setIsCreatingFolder(false);
          setNewFolderName('');
        }}
        okText={t('knowledgebase.create')}
        cancelText={t('common.cancel')}
      >
        <Input
          placeholder={t('knowledgebase.folderNamePlaceholder')}
          value={newFolderName}
          onChange={(e) => setNewFolderName(e.target.value)}
          onPressEnter={handleCreateFolder}
          autoFocus
        />
      </Modal>

      {/* 文件预览模态框 */}
      {previewItem && (
        <FilePreviewModal
          item={previewItem}
          open={!!previewItem}
          onClose={() => setPreviewItem(null)}
          onDownload={() => handleDownloadFile(previewItem)}
        />
      )}
    </div>
  );
};

export default KnowledgebaseContent;