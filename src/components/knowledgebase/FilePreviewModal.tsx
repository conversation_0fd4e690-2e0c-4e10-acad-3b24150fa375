import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import { DownloadOutlined, FileOutlined } from '@ant-design/icons';
import { KnowledgebaseItem } from '../../utils/knowledgebaseStorage';
import { useSimpleTranslation } from '../../i18n/simple-hooks';

interface FilePreviewModalProps {
  item: KnowledgebaseItem;
  open: boolean;
  onClose: () => void;
  onDownload: () => void;
}

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({
  item,
  open,
  onClose,
  onDownload
}) => {
  const { t } = useSimpleTranslation();
  // 获取文件扩展名
  const getFileExtension = (filename: string) => {
    return filename.split('.').pop()?.toLowerCase() || '';
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化上传时间
  const formatUploadTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  const fileExtension = getFileExtension(item.name);

  // 判断是否为图片文件
  const isImageFile = (filename: string) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    const extension = getFileExtension(filename);
    return imageExtensions.includes(extension);
  };

  // 渲染预览内容
  const renderPreviewContent = () => {
    if (isImageFile(item.name) && item.data) {
      // 如果是图片文件，显示图片预览
      return (
        <div className="text-center py-4">
          <img 
             src={item.data} 
             alt={item.name}
             className="max-w-full max-h-80 mx-auto rounded-lg shadow-lg object-contain"
            onError={(e) => {
              // 图片加载失败时显示错误信息
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const errorDiv = target.nextElementSibling as HTMLElement;
              if (errorDiv) {
                errorDiv.style.display = 'block';
              }
            }}
          />
          <div className="hidden text-red-500 mt-4">
            <FileOutlined className="text-4xl mb-2" />
            <p>图片预览失败</p>
          </div>
          <div className="mt-4 space-y-2 text-sm text-gray-600">
            <p>{t('knowledgebase.fileSize').replace('{size}', formatFileSize(item.size))}</p>
            <p>{t('knowledgebase.fileType').replace('{type}', fileExtension.toUpperCase())}</p>
            <p>{t('knowledgebase.uploadTime').replace('{time}', formatUploadTime(item.uploadTime))}</p>
          </div>
        </div>
      );
    } else {
      // 非图片文件显示错误信息
      return (
        <div className="text-center py-8">
          <FileOutlined className="text-6xl text-gray-400 mb-4" />
          <div className="space-y-2 text-sm text-gray-600">
            <p>{t('knowledgebase.fileSize').replace('{size}', formatFileSize(item.size))}</p>
            <p>{t('knowledgebase.fileType').replace('{type}', fileExtension.toUpperCase())}</p>
            <p>{t('knowledgebase.uploadTime').replace('{time}', formatUploadTime(item.uploadTime))}</p>
          </div>
          <div className="mt-6 p-4 bg-red-50 rounded-lg border border-red-200">
            <p className="text-red-600 text-sm font-medium">
              {t('knowledgebase.previewNotSupported')}
            </p>
          </div>
        </div>
      );
    }
  };

  return (
    <Modal
      title={t('knowledgebase.previewTitle').replace('{name}', item.name)}
      open={open}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="download" type="primary" icon={<DownloadOutlined />} onClick={onDownload}>
          {t('knowledgebase.downloadFile')}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t('knowledgebase.closePreview')}
        </Button>
      ]}
    >
      <div className="overflow-hidden">
        {renderPreviewContent()}
      </div>
    </Modal>
  );
};

export default FilePreviewModal;