/* 移动端双面板布局样式 */
.mobile-dual-panel-layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
  background: #f8f9fa;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  /* 确保只在移动端显示 */
  display: block;
}

@media (min-width: 768px) {
  .mobile-dual-panel-layout {
    display: none;
  }
}

/* 移动端面板通用样式 */
.mobile-left-panel,
.mobile-right-panel {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-left-panel:hover,
.mobile-right-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* 面板头部样式 */
.mobile-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 24px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 60px; /* 触摸友好的最小高度 */
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  /* 触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.mobile-panel-header:hover {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
}

.mobile-panel-header:active {
  background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
  transform: scale(0.98);
}

.mobile-panel-header-content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12px;
}

.mobile-panel-icon {
  width: 20px;
  height: 20px;
  color: #4b5563;
  flex-shrink: 0;
}

.mobile-thinking-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.mobile-panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.mobile-panel-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin-left: auto;
}

.mobile-panel-chevron {
  width: 20px;
  height: 20px;
  color: #6b7280;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  transform-origin: center;
}

.mobile-panel-chevron.expanded {
  transform: rotate(180deg) scale(1.05);
  color: #4b5563;
}

/* 面板内容样式 */
.mobile-panel-content {
  overflow: hidden;
  transition: 
    max-height 0.35s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-panel-content.collapsed {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.mobile-panel-content.expanded {
  max-height: 2000px; /* 足够大的值以容纳所有内容 */
  opacity: 1;
  padding: 16px 20px 20px 20px;
}

/* 左侧面板步骤容器 */
.mobile-steps-container {
  position: relative;
}

.mobile-step-item-wrapper {
  position: relative;
  margin-bottom: 16px;
}

.mobile-step-item-wrapper:last-child {
  margin-bottom: 0;
}

/* 步骤连接线 */
.mobile-step-connector {
  position: absolute;
  left: 12px;
  top: 32px;
  bottom: -16px;
  width: 2px;
  background: linear-gradient(to bottom, #e5e7eb 0%, #f3f4f6 100%);
  border-radius: 1px;
}

/* 步骤项样式 */
.mobile-step-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  min-height: 48px; /* 触摸友好的最小高度 */
}

.mobile-step-item.clickable {
  cursor: pointer;
  /* 触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.mobile-step-item.clickable:hover {
  background: rgba(59, 130, 246, 0.04);
  transform: translateX(4px);
}

.mobile-step-item.clickable:active {
  background: rgba(59, 130, 246, 0.08);
  transform: translateX(2px) scale(0.98);
}

.mobile-step-item.active {
  background: rgba(59, 130, 246, 0.08);
  border-left: 3px solid #3b82f6;
  padding-left: 9px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* 步骤圆点 */
.mobile-step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.mobile-step-dot.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
}

.mobile-step-dot.running {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
  animation: mobile-pulse 2s infinite;
}

.mobile-step-check {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.mobile-step-pulse {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

/* 步骤内容 */
.mobile-step-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.mobile-step-text {
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s ease;
  word-break: break-word;
}

.mobile-step-text.completed {
  color: #374151;
  font-weight: 500;
}

.mobile-step-text.running {
  color: #3b82f6;
  font-weight: 600;
}

/* 右侧面板包装器 */
.mobile-right-panel-wrapper {
  position: relative;
}

.mobile-right-panel-content {
  /* 这里会包含PC端右侧面板的内容 */
  font-size: 14px;
  line-height: 1.6;
}

/* 脉冲动画 */
@keyframes mobile-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  }
}

/* 移动端双面板布局中的表格横向滚动优化 */
.mobile-right-panel-content .ai-response-container .table-container {
  overflow-x: auto;
  overflow-y: visible;
  margin: 0.5rem 0;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  /* 平滑滚动 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.mobile-right-panel-content .ai-response-container table {
  min-width: 100%;
  border-collapse: collapse;
  font-size: 0.8rem;
  background-color: white;
}

.mobile-right-panel-content .ai-response-container th,
.mobile-right-panel-content .ai-response-container td {
  border: 1px solid #e2e8f0;
  padding: 0.375rem 0.5rem;
  text-align: left;
  white-space: nowrap;
  min-width: 80px;
}

.mobile-right-panel-content .ai-response-container th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.mobile-right-panel-content .ai-response-container tbody tr:hover {
  background-color: #f8fafc;
}

/* 移动端双面板表格滚动指示器 */
.mobile-right-panel-content .ai-response-container .table-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 12px;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.9), transparent);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-right-panel-content .ai-response-container .table-container:hover::after {
  opacity: 1;
}

/* 移动端双面板表格滚动条样式 */
.mobile-right-panel-content .ai-response-container .table-container::-webkit-scrollbar {
  height: 4px;
}

.mobile-right-panel-content .ai-response-container .table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.mobile-right-panel-content .ai-response-container .table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.mobile-right-panel-content .ai-response-container .table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 移动端响应式设计 */
@media (max-width: 480px) {
  .mobile-dual-panel-layout {
    padding: 12px;
    gap: 12px;
  }

  .mobile-panel-header {
    padding: 14px 16px;
    min-height: 52px;
  }

  .mobile-panel-content.expanded {
    padding: 12px 16px 16px 16px;
  }

  .mobile-panel-title {
    font-size: 15px;
  }

  .mobile-panel-subtitle {
    font-size: 13px;
  }

  .mobile-step-text {
    font-size: 13px;
  }
}

/* 移动端 p 标签字体大小优化 */
@media (max-width: 768px) {
  .mobile-right-panel-content .ai-response-container p {
    font-size: 14px;
  }
}
