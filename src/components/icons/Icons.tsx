import React from 'react';

// 导入本地SVG文件
import vipSvg from './vip.svg';
import ultraSvg from './ultra.svg';

// 图片地址常量 - 集中管理所有外部图片资源
export const IMAGE_URLS = {
  // 用户头像相关
  DEFAULT_AVATAR: 'https://img.medsci.cn/web/img/user_icon.png',

  // 订阅相关图标
  SUBSCRIBE_ICON: 'https://img.medsci.cn/202507/81b770ae34d745d29a9b53f0775107db-pWv2rVrLBAg8.png',
  PAYMENT_HELPER: 'https://img.medsci.cn/202507/619e387a38c1496aa4059d9b6c0a1fcb-O9cEIt2Bv1hh.png',

  // 客服二维码
  CUSTOMER_SERVICE_QR: 'https://img.medsci.cn/202507/0c1f96d22e404a1e842a383e45da085d-R1tjth61bxlr.png',
  ZFB_ICON: 'https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png',

  // 场景相关图标
  GENERAL_SCENE_ICON: 'https://img.medsci.cn/202507/5a4f482dc99f49938f229862f6192c3a-RX57Duz1XbGU.png',
  APP_LOGO_ICON: 'https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png',
} as const;

// 通用图标属性接口
interface IconProps {
  width?: number | string;
  height?: number | string;
  className?: string;
  stroke?: string;
  strokeWidth?: number | string;
  fill?: string;
}

// 默认图标属性
const defaultProps: IconProps = {
  width: 16,
  height: 16,
  fill: 'none',
  stroke: 'currentColor',
  strokeWidth: 2,
};

// 下拉箭头图标
export const ChevronDownIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="6 9 12 15 18 9"></polyline>
    </svg>
  );
};

// 检查标记图标
export const CheckIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="20 6 9 17 4 12"></polyline>
    </svg>
  );
};

// 搜索图标
export const SearchIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <circle cx="11" cy="11" r="8"></circle>
      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
    </svg>
  );
};

// 展开图标
export const ExpandIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="15 3 21 3 21 9"></polyline>
      <polyline points="9 21 3 21 3 15"></polyline>
      <line x1="21" y1="3" x2="14" y2="10"></line>
      <line x1="3" y1="21" x2="10" y2="14"></line>
    </svg>
  );
};

// 来源图标
export const SourcesIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M4 7V4h3"></path>
      <path d="M20 7V4h-3"></path>
      <path d="M4 17v3h3"></path>
      <path d="M20 17v3h-3"></path>
      <path d="M12 4v16"></path>
      <path d="M8 12H4"></path>
      <path d="M14 12h-4"></path>
      <path d="M20 12h-4"></path>
    </svg>
  );
};

// 加号图标 - 用于新建会话
export const PlusIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
  );
};

// 返回箭头图标 - 用于返回上一页
export const ArrowLeftIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="m12 19-7-7 7-7"></path>
      <path d="M19 12H5"></path>
    </svg>
  );
};

// 首页图标 - 用于返回首页
export const HomeIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
      <polyline points="9,22 9,12 15,12 15,22"></polyline>
    </svg>
  );
};

// 现代化退出图标 - 箭头指向门的样式
export const LogoutIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
      <polyline points="16,17 21,12 16,7"></polyline>
      <line x1="21" y1="12" x2="9" y2="12"></line>
    </svg>
  );
};

// 电源按钮样式的退出图标
export const PowerOffIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path>
      <line x1="12" y1="2" x2="12" y2="12"></line>
    </svg>
  );
};

// 历史记录图标 - 用于查看历史对话
export const HistoryIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      aria-label="历史记录"
    >
      <circle cx="12" cy="12" r="10"></circle>
      <polyline points="12,6 12,12 16,14"></polyline>
    </svg>
  );
};

// 皇冠图标组件 - 白银等级
export const SilverCrownIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, ...rest } = { ...defaultProps, ...props };
  return (
    <svg
      width={width}
      height={height}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      {...rest}
    >
      <defs>
        <linearGradient id="silverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#E8E8E8" />
          <stop offset="50%" stopColor="#C0C0C0" />
          <stop offset="100%" stopColor="#A8A8A8" />
        </linearGradient>
      </defs>
      <path
        d="M5 16L3 10L5.5 12L8 8L10.5 12L13 8L15.5 12L18 8L20.5 12L23 10L21 16H5Z"
        fill="url(#silverGradient)"
        stroke="#A8A8A8"
        strokeWidth="1"
      />
      <rect x="5" y="16" width="16" height="3" fill="url(#silverGradient)" stroke="#A8A8A8" strokeWidth="1" />
      <circle cx="12" cy="12" r="1" fill="#F0F0F0" />
    </svg>
  );
};

// 皇冠图标组件 - 黄金等级
export const GoldCrownIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, ...rest } = { ...defaultProps, ...props };
  return (
    <svg
      width={width}
      height={height}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      {...rest}
    >
      <path
        d="M5 16L3 10L5.5 12L8 8L10.5 12L13 8L15.5 12L18 8L20.5 12L23 10L21 16H5Z"
        fill="#FFD700"
        stroke="#DAA520"
        strokeWidth="1"
      />
      <rect x="5" y="16" width="16" height="3" fill="#FFD700" stroke="#DAA520" strokeWidth="1" />
      <circle cx="12" cy="12" r="1.5" fill="#FFA500" />
    </svg>
  );
};

// 钻石皇冠图标组件 - Ultra等级 (引用本地SVG文件)
export const DiamondCrownIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, ...rest } = { ...defaultProps, ...props };
  return (
    <img
      src={ultraSvg}
      width={width}
      height={height}
      className={className}
      alt="Ultra Crown"
      style={{
        display: 'block',
        maxWidth: '100%',
        height: 'auto'
      }}
      {...rest}
    />
  );
};

// VIP图标组件 - Pro等级 (引用本地SVG文件)
export const VIPIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, ...rest } = { ...defaultProps, ...props };
  return (
    <img
      src={vipSvg}
      width={width}
      height={height}
      className={className}
      alt="VIP"
      style={{
        display: 'block',
        maxWidth: '100%',
        height: 'auto'
      }}
      {...rest}
    />
  );
};

// 信息图标 - 用于FAQ等信息展示
export const InfoIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <circle cx="12" cy="12" r="10"></circle>
      <path d="m9,9 0,0 a3,3 0 0,1 6,0c0,2 -3,3 -3,3"></path>
      <path d="m9,17 6,0"></path>
    </svg>
  );
};

// 用户/团队图标 - 用于关于我们
export const UsersIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
      <circle cx="9" cy="7" r="4"></circle>
      <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
    </svg>
  );
};

// 复制图标 - 用于复制消息内容
export const CopyIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
    </svg>
  );
};

// 点赞图标
export const LikeIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
        <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2V10a2 2 0 0 1 2-2h3"></path>
    </svg>
  );
};

// 点踩图标
export const DisLikeIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
        <path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zM17 2h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3"></path>
    </svg>
  );
};

// 导出所有图标的对象，方便批量导入
export const Icons = {
  ChevronDown: ChevronDownIcon,
  Check: CheckIcon,
  Search: SearchIcon,
  Expand: ExpandIcon,
  Sources: SourcesIcon,
  Plus: PlusIcon,
  Home: HomeIcon,
  Logout: LogoutIcon,
  PowerOff: PowerOffIcon,
  History: HistoryIcon,
  SilverCrown: SilverCrownIcon,
  GoldCrown: GoldCrownIcon,
  DiamondCrown: DiamondCrownIcon,
  VIP: VIPIcon,
  Info: InfoIcon,
  Users: UsersIcon,
  Copy: CopyIcon,
  LikeIcon,
  DisLikeIcon,
};

export default Icons;