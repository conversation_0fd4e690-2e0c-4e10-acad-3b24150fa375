import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import { DifyApi, IConversationItem } from '../api/src/dify-api';
import { useI18nRouter, useSimpleTranslation } from '../i18n/simple-hooks'
import { ConversationRefreshManager } from '../utils/conversationRefreshManager'
import { ApiCallDeduplicator } from '../utils/apiCallDeduplicator'

interface ApiProps {
  difyApi: DifyApi;
  appNameEn: string;
  onClose?: () => void;
}

// 历史提问记录组件
const ConversationList: React.FC<ApiProps> = ({ difyApi, appNameEn, onClose }) => {
  const navigate = useNavigate();
  const [conversationList, setConversationList] = useState<IConversationItem[]>([]);
  const [lastId, setLastId] = useState<string>('');
  const [hasMore, setHasMore] = useState<boolean>(true);
  const { currentLanguage, changeLanguage } = useI18nRouter()
  const { t } = useSimpleTranslation()

  // 重命名相关状态
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState<string>('');
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  // 处理重命名点击
  const handleRenameClick = (e: React.MouseEvent, item: IConversationItem) => {
    e.stopPropagation();
    setEditingId(item.id);
    setEditingName(item.name);
  };

  // 处理保存重命名
  const handleSaveRename = async (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation();
    if (!editingName.trim()) return;

    try {
      // 调用API重命名对话
      await difyApi.renameConversation({
        conversation_id: conversationId,
        name: editingName.trim(),
        appId: difyApi.options.appId || ''
      });

      // 更新本地状态
      setConversationList(prev =>
        prev.map(item =>
          item.id === conversationId
            ? { ...item, name: editingName.trim() }
            : item
        )
      );

      // 显示成功提示
      // message.success(t('history.renameSuccess'));

      // 退出编辑状态
      setEditingId(null);
      setEditingName('');

      // 清除所有对话列表相关的API缓存并触发其他组件刷新
      console.log('🔄 ConversationList: 重命名成功，清除所有对话列表缓存并触发全局刷新');
      ApiCallDeduplicator.clearAllConversationCache();
      ConversationRefreshManager.triggerRefreshDelayed(100);
    } catch (error) {
      console.error('重命名对话失败:', error);
      message.error(t('history.renameError'));
    }
  };

  // 处理取消编辑
  const handleCancelEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingId(null);
    setEditingName('');
  };

  // 获取更多历史提问
  const fetchMoreConversations = async () => {
    if (!hasMore) return;

    // 如果是默认的知识库应用，跳过对话历史加载
    if (difyApi.options.appId === 'knowledgebase-default') {
      console.log('⏭️ ConversationList: 跳过默认知识库应用的更多对话加载');
      return;
    }

    try {
      console.log('获取更多历史对话，使用appId:', difyApi.options.appId);
      const response = await difyApi.getConversationList({last_id: lastId});
      setConversationList((prevList) => [...prevList, ...response.data]);
      if (response.data.length > 0) {
        setLastId(response.data[response.data.length - 1].id);
      }
      setHasMore(response.has_more);
    } catch (error) {
      console.error('获取更多历史对话失败:', error);
    }
  };

  // 加载历史对话的函数（使用API去重机制）
  const loadInitialData = useCallback(async (forceRefresh: boolean = false, retryCount: number = 0) => {
    try {
      console.log('🔄 ConversationList: 加载历史对话，使用appId:', difyApi.options.appId);

      // 如果是默认的知识库应用，跳过对话历史加载
      if (difyApi.options.appId === 'knowledgebase-default') {
        console.log('⏭️ ConversationList: 跳过默认知识库应用的对话历史加载');
        setConversationList([]);
        setHasMore(false);
        return;
      }

      // 使用API去重工具防止重复调用
      const response = await ApiCallDeduplicator.getConversationListDeduped(
        difyApi.options.appId || 'default',
        () => difyApi.getConversationList(),
        forceRefresh
      );

      setConversationList(response.data);
      setHasMore(response.has_more);
      if (response.data.length > 0) {
        setLastId(response.data[response.data.length - 1].id);
      }
      console.log('✅ ConversationList: 成功加载历史对话列表，数量:', response.data.length);
    } catch (error) {
      if ((error as Error)?.message === 'API call blocked by cooldown period') {
        console.log('⏰ ConversationList: API调用被冷却机制阻止');

        // 如果重试次数少于3次，则等待后重试
        if (retryCount < 3) {
          const delay = 1000 * (retryCount + 1); // 递增延迟：1s, 2s, 3s
          console.log(`🔄 ConversationList: ${delay}ms后重试 (${retryCount + 1}/3)`);
          setTimeout(() => {
            loadInitialData(forceRefresh, retryCount + 1);
          }, delay);
          return;
        }
      } else {
        console.error('❌ ConversationList: 加载历史对话失败:', error);
      }
    }
  }, [difyApi]);

  // 初次加载历史提问
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);



  // 注册到刷新管理器（添加防重复刷新机制）
  useEffect(() => {
    const componentId = `ConversationList-${difyApi.options.appId}`;
    let isRefreshing = false;

    // 注册刷新回调
    ConversationRefreshManager.registerRefreshCallback(componentId, () => {
      // 防止重复刷新
      if (isRefreshing) {
        console.log('⏰ ConversationList: 刷新正在进行中，跳过重复请求');
        return;
      }

      console.log('🔄 ConversationList: 收到刷新请求，重新加载历史记录');
      isRefreshing = true;

      // 重置状态并重新加载
      setConversationList([]);
      setLastId('');
      setHasMore(true);

      // 执行强制刷新
      loadInitialData(true).finally(() => {
        // 刷新完成后重置标志
        setTimeout(() => {
          isRefreshing = false;
        }, 500); // 500ms冷却时间
      });
    });

    return () => {
      // 组件卸载时取消注册
      ConversationRefreshManager.unregisterRefreshCallback(componentId);
    };
  }, [difyApi.options.appId, loadInitialData]);

  // 处理历史提问点击事件 - 直接跳转到目标详情页，避免中间路由闪烁
  const handleQuestionClick = (conversationId: string) => {
    // 先关闭弹窗
    onClose?.();
    // 使用replace避免在浏览器历史中留下中间状态
    navigate(`/${currentLanguage}/${appNameEn}/${conversationId}`, { replace: true });
  };

  // 获取分类颜色
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      '心血管医学': 'bg-red-100 text-red-700 border-red-200',
      '分子生物学': 'bg-blue-100 text-blue-700 border-blue-200',
      '肿瘤免疫学': 'bg-purple-100 text-purple-700 border-purple-200',
      '神经医学': 'bg-green-100 text-green-700 border-green-200',
      '病毒学': 'bg-yellow-100 text-yellow-700 border-yellow-200',
      '再生医学': 'bg-indigo-100 text-indigo-700 border-indigo-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-700 border-gray-200';
  };

  // 格式化时间显示 timestamp格式 1750747538 - 支持国际化
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp*1000);
    const now = new Date();

    // 检查是否为有效日期
    if (isNaN(date.getTime())) {
      return t('time.unknown');
    }

    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // 防止未来时间导致负数
    if (diffDays < 0) {
      return t('time.justNow');
    }

    if (diffDays === 0) return t('time.today');
    if (diffDays === 1) return t('time.yesterday');
    if (diffDays < 7) return t('time.daysAgo').replace('{days}', diffDays.toString());

    // 根据语言环境格式化日期
    if (currentLanguage === 'zh') {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } else {
      // 英文环境使用本地化日期格式
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  return (
    <div className="space-y-3">
      {conversationList.map((item) => (
        <div
          key={item.id}
          onClick={() => editingId !== item.id && handleQuestionClick(item.id)}
          onMouseEnter={() => setHoveredId(item.id)}
          onMouseLeave={() => setHoveredId(null)}
          className={`
            group p-4 bg-white rounded-xl border border-gray-200
            transition-all duration-200 ease-in-out relative
            ${editingId === item.id
              ? 'cursor-default shadow-md'
              : 'cursor-pointer hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1'
            }
          `}
        >

          {editingId === item.id ? (
            // 编辑状态 - 紧凑布局
            <div className="space-y-2">
              <div className="relative">
                <input
                  type="text"
                  value={editingName}
                  onChange={(e) => setEditingName(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                  className="
                    w-full text-sm font-medium text-gray-900
                    px-3 py-2 border-none outline-none bg-transparent
                    placeholder-gray-500
                  "
                  placeholder={t('history.renamePlaceholder')}
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSaveRename(e as any, item.id);
                    } else if (e.key === 'Escape') {
                      handleCancelEdit(e as any);
                    }
                  }}
                />
              </div>

              <div className="flex items-center justify-end gap-2">
                <button
                  onClick={handleCancelEdit}
                  className="
                    flex items-center gap-1 px-3 py-1.5
                    text-xs font-medium
                    text-gray-600 bg-gray-50 border border-gray-200
                    hover:bg-gray-100 hover:text-gray-700 hover:border-gray-300
                    transition-all duration-200 ease-out
                    active:scale-95 touch-manipulation
                  "
                  style={{ borderRadius: '4px' }}
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  {t('history.cancel')}
                </button>
                <button
                  onClick={(e) => handleSaveRename(e, item.id)}
                  className="
                    flex items-center gap-1 px-3 py-1.5
                    text-xs font-medium
                    bg-gradient-to-r from-blue-400 to-blue-500 text-white
                    shadow-md shadow-blue-400/25
                    hover:from-blue-500 hover:to-blue-600 hover:shadow-lg hover:shadow-blue-400/30
                    transition-all duration-200 ease-out
                    disabled:opacity-50 disabled:cursor-not-allowed
                    active:scale-95 touch-manipulation
                  "
                  style={{ borderRadius: '4px' }}
                  disabled={!editingName.trim()}
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  {t('history.save')}
                </button>
              </div>
            </div>
          ) : (
            // 正常显示状态
            <div className="flex items-start justify-between mb-3 relative">
              <h4 className="
                text-sm md:text-base font-medium text-gray-900
                group-hover:text-blue-600 transition-colors duration-200
                line-clamp-2 flex-1 mr-3
              ">
                {item.name}
              </h4>

              {/* 编辑图标 - 移动端始终显示，桌面端悬停显示 */}
              <button
                onClick={(e) => handleRenameClick(e, item)}
                className="absolute right-0 top-0 p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-100 rounded transition-all duration-200 opacity-100 md:opacity-0 md:group-hover:opacity-100 touch-manipulation"
                title={t('history.rename')}
                style={{
                  marginTop: '-2px',
                  display: (hoveredId === item.id) || window.innerWidth < 768 ? 'block' : 'block'
                }}
              >
                <svg className="w-4 h-4 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </button>
            </div>
          )}

          {/* 时间 - 编辑状态时隐藏 */}
          {editingId !== item.id && (
            <div className="flex justify-end">
              <span className="text-xs text-gray-500">
                {formatTime(item.updated_at)}
              </span>
            </div>
          )}
        </div>
      ))}

      {/* 查看更多按钮 */}
      <div className="text-center pt-4">
        {hasMore ? (
          <button
            onClick={fetchMoreConversations}
            className="
              px-6 py-2 text-sm font-medium text-gray-600 bg-gray-50
              hover:bg-gray-100 hover:text-gray-700 rounded-xl
              transition-colors duration-200 border border-gray-200
            "
          >
            {t('history.viewMore')}
          </button>
        ) : (
          <p className="text-sm text-gray-500">{t('history.noMoreRecords')}</p>
        )}
      </div>


    </div>
  );
};

export default ConversationList;