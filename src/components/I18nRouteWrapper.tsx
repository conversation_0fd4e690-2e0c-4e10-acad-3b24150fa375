import React, { useEffect, useState, createContext, useContext } from 'react'
import { useParams, useNavigate, useLocation } from 'react-router-dom'
import { setLanguage, getCurrentLanguage } from '../i18n/simple'
import { getDefaultLanguage, getDefaultAppName } from '../utils/envConfig'
import { AppService } from '../utils/appService'

// 创建根域名访问状态的Context
interface RootAccessContextType {
  isRootAccess: boolean
  setIsRootAccess: (value: boolean) => void
  currentLanguage: string
  currentAppName: string
}

const RootAccessContext = createContext<RootAccessContextType | null>(null)

// 导出Hook供其他组件使用
export const useRootAccess = () => {
  const context = useContext(RootAccessContext)
  if (!context) {
    throw new Error('useRootAccess must be used within I18nRouteWrapper')
  }
  return context
}

interface I18nRouteWrapperProps {
  children: React.ReactNode
}

/**
 * 简化的国际化路由包装组件
 * 处理路由重定向和语言同步
 * 优化版本：支持根域名简洁显示，只在用户主动操作时添加后缀
 */
const I18nRouteWrapper: React.FC<I18nRouteWrapperProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [isRedirecting, setIsRedirecting] = useState(false)

  // 根域名访问状态管理
  const [isRootAccess, setIsRootAccess] = useState(false)
  const [currentLanguage, setCurrentLanguage] = useState(getDefaultLanguage())
  const [currentAppName, setCurrentAppName] = useState('')

  useEffect(() => {
    const handleRouting = async () => {
      const currentPath = location.pathname
      const segments = currentPath.split('/').filter(Boolean)
      const defaultLanguage = getDefaultLanguage()
      const appService = AppService.getInstance()

      console.log('I18nRouteWrapper: 路由处理开始', {
        currentPath,
        segments,
        defaultLanguage
      })

      // 根路径特殊处理 - 不重定向，设置为根域名访问状态
      if (currentPath === '/') {
        console.log('I18nRouteWrapper: 根路径访问，设置根域名状态')

        // 根据域名和环境决定根路径的语言策略
        const hostname = window.location.hostname
        // 检查是否为国际站：生产域名或international模式
        const isInternationalMode = import.meta.env.MODE === 'international'
        const isInternationalSite = hostname === 'medxy.ai' || hostname === 'www.medxy.ai' || isInternationalMode

        let targetLanguage = defaultLanguage

        if (isInternationalSite) {
          // 国际站：优先使用用户的语言偏好，如果没有则使用英文
          try {
            const savedLanguage = localStorage.getItem('language') as 'zh' | 'en' | null
            if (savedLanguage && (savedLanguage === 'zh' || savedLanguage === 'en')) {
              targetLanguage = savedLanguage
              console.log('I18nRouteWrapper: 国际站使用用户偏好语言', {
                savedLanguage,
                reason: '用户之前的语言选择'
              })
            } else {
              targetLanguage = 'en'
              console.log('I18nRouteWrapper: 国际站使用默认英文', {
                reason: 'localStorage中无有效语言偏好'
              })
            }
          } catch (error) {
            console.warn('I18nRouteWrapper: localStorage访问失败，国际站使用英文', error)
            targetLanguage = 'en'
          }
        } else {
          // 非国际站：统一使用中文，忽略用户的语言偏好
          targetLanguage = 'zh'
          console.log('I18nRouteWrapper: 非国际站强制使用中文', {
            hostname,
            reason: '非国际站域名，统一使用中文'
          })
        }

        console.log('I18nRouteWrapper: 根路径语言决策', {
          hostname,
          isInternationalSite,
          defaultLanguage,
          targetLanguage,
          strategy: isInternationalSite ? '国际站策略' : '非国际站策略'
        })

        setIsRootAccess(true)
        setCurrentLanguage(targetLanguage)
        setLanguage(targetLanguage)

        // 获取默认应用名称
        try {
          const cacheStatus = appService.getCacheStatus()
          let firstAppName = ''

          if (cacheStatus.isValid) {
            firstAppName = appService.getFirstAvailableAppNameSync()
          } else {
            firstAppName = await appService.getFirstAvailableAppName()
          }

          setCurrentAppName(firstAppName)
          console.log('I18nRouteWrapper: 根路径状态设置完成', {
            language: targetLanguage,
            appName: firstAppName,
            hostname: window.location.hostname,
            isInternationalSite: window.location.hostname === 'medxy.ai' || window.location.hostname === 'www.medxy.ai',
            strategy: (window.location.hostname === 'medxy.ai' || window.location.hostname === 'www.medxy.ai') ? '国际站策略' : '非国际站策略'
          })
        } catch (error) {
          console.error('I18nRouteWrapper: 获取默认应用失败', error)
          setCurrentAppName(getDefaultAppName())
        }

        setIsRedirecting(false)
        return
      }

      // 重置根域名访问状态（当访问带后缀的URL时）
      if (segments.length > 0) {
        setIsRootAccess(false)
      }

      // 检查是否需要重定向（排除根路径）
      const needsRedirect =
        currentPath === '/en/' ||
        currentPath === '/zh/' ||
        (segments.length === 1 && (segments[0] === 'zh' || segments[0] === 'en')) ||
        (segments.length > 0 && segments[0] !== 'zh' && segments[0] !== 'en')

      if (needsRedirect) {
        setIsRedirecting(true)

        try {
          let targetRoute = ''
          let firstAppName: string

          // 优先尝试获取真实的第一个应用
          const cacheStatus = appService.getCacheStatus()
          console.log('I18nRouteWrapper: 开始获取第一个应用', {
            path: currentPath,
            cacheStatus: {
              hasCache: cacheStatus.hasCache,
              isValid: cacheStatus.isValid,
              age: `${(cacheStatus.age / 1000).toFixed(1)}秒`
            }
          })

          if (cacheStatus.isValid) {
            // 使用同步缓存数据
            firstAppName = appService.getFirstAvailableAppNameSync()
            console.log('I18nRouteWrapper: 使用同步缓存数据', {
              cacheAge: `${(cacheStatus.age / 1000).toFixed(1)}秒`,
              firstApp: firstAppName
            })
          } else {
            // 缓存无效时，尝试快速获取第一个应用
            try {
              console.log('I18nRouteWrapper: 缓存无效，尝试API获取应用列表')
              firstAppName = await appService.getFirstAvailableAppName()
              console.log('I18nRouteWrapper: API获取第一个应用成功', {
                firstApp: firstAppName
              })
            } catch (error) {
              console.error('I18nRouteWrapper: API获取失败，使用fallback', {
                error: error instanceof Error ? error.message : String(error),
                fallback: getDefaultAppName()
              })
              firstAppName = getDefaultAppName()
            }
          }

          // 根据不同情况构建目标路由（已移除根路径重定向）
          // 如果是带尾部斜杠的语言路径，重定向到该语言的第一个可用应用
          if (currentPath === '/en/' || currentPath === '/zh/') {
            const lang = currentPath === '/en/' ? 'en' : 'zh'
            targetRoute = `/${lang}/${firstAppName}`
            console.log('I18nRouteWrapper: 带尾部斜杠的语言路径重定向', {
              from: currentPath,
              to: targetRoute,
              language: lang,
              firstApp: firstAppName
            })
          }
          // 如果路径不包含语言代码，添加默认语言
          else if (segments.length > 0 && segments[0] !== 'zh' && segments[0] !== 'en') {
            targetRoute = `/${defaultLanguage}${currentPath}`
            console.log('I18nRouteWrapper: 添加默认语言', {
              from: currentPath,
              to: targetRoute
            })
          }
          // 如果路径只有语言代码，重定向到该语言的第一个可用应用
          else if (segments.length === 1 && (segments[0] === 'zh' || segments[0] === 'en')) {
            targetRoute = `${currentPath}/${firstAppName}`
            console.log('I18nRouteWrapper: 语言路径重定向', {
              from: currentPath,
              to: targetRoute,
              language: segments[0],
              firstApp: firstAppName
            })
          }

          // 立即执行重定向
          if (targetRoute) {
            navigate(targetRoute, { replace: true })
          }
        } catch (error) {
          console.error('I18nRouteWrapper: 重定向处理失败', error)
          // 使用fallback重定向（已移除根路径fallback）
          const fallbackApp = getDefaultAppName()
          const fallbackRoute = segments.length === 1 && (segments[0] === 'zh' || segments[0] === 'en')
            ? `${currentPath}/${fallbackApp}`
            : `/${defaultLanguage}${currentPath}`
          navigate(fallbackRoute, { replace: true })
        } finally {
          setIsRedirecting(false)
        }
        return
      }

      // 同步URL中的语言到全局状态
      if (segments.length > 0 && (segments[0] === 'zh' || segments[0] === 'en')) {
        console.log('I18nRouteWrapper: 同步URL语言到全局状态', {
          path: currentPath,
          detectedLang: segments[0],
          timestamp: new Date().toISOString()
        })
        setLanguage(segments[0] as 'zh' | 'en')
      }

      setIsRedirecting(false)
    }

    handleRouting()
  }, [location.pathname, navigate])

  // 如果正在重定向，返回null避免渲染子组件（防止404闪现）
  if (isRedirecting) {
    return null
  }

  // 提供根域名访问状态的Context
  const contextValue: RootAccessContextType = {
    isRootAccess,
    setIsRootAccess,
    currentLanguage,
    currentAppName
  }

  return (
    <RootAccessContext.Provider value={contextValue}>
      {children}
    </RootAccessContext.Provider>
  )
}

export default I18nRouteWrapper
