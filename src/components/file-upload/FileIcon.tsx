import React from 'react'
import { getFileExtByName } from '../../api/src/utils/file-type'

interface FileIconProps {
  fileName: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const FileIcon: React.FC<FileIconProps> = ({ fileName, size = 'md', className = '' }) => {
  const extension = getFileExtByName(fileName)
  
  // 根据尺寸设置图标大小
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-10 h-10', 
    lg: 'w-16 h-16'
  }
  
  const iconSize = sizeClasses[size]
  
  // 根据文件类型返回对应的图标
  const renderIcon = () => {
    switch (extension) {
      case 'pdf':
        return (
          <div className={`relative ${iconSize} ${className}`}>
            <svg className={`${iconSize} text-red-500`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center mt-1">
              <span className="text-red-800 text-xs font-bold">PDF</span>
            </div>
          </div>
        )
      case 'docx':
        return (
          <div className={`relative ${iconSize} ${className}`}>
            <svg className={`${iconSize} text-blue-500`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center mt-1">
              <span className="text-blue-800 text-xs font-bold">DOC</span>
            </div>
          </div>
        )
      case 'xls':
      case 'xlsx':
        return (
          <div className={`relative ${iconSize} ${className}`}>
            <svg className={`${iconSize} text-green-500`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center mt-1">
              <span className="text-green-800 text-xs font-bold">XLS</span>
            </div>
          </div>
        )
      case 'ppt':
      case 'pptx':
        return (
          <div className={`relative ${iconSize} ${className}`}>
            <svg className={`${iconSize} text-orange-500`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center mt-1">
              <span className="text-orange-800 text-xs font-bold">PPT</span>
            </div>
          </div>
        )
      case 'txt':
        return (
          <div className={`relative ${iconSize} ${className}`}>
            <svg className={`${iconSize} text-gray-500`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center mt-1">
              <span className="text-gray-800 text-xs font-bold">TXT</span>
            </div>
          </div>
        )
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return (
          <div className={`relative ${iconSize} ${className}`}>
            <svg className={`${iconSize} text-purple-500`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center mt-1">
              <span className="text-purple-800 text-xs font-bold">IMG</span>
            </div>
          </div>
        )
      default:
        return (
          <div className={`relative ${iconSize} ${className}`}>
            <svg className={`${iconSize} text-gray-400`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center mt-1">
              <span className="text-gray-800 text-xs font-bold">FILE</span>
            </div>
          </div>
        )
    }
  }

  return renderIcon()
}

export default FileIcon
