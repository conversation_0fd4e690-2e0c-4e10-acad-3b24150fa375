import React from 'react'

interface CrownBorderProps {
  tier: 'base' | 'pro' | 'ultra' | null
  children: React.ReactNode
  className?: string
}

// 皇冠形状的虚线边框组件
const CrownBorder: React.FC<CrownBorderProps> = ({ tier, children, className = '' }) => {
  if (!tier) {
    return <div className={className}>{children}</div>
  }

  // 根据等级获取颜色
  const getStrokeColor = (tier: 'base' | 'pro' | 'ultra') => {
    switch (tier) {
      case 'base':
        return '#9CA3AF' // gray-400
      case 'pro':
        return '#EAB308' // yellow-500
      case 'ultra':
        return '#A855F7' // purple-500
      default:
        return '#9CA3AF'
    }
  }

  const strokeColor = getStrokeColor(tier)

  return (
    <div className={`relative ${className}`}>
      {/* 皇冠装饰虚线边框 */}
      <div className="absolute inset-0 pointer-events-none">
        {/* 主虚线边框 */}
        <div
          className="absolute inset-0 rounded-xl border-2 border-dashed"
          style={{
            borderColor: strokeColor,
            opacity: 0.8,
            borderWidth: '2px',
            borderStyle: 'dashed'
          }}
        />

        {/* 皇冠装饰元素 - 顶部中心 */}
        <div className="absolute -top-1.5 left-1/2 transform -translate-x-1/2">
          <div
            className="w-3 h-3 border-2 border-dashed"
            style={{
              borderColor: strokeColor,
              borderRadius: '50% 50% 0 0',
              opacity: 0.9
            }}
          />
        </div>

        {/* 皇冠装饰元素 - 左上角 */}
        <div className="absolute -top-0.5 left-2">
          <div
            className="w-2 h-2 border border-dashed"
            style={{
              borderColor: strokeColor,
              borderRadius: '50% 50% 0 0',
              opacity: 0.8
            }}
          />
        </div>

        {/* 皇冠装饰元素 - 右上角 */}
        <div className="absolute -top-0.5 right-2">
          <div
            className="w-2 h-2 border border-dashed"
            style={{
              borderColor: strokeColor,
              borderRadius: '50% 50% 0 0',
              opacity: 0.8
            }}
          />
        </div>
      </div>

      {/* 内容 */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default CrownBorder
