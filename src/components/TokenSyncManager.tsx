import { useEffect, useRef } from 'react'
import Cookies from 'js-cookie'
import { apiManager } from '../utils/apiManager'

/**
 * Token同步管理组件
 * 负责监听登录状态变化，确保API实例的token始终是最新的
 */
const TokenSyncManager: React.FC = () => {
  const lastTokenRef = useRef<string>('')
  const lastUserRef = useRef<string>('')

  useEffect(() => {
    // 检查并同步token的函数
    const syncToken = () => {
      const currentToken = Cookies.get('medxyToken') || ''
      const userInfoString = Cookies.get('userInfo')
      let currentUser = 'nologin'

      if (userInfoString) {
        try {
          const userInfo = JSON.parse(userInfoString)
          currentUser = userInfo.userName || 'nologin'
        } catch (error) {
          console.error('解析用户信息失败:', error)
        }
      }

      // 检查token或用户是否发生变化
      if (currentToken !== lastTokenRef.current || currentUser !== lastUserRef.current) {
        console.log('🔄 检测到token或用户变化:', {
          oldToken: lastTokenRef.current.substring(0, 10) + '...',
          newToken: currentToken.substring(0, 10) + '...',
          oldUser: lastUserRef.current,
          newUser: currentUser
        })

        // 更新API管理器的token
        if (currentToken) {
          apiManager.updateToken(currentToken, currentUser)
          console.log('✅ API实例token已同步')
        } else {
          apiManager.clearInstances()
          console.log('🧹 API实例已清除（无token）')
        }

        // 更新引用
        lastTokenRef.current = currentToken
        lastUserRef.current = currentUser
      }
    }

    // 监听用户信息更新事件
    const handleUserInfoUpdated = (event: CustomEvent) => {
      console.log('📢 TokenSyncManager收到用户信息更新事件')
      
      // 减少延迟，加快响应速度
      setTimeout(() => {
        syncToken()
      }, 10)
    }

    // 监听登录状态变化事件
    const handleLoginStatusChanged = (event: CustomEvent) => {
      console.log('📢 TokenSyncManager收到登录状态变化事件:', event.detail.type)
      
      if (event.detail.type === 'logout') {
        apiManager.clearInstances()
        lastTokenRef.current = ''
        lastUserRef.current = 'nologin'
        console.log('🧹 已清除所有API实例')
      } else {
        // 减少延迟，加快响应速度
        setTimeout(() => {
          syncToken()
        }, 10)
      }
    }

    // 监听Cookie变化（使用MutationObserver监听document.cookie的变化）
    const checkCookieChanges = () => {
      syncToken()
    }

    // 添加事件监听器
    window.addEventListener('userInfoUpdated', handleUserInfoUpdated as EventListener)
    window.addEventListener('loginStatusChanged', handleLoginStatusChanged as EventListener)

    // 初始同步
    syncToken()

    // 定期检查token变化（备用方案）
    const interval = setInterval(checkCookieChanges, 2000)

    // 监听页面可见性变化，当页面重新可见时检查token
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        syncToken()
      }
    }
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 监听存储事件（跨标签页同步）
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'medxyToken' || event.key === 'userInfo') {
        console.log('📢 检测到跨标签页存储变化')
        setTimeout(() => {
          syncToken()
        }, 100)
      }
    }
    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('userInfoUpdated', handleUserInfoUpdated as EventListener)
      window.removeEventListener('loginStatusChanged', handleLoginStatusChanged as EventListener)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('storage', handleStorageChange)
      clearInterval(interval)
    }
  }, [])

  // 这个组件不渲染任何内容
  return null
}

export default TokenSyncManager
