/**
 * Mermaid 图表组件
 * 模拟 Dify 项目实现，支持弹窗放大缩小
 */

import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';

interface MermaidDiagramProps {
  code: string;
  className?: string;
  onError?: (error: string) => void;
}

// 弹窗组件
interface MermaidModalProps {
  isOpen: boolean;
  onClose: () => void;
  svg: string;
  code: string;
}

const MermaidModal: React.FC<MermaidModalProps> = ({ isOpen, onClose, svg, code }) => {
  const [scale, setScale] = useState(1.5); // 默认放大到 150%
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  if (!isOpen) return null;

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.25, 5)); // 最大 500%
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.25, 0.3)); // 最小 30%
  };

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '=':
          case '+':
            e.preventDefault();
            handleZoomIn();
            break;
          case '-':
            e.preventDefault();
            handleZoomOut();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleDownload = () => {
    const blob = new Blob([svg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mermaid-diagram.svg';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-75">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0"
        onClick={onClose}
      />

      {/* 弹窗内容 */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-[90vw] max-h-[90vh] flex flex-col">
        {/* 头部工具栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Mermaid 图表</h3>
          <div className="flex items-center space-x-2">
            {/* 缩放控制 */}
            <button
              onClick={handleZoomOut}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
              title="缩小"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </button>

            <span className="text-sm text-gray-600 min-w-[60px] text-center">
              {Math.round(scale * 100)}%
            </span>

            <button
              onClick={handleZoomIn}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
              title="放大"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>

            <div className="w-px h-6 bg-gray-300" />

            {/* 下载按钮 */}
            <button
              onClick={handleDownload}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
              title="下载 SVG"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>

            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
              title="关闭"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 图表容器 */}
        <div
          className="flex-1 overflow-hidden bg-gray-50 relative"
          style={{ minHeight: '400px', minWidth: '600px' }}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <div
            className={`absolute inset-0 flex items-center justify-center ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
            style={{
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transformOrigin: 'center center'
            }}
            onMouseDown={handleMouseDown}
          >
            <div
              className="bg-white p-4 rounded shadow-lg"
              dangerouslySetInnerHTML={{ __html: svg }}
            />
          </div>
        </div>

        {/* 底部信息 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <details>
            <summary className="text-sm text-gray-600 cursor-pointer hover:text-gray-800">
              查看源代码
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 p-3 rounded border overflow-x-auto max-h-32">
              {code}
            </pre>
          </details>
        </div>
      </div>
    </div>
  );
};

// 全局初始化状态
let mermaidInitialized = false;

// 处理 Mermaid 代码中的 Markdown 格式和文本换行
const processMermaidMarkdown = (code: string): string => {
  let processedCode = code;

  // 处理粗体 **text** -> <b>text</b>
  processedCode = processedCode.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>');

  // 处理斜体 *text* -> <i>text</i>
  processedCode = processedCode.replace(/\*(.*?)\*/g, '<i>$1</i>');

  // 处理代码 `code` -> <code>code</code>
  processedCode = processedCode.replace(/`([^`]+)`/g, '<code>$1</code>');

  // 处理删除线 ~~text~~ -> <s>text</s>
  processedCode = processedCode.replace(/~~(.*?)~~/g, '<s>$1</s>');

  // 处理长文本换行 - 只在真正需要时换行，保持题号和题目在一起
  processedCode = processedCode.replace(/\[([^\]]{40,})\]/g, (_, content) => {
    const maxLength = 30; // 每行最大字符数，更宽松

    // 检查是否包含题号模式（如：1.1、2.3、A1、B2等）
    const titlePattern = /^(\d+\.?\d*\.?\s*|[A-Z]\d*\.?\s*)/;
    const titleMatch = content.match(titlePattern);

    if (titleMatch) {
      // 有题号的情况：保持题号和第一部分内容在一起
      const titlePart = titleMatch[0];
      const restContent = content.substring(titlePart.length);

      if (content.length <= maxLength * 1.5) {
        return `[${content}]`; // 不需要换行，给更多容忍度
      }

      // 找到第一个合适的断点（空格、标点符号等）
      let firstLineEnd = maxLength - titlePart.length;
      const breakPoints = [' ', '，', '。', '：', '；', '、'];

      // 如果剩余内容不长，就不要强制换行
      if (restContent.length <= maxLength * 0.8) {
        return `[${content}]`; // 保持在一行
      }

      for (let i = firstLineEnd; i >= titlePart.length / 2; i--) {
        if (breakPoints.includes(restContent[i])) {
          firstLineEnd = i + 1;
          break;
        }
      }

      const firstLine = titlePart + restContent.substring(0, firstLineEnd);
      const remainingText = restContent.substring(firstLineEnd);

      if (remainingText.length === 0) {
        return `[${firstLine}]`;
      }

      // 处理剩余文本
      const lines = [firstLine];
      let currentPos = 0;

      while (currentPos < remainingText.length) {
        let lineEnd = Math.min(currentPos + maxLength, remainingText.length);

        // 寻找合适的断点
        if (lineEnd < remainingText.length) {
          for (let i = lineEnd; i >= currentPos + maxLength / 2; i--) {
            if (breakPoints.includes(remainingText[i])) {
              lineEnd = i + 1;
              break;
            }
          }
        }

        lines.push(remainingText.substring(currentPos, lineEnd).trim());
        currentPos = lineEnd;
      }

      return `[${lines.filter(line => line.length > 0).join('<br/>')}]`;
    } else {
      // 没有题号的普通文本处理
      const words = content.split(/(\s+|，|。|：|；|、)/);
      let lines = [];
      let currentLine = '';

      for (const word of words) {
        if ((currentLine + word).length > maxLength && currentLine.length > 0) {
          lines.push(currentLine.trim());
          currentLine = word;
        } else {
          currentLine += word;
        }
      }
      if (currentLine.trim()) {
        lines.push(currentLine.trim());
      }

      return `[${lines.join('<br/>')}]`;
    }
  });

  return processedCode;
};

const MermaidDiagram: React.FC<MermaidDiagramProps> = ({
  code,
  className = '',
  onError
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [svg, setSvg] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const renderDiagram = async () => {
      // 开始渲染 Mermaid 图表

      setIsLoading(true);
      setError('');
      setSvg('');

      // 添加超时保护
      const timeoutId = setTimeout(() => {
        setError('渲染超时，请检查代码格式');
        setIsLoading(false);
      }, 10000); // 10秒超时

      try {
        // 初始化 Mermaid（只初始化一次）
        if (!mermaidInitialized) {
          console.log('初始化 Mermaid...');
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'inherit',
            htmlLabels: true, // 启用 HTML 标签支持
            flowchart: {
              htmlLabels: true
            }
          });
          mermaidInitialized = true;
          console.log('Mermaid 初始化完成');
        }

        // 简单的代码验证
        if (!code || !code.trim()) {
          throw new Error('Mermaid 代码为空');
        }

        // 基本的代码清理
        let cleanCode = code.trim();
        console.log('原始代码:', cleanCode);

        // 修复连续节点定义问题
        cleanCode = cleanCode.replace(/(\]\s*)([A-Z]\d*\[)/g, '$1\n$2');

        // 处理 Markdown 格式
        cleanCode = processMermaidMarkdown(cleanCode);
        console.log('清理后代码:', cleanCode);

        // 验证代码格式
        const validStarters = [
          'graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram',
          'erDiagram', 'journey', 'gantt', 'pie', 'gitGraph', 'mindmap'
        ];

        const isValid = validStarters.some(starter => cleanCode.startsWith(starter));
        if (!isValid) {
          throw new Error('无效的 Mermaid 图表类型');
        }

        // 生成唯一ID
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        // 渲染图表
        const { svg: renderedSvg } = await mermaid.render(id, cleanCode);

        // 处理 SVG 以确保文本左对齐
        const processedSvg = renderedSvg.replace(
          /<text([^>]*?)>/g,
          (match, attrs) => {
            // 如果已经有 text-anchor，就不修改
            if (attrs.includes('text-anchor')) {
              return match;
            }
            // 添加左对齐属性
            return `<text${attrs} text-anchor="start">`;
          }
        );

        clearTimeout(timeoutId);
        setSvg(processedSvg);
        setError('');
      } catch (err: any) {
        clearTimeout(timeoutId);
        const errorMessage = err.message || '渲染失败';
        // 静默处理渲染错误
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    renderDiagram();
  }, [code, onError]);

  // 错误状态
  if (error) {
    return (
      <div className={`mermaid-error border border-red-200 bg-red-50 rounded-lg p-4 ${className}`}>
        <div className="flex items-start space-x-2">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-red-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">图表渲染失败</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
            <details className="mt-2">
              <summary className="text-sm text-red-600 cursor-pointer hover:text-red-800">
                查看源代码
              </summary>
              <pre className="mt-2 text-xs bg-red-100 p-2 rounded border overflow-x-auto">
                {code}
              </pre>
            </details>
          </div>
        </div>
      </div>
    );
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className={`mermaid-loading flex items-center justify-center p-8 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
          <span className="text-gray-600 text-sm">正在渲染图表...</span>
        </div>
      </div>
    );
  }

  // 成功渲染
  return (
    <>
      <div
        ref={containerRef}
        className={`mermaid-container border border-gray-200 rounded-lg p-4 bg-white overflow-x-auto relative group ${className}`}
      >
        {svg && (
          <>
            {/* 图表内容 */}
            <div
              className="mermaid-svg cursor-pointer"
              dangerouslySetInnerHTML={{ __html: svg }}
              onClick={() => setIsModalOpen(true)}
            />

            {/* 悬浮工具栏 */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="flex items-center space-x-1 bg-white rounded-lg shadow-lg border border-gray-200 p-1">
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
                  title="放大查看"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </button>

                <button
                  onClick={() => {
                    const blob = new Blob([svg], { type: 'image/svg+xml' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'mermaid-diagram.svg';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
                  title="下载 SVG"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </button>
              </div>
            </div>

            {/* 点击提示 */}
            <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                点击放大查看
              </div>
            </div>
          </>
        )}
      </div>

      {/* 弹窗 */}
      <MermaidModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        svg={svg}
        code={code}
      />
    </>
  );
};

export default MermaidDiagram;
