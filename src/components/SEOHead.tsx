import React from 'react'
import { Helmet } from 'react-helmet-async'
import { useLocation } from 'react-router-dom'
import { useSimpleTranslation } from '../i18n/simple-hooks'

export interface SEOHeadProps {
  /** 页面标题 */
  title?: string
  /** 页面描述 */
  description?: string
  /** 关键词 */
  keywords?: string
  /** 应用名称键 */
  appKey?: string
  /** 页面类型 */
  pageType?: 'home' | 'chat' | 'newChat' | 'loading' | 'error' | 'notFound' | 'login' | 'signup'
  /** 自定义 Open Graph 图片 */
  ogImage?: string
  /** 是否为文章页面 */
  isArticle?: boolean
  /** 文章发布时间 */
  publishedTime?: string
  /** 文章修改时间 */
  modifiedTime?: string
  /** 作者信息 */
  author?: string
  /** 自定义 canonical URL */
  canonicalUrl?: string
}

/**
 * SEO 头部组件
 * 提供完整的 SEO 元数据支持，包括 Open Graph、Twitter Cards、结构化数据等
 * 专为预渲染优化，确保搜索引擎能够正确抓取页面信息
 */
const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords,
  appKey,
  pageType = 'home',
  ogImage,
  isArticle = false,
  publishedTime,
  modifiedTime,
  author,
  canonicalUrl
}) => {
  const { t } = useSimpleTranslation()
  const location = useLocation()

  // 生成默认值
  const defaultTitle = ''
  const defaultDescription = ''
  const defaultKeywords = ''
  const defaultOgImage = ''

  // 检测语言
  const isEnglish = location.pathname.includes('/en/')

  // 根据应用和语言生成元数据
  const generateMetadata = () => {
    let finalTitle = title
    let finalDescription = description
    let finalKeywords = keywords

    if (!finalTitle || !finalDescription) {
      // 根据应用类型生成默认元数据
      if (appKey) {
        switch (appKey) {
          case 'novax-base':
            if (isEnglish) {
              finalTitle = finalTitle || 'Research Inspiration Engine | Innovation Design Tool'
              finalDescription = finalDescription || 'Quickly generate innovative research ideas! Input research background to rapidly obtain research direction design, experimental ideas and preliminary concepts to help launch research projects'
              finalKeywords = finalKeywords || 'Research Design,Innovation Inspiration,Project Direction,Experimental Design,Research Assistant,Research Concepts'
            } else {
              finalTitle = finalTitle || '科研灵感引擎 | 创新思路设计工具'
              finalDescription = finalDescription || '快速生成科研创新思路！输入研究背景，快速获取科研方向设计、实验思路与初步构想，助力启动研究项目'
              finalKeywords = finalKeywords || '科研思路设计,创新灵感,课题方向,实验设计,科研助手,研究构想'
            }
            break

          case 'novax-pro':
            if (isEnglish) {
              finalTitle = finalTitle || 'Research Inspiration Engine | Advanced Innovation Design Tool'
              finalDescription = finalDescription || 'Quickly generate innovative research ideas! Input research background to rapidly obtain research direction design, experimental ideas and preliminary concepts to help launch research projects'
              finalKeywords = finalKeywords || 'Research Design,Innovation Inspiration,Project Direction,Experimental Design,Research Assistant,Research Concepts'
            } else {
              finalTitle = finalTitle || '科研灵感引擎 | 高级创新思路设计工具'
              finalDescription = finalDescription || '快速生成科研创新思路！输入研究背景，快速获取科研方向设计、实验思路与初步构想，助力启动研究项目'
              finalKeywords = finalKeywords || '科研思路设计,创新灵感,课题方向,实验设计,科研助手,研究构想'
            }
            break

          case 'novax-ultra':
            if (isEnglish) {
              finalTitle = finalTitle || 'NovaX Ultra - Building Innovation Ecosystem | Medxy AI'
              finalDescription = finalDescription || 'NovaX Ultra builds innovation ecosystem with strategic topic clusters, beyond single ideas.'
              finalKeywords = finalKeywords || 'NovaX Ultra,Innovation Ecosystem,Strategic Research,Medical AI'
            } else {
              finalTitle = finalTitle || 'NovaX Ultra - 构筑创新生态 | Medxy AI'
              finalDescription = finalDescription || 'NovaX Ultra构筑创新生态，战略性课题集群，超越单一思路。'
              finalKeywords = finalKeywords || 'NovaX Ultra,创新生态,战略研究,医学AI'
            }
            break

          case 'elavax-base':
            if (isEnglish) {
              finalTitle = finalTitle || 'Research Opportunity Insight Platform | Academic Development Intelligence Matching'
              finalDescription = finalDescription || 'Intelligent research development opportunity analysis tool! Deep interpretation of research results, precise matching of target journals/platforms, mining academic collaboration and transformation opportunities to boost career development'
              finalKeywords = finalKeywords || 'Research Opportunity Insight,Academic Platform Matching,Journal Recommendation,Research Translation,AI Academic Expert'
            } else {
              finalTitle = finalTitle || '科研机遇洞察平台 | 学术发展智能匹配'
              finalDescription = finalDescription || '科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展'
              finalKeywords = finalKeywords || '科研机遇洞察,学术平台匹配,期刊推荐,研究转化,AI学术专家'
            }
            break

          case 'elavax-pro':
            if (isEnglish) {
              finalTitle = finalTitle || 'Research Opportunity Insight Platform | Advanced Academic Development Intelligence Matching'
              finalDescription = finalDescription || 'Intelligent research development opportunity analysis tool! Deep interpretation of research results, precise matching of target journals/platforms, mining academic collaboration and transformation opportunities to boost career development'
              finalKeywords = finalKeywords || 'Research Opportunity Insight,Academic Platform Matching,Journal Recommendation,Research Translation,AI Academic Expert'
            } else {
              finalTitle = finalTitle || '科研机遇洞察平台 | 高级学术发展智能匹配'
              finalDescription = finalDescription || '科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展'
              finalKeywords = finalKeywords || '科研机遇洞察,学术平台匹配,期刊推荐,研究转化,AI学术专家'
            }
            break

          case 'elavax-ultra':
            if (isEnglish) {
              finalTitle = finalTitle || 'ElaVaX Ultra - Comprehensive Analysis Platform | Medxy AI'
              finalDescription = finalDescription || 'ElaVaX Ultra provides comprehensive medical data analysis platform with enterprise-level features.'
              finalKeywords = finalKeywords || 'ElaVaX Ultra,Comprehensive Analysis,Enterprise Platform,Medical Data,Medical AI'
            } else {
              finalTitle = finalTitle || 'ElaVaX Ultra - 综合分析平台 | Medxy AI'
              finalDescription = finalDescription || 'ElaVaX Ultra提供综合医学数据分析平台，具备企业级功能。'
              finalKeywords = finalKeywords || 'ElaVaX Ultra,综合分析,企业平台,医学数据,医学AI'
            }
            break
        }
      }

      // 根据页面类型生成默认元数据
      if (!finalTitle || !finalDescription) {
        switch (pageType) {
          case 'login':
            if (isEnglish) {
              finalTitle = finalTitle || 'Login - Medxy AI'
              finalDescription = finalDescription || 'Login to Medxy AI medical agent platform to access professional medical AI tools and services.'
              finalKeywords = finalKeywords || 'Login,Medxy AI,Medical AI,Authentication'
            } else {
              finalTitle = finalTitle || '登录 - Medxy AI'
              finalDescription = finalDescription || '登录Medxy AI医学智能体平台，访问专业的医学AI工具和服务。'
              finalKeywords = finalKeywords || '登录,Medxy AI,医学AI,身份验证'
            }
            break

          case 'signup':
            if (isEnglish) {
              finalTitle = finalTitle || 'Sign Up - Medxy AI'
              finalDescription = finalDescription || 'Sign up for Medxy AI medical agent platform to access professional medical AI tools and services.'
              finalKeywords = finalKeywords || 'Sign Up,Medxy AI,Medical AI,Registration'
            } else {
              finalTitle = finalTitle || '注册 - Medxy AI'
              finalDescription = finalDescription || '注册Medxy AI医学智能体平台，访问专业的医学AI工具和服务。'
              finalKeywords = finalKeywords || '注册,Medxy AI,医学AI,用户注册'
            }
            break

          case 'chat':
            if (isEnglish) {
              finalTitle = finalTitle || 'Chat - Medxy AI'
              finalDescription = finalDescription || 'Chat with Medxy AI medical agents for professional medical assistance and consultation.'
              finalKeywords = finalKeywords || 'Chat,Medxy AI,Medical AI,Medical Consultation'
            } else {
              finalTitle = finalTitle || '对话 - Medxy AI'
              finalDescription = finalDescription || '与Medxy AI医学智能体对话，获得专业的医学协助和咨询。'
              finalKeywords = finalKeywords || '对话,Medxy AI,医学AI,医学咨询'
            }
            break

          case 'newChat':
            if (isEnglish) {
              finalTitle = finalTitle || 'New Chat - Medxy AI'
              finalDescription = finalDescription || 'Start a new conversation with Medxy AI medical agents for professional medical assistance.'
              finalKeywords = finalKeywords || 'New Chat,Medxy AI,Medical AI,Medical Consultation'
            } else {
              finalTitle = finalTitle || '新建对话 - Medxy AI'
              finalDescription = finalDescription || '开始与Medxy AI医学智能体的新对话，获得专业的医学协助。'
              finalKeywords = finalKeywords || '新建对话,Medxy AI,医学AI,医学咨询'
            }
            break

          default:
            if (isEnglish) {
              finalTitle = finalTitle || 'Medxy AI - Medical AI Agent Platform'
              finalDescription = finalDescription || 'MedSci\'s Medxy AI is a medical AI agent platform providing medical writing, translation, patient care, diagnosis, treatment, and consultation services.'
              finalKeywords = finalKeywords || 'Medxy AI,Medical AI,AI Agent,Medical Writing,Medical Tools,Medical Q&A'
            } else {
              finalTitle = finalTitle || defaultTitle
              finalDescription = finalDescription || defaultDescription
              finalKeywords = finalKeywords || defaultKeywords
            }
        }
      }
    }

    return {
      title: finalTitle || defaultTitle,
      description: finalDescription || defaultDescription,
      keywords: finalKeywords || defaultKeywords
    }
  }

  const metadata = generateMetadata()
  const currentUrl = `${window.location.origin}/${location.pathname}`
  const finalCanonicalUrl = canonicalUrl || currentUrl
  const finalOgImage = ogImage || defaultOgImage

  // 生成结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": isArticle ? "Article" : "WebApplication",
    "name": metadata.title,
    "description": metadata.description,
    "url": currentUrl,
    ...(isArticle ? {
      "headline": metadata.title,
      "author": {
        "@type": "Organization",
        "name": author || "Medxy AI"
      },
      "publisher": {
        "@type": "Organization",
        "name": "MedSci",
        "logo": {
          "@type": "ImageObject",
          "url": finalOgImage
        }
      },
      "image": finalOgImage,
      ...(publishedTime && { "datePublished": publishedTime }),
      ...(modifiedTime && { "dateModified": modifiedTime })
    } : {
      "applicationCategory": "HealthApplication",
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "CNY"
      }
    })
  }

  return (
    <Helmet>
      {/* 基础元数据 */}
      <title>{metadata.title}</title>
      <meta name="description" content={metadata.description} />
      <meta name="keywords" content={metadata.keywords} />
      <link rel="canonical" href={finalCanonicalUrl} />

      {/* Open Graph 元数据 */}
      <meta property="og:type" content={isArticle ? "article" : "website"} />
      <meta property="og:title" content={metadata.title} />
      <meta property="og:description" content={metadata.description} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={finalOgImage} />
      <meta property="og:locale" content={isEnglish ? "en_US" : "zh_CN"} />

      {/* Twitter Cards 元数据 */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={metadata.title} />
      <meta name="twitter:description" content={metadata.description} />
      <meta name="twitter:image" content={finalOgImage} />

      {/* 文章特定元数据 */}
      {isArticle && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {isArticle && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {isArticle && author && (
        <meta property="article:author" content={author} />
      )}

      {/* 结构化数据 */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData, null, 2)}
      </script>

      {/* 其他 SEO 元数据 */}
      <meta name="robots" content="index, follow" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="format-detection" content="telephone=no" />
    </Helmet>
  )
}

export default SEOHead
