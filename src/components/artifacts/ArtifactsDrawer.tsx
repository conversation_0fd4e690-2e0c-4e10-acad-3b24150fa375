import React, { useState, useEffect, useRef } from 'react'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import DataScoreRadarChart from './DataScoreRadarChart'

interface ArtifactItem {
  id: string
  name: string
  type: 'txt' | 'md' | 'pdf' | 'doc' | 'xlsx'
  size: string
  createdAt: Date
}

interface ArtifactsDrawerProps {
  isOpen: boolean
  onClose: () => void
  width?: number
  onWidthChange?: (width: number) => void
  onResizeStart?: () => void
  onResizeEnd?: () => void
}

const ArtifactsDrawer: React.FC<ArtifactsDrawerProps> = ({ 
  isOpen, 
  onClose, 
  width = 50, // Default to 50% of viewport width
  onWidthChange,
  onResizeStart,
  onResizeEnd
}) => {
  const { t } = useSimpleTranslation()
  
  // Resizing state and refs
  const [isResizing, setIsResizing] = useState(false)
  const [currentWidth, setCurrentWidth] = useState(width)
  const resizeRef = useRef<HTMLDivElement>(null)
  
  // Min and max width constraints (as percentage of viewport width)
  const MIN_WIDTH = 25 // 25%
  const MAX_WIDTH = 75 // 75%
  
  // Radar chart scores state (0-100 scale)
  const [radarScores, setRadarScores] = useState<{
    sampleSize: number
    dataCompleteness: number
    structureLevel: number
    endpoints: number
    innovation: number
  } | undefined>(undefined)

  // Sample artifacts list (empty initially)
  const [artifacts] = useState<ArtifactItem[]>([])

  // Update current width when prop changes
  useEffect(() => {
    setCurrentWidth(width)
  }, [width])

  // Mouse event handlers for resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
    onResizeStart?.()
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return

    const viewportWidth = window.innerWidth
    const mouseX = e.clientX
    const newWidthPx = viewportWidth - mouseX
    const newWidthPercent = (newWidthPx / viewportWidth) * 100

    // Constrain within min/max bounds
    const constrainedWidth = Math.min(Math.max(newWidthPercent, MIN_WIDTH), MAX_WIDTH)
    
    setCurrentWidth(constrainedWidth)
    onWidthChange?.(constrainedWidth)
  }

  const handleMouseUp = () => {
    setIsResizing(false)
    onResizeEnd?.()
  }

  // Add/remove global mouse event listeners
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'ew-resize'
      document.body.style.userSelect = 'none'
    } else {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isResizing])

  // Debug function to populate radar chart with random values
  const handleDebugFillScores = () => {
    setRadarScores({
      sampleSize: Math.floor(Math.random() * 80) + 20, // 20-100 range for better visualization
      dataCompleteness: Math.floor(Math.random() * 80) + 20,
      structureLevel: Math.floor(Math.random() * 80) + 20,
      endpoints: Math.floor(Math.random() * 80) + 20,
      innovation: Math.floor(Math.random() * 80) + 20
    })
  }

  const getFileIcon = (type: string) => {
    const iconClass = "w-4 h-4"
    switch (type) {
      case 'txt':
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
      case 'md':
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        )
      case 'pdf':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
        )
      default:
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
    }
  }

  if (!isOpen) return null

  return (
    <div 
      className={`fixed top-0 right-0 h-full bg-white shadow-xl border-l border-gray-200 z-40 flex flex-row ${
        isResizing ? '' : 'transition-all duration-300'
      }`}
      style={{ width: `${currentWidth}vw` }}
    >
      {/* Resize Handle */}
      <div
        ref={resizeRef}
        className={`w-4 h-full bg-transparent hover:bg-blue-50 cursor-ew-resize relative transition-colors duration-200 flex-shrink-0 group ${
          isResizing ? 'bg-blue-50' : ''
        }`}
        onMouseDown={handleMouseDown}
        title={t('artifacts.dragToResize')}
      >
        {/* Resize handle grip dots */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col space-y-1.5">
          <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
            isResizing ? 'bg-blue-500' : 'bg-gray-400 group-hover:bg-blue-500'
          }`} />
          <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
            isResizing ? 'bg-blue-500' : 'bg-gray-400 group-hover:bg-blue-500'
          }`} />
          <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
            isResizing ? 'bg-blue-500' : 'bg-gray-400 group-hover:bg-blue-500'
          }`} />
        </div>
      </div>

      {/* Main drawer content */}
      <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">{t('artifacts.title')}</h2>
        <button
          onClick={onClose}
          className="p-2 text-gray-500 hover:text-gray-700 rounded-lg transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        {/* Radar Chart Section */}
        <div className="mb-8">
          <div 
            className="rounded-lg p-4"
            style={{ height: '380px', backgroundColor: 'transparent' }}
          >
            <DataScoreRadarChart scores={radarScores} />
          </div>
        </div>

        {/* Artifacts List */}
        <div className="space-y-4">
          {artifacts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-sm font-medium">{t('artifacts.noArtifacts')}</p>
              <p className="text-xs text-gray-400 mt-1">{t('artifacts.noArtifactsDescription')}</p>
            </div>
          ) : (
            artifacts.map((artifact) => (
              <div
                key={artifact.id}
                className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all duration-200 cursor-pointer"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-gray-600">
                    {getFileIcon(artifact.type)}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 text-sm">{artifact.name}</div>
                    <div className="text-xs text-gray-500">{artifact.size} • {artifact.createdAt.toLocaleDateString()}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                  </button>
                  <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Debug Button */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={handleDebugFillScores}
          className="w-full px-4 py-2 bg-red-500 text-white text-sm font-medium rounded-lg hover:bg-red-600 transition-colors"
        >
          {t('artifacts.debugFillScores')}
        </button>
      </div>
      </div>
    </div>
  )
}

export default ArtifactsDrawer