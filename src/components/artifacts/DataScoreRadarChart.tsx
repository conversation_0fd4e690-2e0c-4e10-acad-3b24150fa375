import type React from 'react'
import { useMemo } from 'react'
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, Tooltip } from 'recharts'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import { type DataScoreRadarChartProps, type RadarChartData, CategoryTooltipKey } from '../../types/artifacts'

const DataScoreRadarChart: React.FC<DataScoreRadarChartProps> = ({ scores, isLoading = false }) => {
  const { t } = useSimpleTranslation()

  // Transform scores into radar chart data
  const chartData: RadarChartData[] = useMemo(() => {
    if (!scores) {
      // Return empty/placeholder data when no scores
      return [
        { category: t('artifacts.categories.sampleSize'), value: 0, fullMark: 100, key: 'sampleSize' },
        { category: t('artifacts.categories.dataCompleteness'), value: 0, fullMark: 100, key: 'dataCompleteness' },
        { category: t('artifacts.categories.structureLevel'), value: 0, fullMark: 100, key: 'structureLevel' },
        { category: t('artifacts.categories.endpoints'), value: 0, fullMark: 100, key: 'endpoints' },
        { category: t('artifacts.categories.innovation'), value: 0, fullMark: 100, key: 'innovation' }
      ]
    }

    return [
      { category: t('artifacts.categories.sampleSize'), value: scores.sampleSize, fullMark: 100, key: 'sampleSize' },
      { category: t('artifacts.categories.dataCompleteness'), value: scores.dataCompleteness, fullMark: 100, key: 'dataCompleteness' },
      { category: t('artifacts.categories.structureLevel'), value: scores.structureLevel, fullMark: 100, key: 'structureLevel' },
      { category: t('artifacts.categories.endpoints'), value: scores.endpoints, fullMark: 100, key: 'endpoints' },
      { category: t('artifacts.categories.innovation'), value: scores.innovation, fullMark: 100, key: 'innovation' }
    ]
  }, [scores, t])

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as RadarChartData
      const tooltipText = t(`artifacts.categoryTooltip.${data.key}` as const)

      return (
        <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200 max-w-xs">
          <p className="font-medium text-gray-900 mb-1">{data.category}</p>
          <p className="text-sm text-gray-600 mb-2">{tooltipText}</p>
          <p className="text-sm font-medium text-blue-600">
            {t('common.score')}: {data.value}/100
          </p>
        </div>
      )
    }
    return null
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full h-80 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-3">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <p className="text-sm text-gray-500">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-80">
      <ResponsiveContainer width="100%" height="100%">
        <RadarChart data={chartData} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
          <PolarGrid stroke="#e5e7eb" />
          <PolarAngleAxis
            dataKey="category"
            tick={{
              fontSize: 12,
              fill: '#374151',
              textAnchor: 'middle'
            }}
            className="text-xs font-medium"
          />
          <PolarRadiusAxis
            angle={90}
            domain={[0, 100]}
            tick={{ fontSize: 10, fill: '#9ca3af' }}
            axisLine={false}
          />
          <Radar
            name="DataScore"
            dataKey="value"
            stroke="#3b82f6"
            fill="#3b82f6"
            fillOpacity={0.2}
            strokeWidth={2}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
          />
          <Tooltip content={<CustomTooltip />} />
        </RadarChart>
      </ResponsiveContainer>

      {/* Chart description */}
      <div className="mt-2 text-center">
        <p className="text-xs text-gray-500">
          {scores ? t('artifacts.radarChart') : t('artifacts.noArtifactsDescription')}
        </p>
      </div>
    </div>
  )
}

export default DataScoreRadarChart