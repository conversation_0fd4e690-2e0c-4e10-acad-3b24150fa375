# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This project has a new branch named **DataScore** - a clinical research data management platform with knowledgebase functionality for medical professionals.

## Development Commands

### Build Commands
- `bun run dev` - Start development server on port 3000 with hot reload
- `bun run build` - Production build (same as build:prod)
- `bun run build:dev` - Development build
- `bun run build:test` - Test environment build
- `bun run build:prod` - Production build
- `bun run build:international` - International environment build
- `bun run preview` - Preview built application

### Code Quality
- `bun run lint` - Run Biome linter with auto-fix and TypeScript type checking
- `bun run format` - Format code using Biome formatter

### Package Manager
This project uses **Bun** as the package manager. Always use `bun install` instead of npm/yarn.

## Project Architecture

### Technology Stack
- **React 18** with TypeScript
- **Vite** as build tool with SWC for Fast Refresh
- **Bun** for package management
- **Biome** for linting and formatting (replaces ESL<PERSON>/Prettier)
- **TailwindCSS** with Tailwind Typography plugin for styling
- **React Router Dom v7** for routing
- **Antd** as UI component library
- **i18next** for internationalization

### Environment System
The application supports multiple deployment environments controlled by `REACT_APP_ENV`:
- `development` - Local development
- `test` - Test environment (ai.medon.com.cn)
- `production` - Production environment (ai.medsci.cn)
- `international` - International deployment

Environment configuration is handled by `src/utils/envConfig.ts` which provides environment-specific API bases, default emails, and feature flags.

### API Architecture
- **Dual API System**: Uses both Dify API and XAI API through a unified API manager
- **API Manager**: `src/utils/apiManager.ts` creates API instances with automatic token management
- **API Deduplication**: `src/utils/apiCallDeduplicator.ts` prevents duplicate API calls with cooldown mechanisms
- **Base Request**: `src/api/src/base-request.ts` handles HTTP requests with error handling

### Internationalization (i18n)
- **Simplified i18n**: Uses custom lightweight i18n implementation (`src/i18n/simple.ts`)
- **Supported Languages**: Chinese (zh) and English (en)
- **Language Detection**: Automatic language detection based on environment (international = English, others = Chinese)
- **Route-based i18n**: URLs include language prefix (e.g., `/zh/novax-base`, `/en/novax-base`)

### Routing Structure
The app uses a hierarchical routing system:
- `/` - Home page
- `/:lang/login` - Login page (not available in XAI environment)
- `/:lang/knowledgebase` - DataScore knowledgebase (NEW)
- `/:lang/:appName` - Application home page
- `/:lang/:appName/new` - New conversation
- `/:lang/:appName/:sessionId` - Specific conversation
- `/:lang/cases/:caseId` - Case examples

### State Management
- **React State + Hooks**: No external state management library
- **Cookie-based Authentication**: Uses `js-cookie` for token management
- **Custom Hooks**:
  - `useLoginStatus.ts` - Authentication state
  - `useCookieMonitor.ts` - Cookie change monitoring
  - `useConfigCache.ts` - Configuration caching

### Key Components Structure
- **App.tsx**: Main application entry with routing and authentication
- **ChatDetail.tsx**: Main chat interface component
- **Home.tsx**: Landing page with application selection
- **ConversationList.tsx**: Chat history sidebar
- **Sidebar.tsx**: Navigation sidebar with knowledgebase access
- **LoginManager.tsx**: Handles seamless login without page refresh
- **TokenSyncManager.tsx**: Ensures API tokens stay synchronized

### DataScore Knowledgebase Components
- **Knowledgebase.tsx**: Main knowledgebase page with layout and title
- **KnowledgebaseContent.tsx**: Core file/folder management functionality
- **FilePreviewModal.tsx**: Document preview modal system
- **knowledgebaseStorage.ts**: Local storage management utility (API-ready)

### DataScore Artifacts Drawer System
- **ArtifactsDrawer.tsx**: Resizable drawer component for DataScore analysis artifacts
- **DataScoreRadarChart.tsx**: Pentagon radar chart component with interactive tooltips (0-100 scale)
- **Resizable Interface**: Smooth drag-to-resize functionality with 25%-75% viewport width constraints
- **Layout Integration**: Pushes main content area in both Home.tsx and ChatDetail.tsx
- **Visual Design**: Clean three-dot drag handle with hover feedback and smooth transitions

### File Upload System
- **Unified Upload Component**: `src/components/file-upload/FileUpload.tsx`
- **Custom Hook**: `useFileUpload.ts` for upload logic
- **File Type Detection**: `src/api/src/utils/file-type.ts`
- **Upload Status Management**: Real-time upload progress and status

### Utilities and Helpers
- **Cache Management**: `src/utils/cacheManager.ts` for application caching
- **Performance Monitoring**: `src/utils/performanceMonitor.ts` for performance tracking
- **API Timing Manager**: `src/utils/apiTimingManager.ts` for API call timing
- **Conversation Refresh**: `src/utils/conversationRefreshManager.ts` for chat history updates
- **Login Event Manager**: `src/utils/loginEventManager.ts` for login state synchronization

### Styling System
- **TailwindCSS**: Primary styling framework
- **CSS Modules**: Component-specific styles where needed
- **Antd Theming**: UI components styled with Antd
- **Responsive Design**: Mobile-first approach with responsive layouts

## Application Types
The system supports multiple AI applications:
- **NovaX Base**: Core research idea generation
- **NovaX Pro**: Advanced research planning with personalization
- **NovaX Ultra**: Strategic research cluster planning
- **ElaVaX Base/Pro/Ultra**: Research evaluation and optimization variants

## DataScore Features
The DataScore branch includes comprehensive data analysis and management systems:

### Knowledgebase System
- **File Management**: Upload, organize, rename, delete, and download files
- **Folder Structure**: Create and navigate hierarchical folder systems
- **Data Categorization**: Automatic classification into "结构型" (structured) and "文档型" (document) types
- **Universal File Support**: All file formats supported including documents, images, videos, audio, archives
- **Interactive UI**: Large square cards with hover animations, drag-and-drop functionality
- **Smart Navigation**: Flexible breadcrumb system that adapts to folder depth
- **Preview System**: Foundation for document preview functionality
- **User Isolation**: Each medical professional has their own secure knowledgebase

### Artifacts Drawer System
- **Resizable Interface**: Drag-to-resize drawer with smooth linear dragging (25%-75% viewport width)
- **Pentagon Radar Chart**: Interactive 5-category scoring visualization with hover tooltips
- **Scoring Categories**: 样本量, 数据完整度, 结构化程度, 终点与结局事件, 创新潜力与延展性
- **Debug Functionality**: Random score generation for testing (20-100 range)
- **Layout Integration**: Pushes main content in both home and chat views
- **Header Toggle**: Accessible via button in header for datascore-base application
- **Visual Design**: Clean three-dot drag handle with hover feedback and state transitions
- **Artifacts Management**: Foundation for displaying generated analysis reports and files

## Code Standards
- **TypeScript**: Strict mode enabled with comprehensive type checking
- **Biome Configuration**: Custom rules in `biome.json` with accessibility rules mostly disabled
- **Code Style**: Double quotes, 2-space indentation, space-based formatting
- **Import Organization**: Automatic import sorting enabled via Biome

## Development Notes
- **JSX Import Source**: Uses `bippy/dist` for JSX runtime (non-standard React setup)
- **Mermaid Integration**: Complex Mermaid diagram rendering with error handling and performance optimization
- **Performance Optimizations**: Extensive use of React optimization patterns, memoization, and efficient re-rendering strategies
- **Error Boundaries**: Comprehensive error handling throughout the application
- **Token Management**: Sophisticated token refresh and synchronization system across multiple API endpoints