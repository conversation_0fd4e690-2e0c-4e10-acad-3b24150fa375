server {
  listen 80;
  listen 443 ssl;
  server_name medxy.ai www.medxy.ai;

  index  index.shtml index.htm index.php index.html;
  #root  /var/www/html/medxy.ai/public;

  ssl_certificate  /etc/letsencrypt/live/medxy.ai/fullchain.cer;
  ssl_certificate_key /etc/letsencrypt/live/medxy.ai/medxy.ai.key;

  ssl_session_timeout 5m;
  ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
  ssl_protocols TLSv1.2;
  ssl_prefer_server_ciphers on;
#---------------------------------------------------------
  proxy_connect_timeout 600s;
  proxy_read_timeout 3600s;  
  proxy_send_timeout 3600s;

# 只强制HTTPS，不强制www前缀
if ($scheme = http) {
  return 301 https://$host$request_uri;
  }
#---------------------------------------------------------
location ~* /(?!robots).*\.txt {
  root /u/medsci/docker-pro/novax-wordpress/wordpress;
  }
location ~* "\.(sql|bak|zip|tgz|tar|gz|gz|rar|RAR|bz2|7z|sh|py|bz|Z|ARJ|arj|ISO|ace)$" {
  return 403;
  }
location ~* /actuator/ {
  deny all;
  }
location ~ /\.(?!well-known).* {
  deny all;
  }
#---------------------------------------------------------
location ~* /(google|ByteDanceVerify|609d8|(.*)verify)(.*)\.html {
  root /u/medsci/docker-pro/novax-wordpress/wordpress;
  }
#---------------------------------------------------------
# novax.html 静态文件路由
location = /novax {
  proxy_pass http://127.0.0.1:3000/novax.html;
  proxy_http_version 1.1;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;

  # 静态文件缓存设置
  proxy_cache_valid 200 1h;
  add_header Cache-Control "public, max-age=3600";
}
location = /elavax {
  proxy_pass http://127.0.0.1:3000/elavax.html;
  proxy_http_version 1.1;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;

  # 静态文件缓存设置
  proxy_cache_valid 200 1h;
  add_header Cache-Control "public, max-age=3600";
}

# 默认路由
location / {
  proxy_pass http://127.0.0.1:3000;
  proxy_http_version 1.1;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection "upgrade";

  proxy_redirect http://$host:3000/ /;
 }
  access_log /u/medsci/logs/nginx/www.medxy.ai.log access;
}