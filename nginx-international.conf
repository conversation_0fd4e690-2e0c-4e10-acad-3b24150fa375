server {
  listen       3000;
  server_name  localhost;
  server_tokens off;
  root /usr/share/nginx/html;
  index index.html;
    
  client_max_body_size 256m;
  client_header_buffer_size 10240k;
  large_client_header_buffers 6 10240k;
  # ========================================================================
  # 代理超时配置
  # ========================================================================
  proxy_connect_timeout 300s;
  proxy_read_timeout 300s;
  proxy_send_timeout 300s;
  # ========================================================================
  # 安全头配置
  # ========================================================================
  add_header X-Frame-Options "SAMEORIGIN" always;
  add_header X-Content-Type-Options "nosniff" always;
  add_header X-XSS-Protection "1; mode=block" always;
  add_header Referrer-Policy "strict-origin-when-cross-origin" always;
  add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://ai.medsci.cn;" always;
  
  # ========================================================================
  # Gzip压缩配置
  # ========================================================================
  gzip on;
  gzip_vary on;
  gzip_min_length 1024;
  gzip_comp_level 6;
  gzip_types
      text/plain
      text/css
      text/xml
      text/javascript
      application/json
      application/javascript
      application/xml+rss
      application/atom+xml
      image/svg+xml;
  # ========================================================================
  # API代理配置
  # ========================================================================
  
  # 代理所有API请求到主站
  location /dev-api/ {
      # 代理到主站API
      proxy_pass https://ai.medsci.cn/dev-api/;
      # 代理头设置
      proxy_set_header Host ai.medsci.cn;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Original-Host $host;
      # CORS处理
      proxy_set_header Origin https://ai.medsci.cn;
      # 超时设置
      proxy_connect_timeout 300s;
      proxy_read_timeout 600s;
      proxy_send_timeout 600s;
      
      # 流式响应优化
      proxy_buffering off;
      proxy_cache off;
      proxy_http_version 1.1;
      # 禁用缓存
      add_header Cache-Control "no-cache, no-store, must-revalidate";
      add_header Pragma "no-cache";
      add_header Expires "0";
  }
  
  # ========================================================================
  # 静态资源配置（高优先级，使用^~前缀匹配）
  # ========================================================================

  # novax.html 静态文件（最高优先级）
  location = /novax.html {
      expires 1h;
      add_header Cache-Control "public, max-age=3600";
      add_header Access-Control-Allow-Origin "*";

      # 安全头
      add_header X-Frame-Options "SAMEORIGIN";
      add_header X-Content-Type-Options "nosniff";

      try_files $uri =404;
  }
  location = /elavax.html {
      expires 1h;
      add_header Cache-Control "public, max-age=3600";
      add_header Access-Control-Allow-Origin "*";

      # 安全头
      add_header X-Frame-Options "SAMEORIGIN";
      add_header X-Content-Type-Options "nosniff";

      try_files $uri =404;
  }

  # assets目录静态资源（最高优先级）
  location ^~ /assets/ {
      expires 1y;
      add_header Cache-Control "public, immutable";
      add_header Access-Control-Allow-Origin "*";

      # 尝试直接提供文件，如果不存在则返回404
      try_files $uri =404;
  }

  # static目录静态资源
  location ^~ /static/ {
      expires 1y;
      add_header Cache-Control "public, immutable";
      add_header Access-Control-Allow-Origin "*";

      try_files $uri =404;
  }

  # 字体文件缓存（通过文件扩展名匹配）
  location ~* \.(woff|woff2|ttf|eot)$ {
      expires 1y;
      add_header Cache-Control "public, immutable";
      add_header Access-Control-Allow-Origin "*";
  }

  # 图片资源缓存
  location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
      expires 1M;
      add_header Cache-Control "public";
  }

  # CSS和JS文件缓存（除了assets目录，因为assets已经被上面的规则处理）
  location ~* ^/(?!assets/).*\.(css|js)$ {
      expires 1y;
      add_header Cache-Control "public, immutable";
  }

  # 根目录常见静态文件
  location ~* ^/(favicon\.ico|robots\.txt|sitemap\.xml|manifest\.json)$ {
      expires 1M;
      add_header Cache-Control "public";
      try_files $uri =404;
  }

  # 其他静态资源
  location ~* \.(pdf|doc|docx|xls|xlsx|ppt|pptx)$ {
      expires 1M;
      add_header Cache-Control "public";
  }
  
  # ========================================================================
  # 预渲染SEO页面配置（最高优先级）
  # ========================================================================

  # 预渲染的多语言SEO页面 - 精确匹配基础路径
  location ~ ^/(zh|en)/(ai-doctor|derma-muse|novax-base|novax-pro|elavax-base|elavax-pro|datascore-base|knowledgebase|litra-x)/$ {
      # 优先尝试预渲染文件，然后回退到SPA
      try_files $uri $uri/index.html /index.html;

      # SEO页面缓存策略
      expires 2h;
      add_header Cache-Control "public, max-age=7200";

      # SEO优化头
      add_header X-Robots-Tag "index, follow";
      add_header X-Content-Type-Options "nosniff";
      add_header X-Frame-Options "SAMEORIGIN";
  }

  # ========================================================================
  # SPA路由支持配置
  # ========================================================================

  # 国际化路由支持 - 英文版应用（带子路径）
  # 支持路径：/en/{app-name}/new, /en/{app-name}/{session-id}
  location ~ ^/en/(ai-doctor|derma-muse|novax-base|novax-pro|novax-ultra|elavax-base|elavax-pro|elavax-ultra|datascore-base|knowledgebase|litra-x)(/.+)$ {
      try_files $uri $uri/ /index.html;

      # 禁用缓存HTML文件
      add_header Cache-Control "no-cache, no-store, must-revalidate";
      add_header Pragma "no-cache";
      add_header Expires "0";
  }

  # 国际化路由支持 - 中文版应用（带子路径）
  # 支持路径：/zh/{app-name}/new, /zh/{app-name}/{session-id}
  location ~ ^/zh/(ai-doctor|derma-muse|novax-base|novax-pro|novax-ultra|elavax-base|elavax-pro|elavax-ultra|datascore-base|knowledgebase|litra-x)(/.+)$ {
      try_files $uri $uri/ /index.html;

      # 禁用缓存HTML文件
      add_header Cache-Control "no-cache, no-store, must-revalidate";
      add_header Pragma "no-cache";
      add_header Expires "0";
  }

  # 国际化路由支持 - 不带尾部斜杠的基础路径（重定向到带斜杠版本以匹配预渲染）
  location ~ ^/(zh|en)/(ai-doctor|derma-muse|novax-base|novax-pro|elavax-base|elavax-pro|datascore-base|knowledgebase|litra-x)$ {
      return 301 $scheme://$host$uri/;
  }

  # 案例页面路由支持
  # 支持路径：/{lang}/cases/{case-id}
  location ~ ^/(en|zh)/cases/[^/]+(/.*)? {
      try_files $uri $uri/ /index.html;

      # 禁用缓存HTML文件
      add_header Cache-Control "no-cache, no-store, must-revalidate";
      add_header Pragma "no-cache";
      add_header Expires "0";
  }
  
  # ========================================================================
  # 根路径和默认路由
  # ========================================================================

  # 根路径处理 - 让React应用处理路由重定向
  location = / {
      # 直接提供index.html，让React的I18nRouteWrapper处理重定向逻辑
      # 这样可以根据环境配置动态选择默认语言和应用
      try_files $uri /index.html;
  }

  # 语言根路径处理 - 让React应用处理重定向
  location = /en {
      # 让React的I18nRouteWrapper处理 /en -> /en/{first-app} 的重定向
      try_files $uri /index.html;
  }

  location = /zh {
      # 让React的I18nRouteWrapper处理 /zh -> /zh/{first-app} 的重定向
      try_files $uri /index.html;
  }

  # 带尾部斜杠的语言根路径处理 - 修复403问题
  location = /en/ {
      # 让React的I18nRouteWrapper处理 /en/ -> /en/{first-app} 的重定向
      try_files $uri /index.html;
  }

  location = /zh/ {
      # 让React的I18nRouteWrapper处理 /zh/ -> /zh/{first-app} 的重定向
      try_files $uri /index.html;
  }

  # 处理不带语言前缀的应用路径（仅限已知应用名）
  location ~ ^/(novax-base|novax-pro|novax-ultra|elavax-base|elavax-pro|elavax-ultra|knowledgebase|litra-x)(/.*)? {
      return 302 /en$request_uri;
  }
  
  # ========================================================================
  # 主HTML文件配置
  # ========================================================================
  
  # 主应用HTML文件
  location = /index.html {
      # 禁用缓存
      add_header Cache-Control "no-cache, no-store, must-revalidate";
      add_header Pragma "no-cache";
      add_header Expires "0";
      
      # 安全头
      add_header X-Frame-Options "SAMEORIGIN";
      add_header X-Content-Type-Options "nosniff";
  }
  # ========================================================================
  # 通用fallback规则
  # ========================================================================
  # 所有其他路径都fallback到index.html（由前端路由处理）
  # React应用会通过路由匹配显示相应页面，未匹配的显示404页面
  location / {
      try_files $uri $uri/ /index.html;

      # 禁用缓存HTML文件
      add_header Cache-Control "no-cache, no-store, must-revalidate";
      add_header Pragma "no-cache";
      add_header Expires "0";
  }
  # ========================================================================
  # 错误页面配置
  # ========================================================================
  # 404错误显示静态404页面
  error_page 404 /404.html;
  error_page 500 502 503 504 /50x.html;

  # 404页面配置
  location = /404.html {
      root /usr/share/nginx/html;
      internal;
  }

  location = /50x.html {
      root /usr/share/nginx/html;
  }
  # ========================================================================
  # 日志配置
  # ========================================================================
  #access_log /var/log/nginx/medxy.ai.access.log combined;
  #error_log /var/log/nginx/medxy.ai.error.log warn;
}
