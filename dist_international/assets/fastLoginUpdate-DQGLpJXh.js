import{a as n}from"./index-C26nOUE6.js";class r{static triggerImmediateUpdate(t){if(!t){const e=n.get("userInfo");if(e)try{t=JSON.parse(e)}catch{return}else{console.warn("没有找到用户信息");return}}[new CustomEvent("userInfoUpdated",{detail:{userInfo:t,immediate:!0}}),new CustomEvent("loginStatusChanged",{detail:{type:"login",immediate:!0}}),new CustomEvent("fastLoginUpdate",{detail:{userInfo:t,timestamp:Date.now()}})].forEach(e=>{window.dispatchEvent(e)})}static forceRefreshUserInfo(){const t=n.get("userInfo"),s=n.get("medxyToken");if(t&&s)try{const e=JSON.parse(t);window.dispatchEvent(new CustomEvent("forceUserInfoRefresh",{detail:{userInfo:e,token:s,timestamp:Date.now()}})),this.triggerImmediateUpdate(e)}catch{}else console.warn("没有找到完整的登录信息")}static checkAndFixDisplayDelay(){const t=n.get("userInfo"),s=n.get("medxyToken");if(t&&s){const e=document.querySelectorAll('[data-testid="login-button"], button:contains("登录")'),i=document.querySelectorAll('[data-testid="user-avatar"], img[alt*="avatar"]');e.length>0&&i.length===0&&(this.forceRefreshUserInfo(),setTimeout(()=>{this.checkAndFixDisplayDelay()},500))}}static initFastUpdateListener(){window.addEventListener("loginSuccess",o=>{var a;(a=o.detail)!=null&&a.userInfo?this.triggerImmediateUpdate(o.detail.userInfo):setTimeout(()=>{this.triggerImmediateUpdate()},5)});let t=n.get("userInfo"),s=n.get("medxyToken");const e=()=>{const o=n.get("userInfo"),a=n.get("medxyToken");(o!==t||a!==s)&&(o&&a&&this.triggerImmediateUpdate(),t=o,s=a)},i=setInterval(e,100);setTimeout(()=>{clearInterval(i),setInterval(e,1e3)},5e3)}static initDebugTools(){}}r.initFastUpdateListener();r.initDebugTools();export{r as FastLoginUpdate,r as default};
