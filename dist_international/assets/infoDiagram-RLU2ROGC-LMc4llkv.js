import{_ as e,l as s,I as n,f as i,K as p}from"./index-C26nOUE6.js";import{p as g}from"./treemap-FKARHQ26-DFT0woX9.js";import"./_baseUniq-B4yQh1tR.js";import"./_basePickBy-ChRtxSCf.js";import"./clone-CT7VYSm_.js";var v={parse:e(async r=>{const a=await g("info",r);s.debug(a)},"parse")},d={version:p.version+""},m=e(()=>d.version,"getVersion"),c={getVersion:m},f=e((r,a,o)=>{s.debug(`rendering info diagram
`+r);const t=n(a);i(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${o}`)},"draw"),l={draw:f},S={parser:v,db:c,renderer:l};export{S as diagram};
