import{e as v,c as O,g,k as P,h as p,j as c,l as w,m as A,n as x,t as E,o as N}from"./_baseUniq-B4yQh1tR.js";import{bc as b,aE as F,bd as M,be as _,bf as $,bg as I,bh as B,bi as T,bj as y,bk as S}from"./index-C26nOUE6.js";var G=/\s/;function H(n){for(var r=n.length;r--&&G.test(n.charAt(r)););return r}var L=/^\s+/;function R(n){return n&&n.slice(0,H(n)+1).replace(L,"")}var m=NaN,q=/^[-+]0x[0-9a-f]+$/i,z=/^0b[01]+$/i,C=/^0o[0-7]+$/i,K=parseInt;function W(n){if(typeof n=="number")return n;if(v(n))return m;if(b(n)){var r=typeof n.valueOf=="function"?n.valueOf():n;n=b(r)?r+"":r}if(typeof n!="string")return n===0?n:+n;n=R(n);var t=z.test(n);return t||C.test(n)?K(n.slice(2),t?2:8):q.test(n)?m:+n}var o=1/0,X=17976931348623157e292;function Y(n){if(!n)return n===0?n:0;if(n=W(n),n===o||n===-o){var r=n<0?-1:1;return r*X}return n===n?n:0}function D(n){var r=Y(n),t=r%1;return r===r?t?r-t:r:0}function fn(n){var r=n==null?0:n.length;return r?O(n):[]}var l=Object.prototype,J=l.hasOwnProperty,dn=F(function(n,r){n=Object(n);var t=-1,a=r.length,e=a>2?r[2]:void 0;for(e&&M(r[0],r[1],e)&&(a=1);++t<a;)for(var f=r[t],i=_(f),s=-1,d=i.length;++s<d;){var u=i[s],h=n[u];(h===void 0||$(h,l[u])&&!J.call(n,u))&&(n[u]=f[u])}return n});function un(n){var r=n==null?0:n.length;return r?n[r-1]:void 0}function Q(n){return function(r,t,a){var e=Object(r);if(!I(r)){var f=g(t);r=P(r),t=function(s){return f(e[s],s,e)}}var i=n(r,t,a);return i>-1?e[f?r[i]:i]:void 0}}var U=Math.max;function Z(n,r,t){var a=n==null?0:n.length;if(!a)return-1;var e=t==null?0:D(t);return e<0&&(e=U(a+e,0)),p(n,g(r),e)}var hn=Q(Z);function V(n,r){var t=-1,a=I(n)?Array(n.length):[];return c(n,function(e,f,i){a[++t]=r(e,f,i)}),a}function bn(n,r){var t=B(n)?w:V;return t(n,g(r))}var j=Object.prototype,k=j.hasOwnProperty;function nn(n,r){return n!=null&&k.call(n,r)}function gn(n,r){return n!=null&&A(n,r,nn)}function rn(n,r){return n<r}function tn(n,r,t){for(var a=-1,e=n.length;++a<e;){var f=n[a],i=r(f);if(i!=null&&(s===void 0?i===i&&!v(i):t(i,s)))var s=i,d=f}return d}function mn(n){return n&&n.length?tn(n,T,rn):void 0}function en(n,r,t,a){if(!b(n))return n;r=x(r,n);for(var e=-1,f=r.length,i=f-1,s=n;s!=null&&++e<f;){var d=E(r[e]),u=t;if(d==="__proto__"||d==="constructor"||d==="prototype")return n;if(e!=i){var h=s[d];u=void 0,u===void 0&&(u=b(h)?h:y(r[e+1])?[]:{})}S(s,d,u),s=s[d]}return n}function on(n,r,t){for(var a=-1,e=r.length,f={};++a<e;){var i=r[a],s=N(n,i);t(s,i)&&en(f,x(i,n),s)}return f}export{rn as a,tn as b,V as c,on as d,mn as e,fn as f,hn as g,gn as h,dn as i,D as j,un as l,bn as m,Y as t};
