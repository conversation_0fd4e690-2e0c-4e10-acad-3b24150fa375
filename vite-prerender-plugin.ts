import fs from 'fs';
import path from 'path';
import type { Plugin } from 'vite';

/**
 * Vite 预渲染插件
 * 在构建完成后自动复制和修改 HTML 文件到对应的路由目录
 */
export function prerenderPlugin(): Plugin {
  return {
    name: 'vite-prerender-plugin',
    configurePreviewServer(server) {
      // 预览模式下的路由处理
      server.middlewares.use((req, res, next) => {
        const url = req.url?.split('?')[0];

        if (url && url !== '/') {
          const filePath = path.join('dist', url, 'index.html');

          if (fs.existsSync(filePath)) {
            // 如果预渲染文件存在，直接提供
            const content = fs.readFileSync(filePath, 'utf-8');
            res.setHeader('Content-Type', 'text/html');
            res.end(content);
            return;
          }
        }

        next();
      });
    },
    closeBundle() {
      console.log('🎯 开始处理预渲染文件...');
      
      const distDir = 'dist';
      const templatePath = path.join(distDir, 'index.html');
      
      if (!fs.existsSync(templatePath)) {
        console.error('❌ 模板文件不存在:', templatePath);
        return;
      }
      
      // 应用配置 - 基础信息
      const appConfigs = {
        'novax-base': {
          zh: { title: '科研灵感引擎 | 创新思路设计工具', description: '快速生成科研创新思路！输入研究背景，快速获取科研方向设计、实验思路与初步构想，助力启动研究项目', keywords: '科研思路设计,创新灵感,课题方向,实验设计,科研助手,研究构想' },
          en: { title: 'Research Inspiration Engine | Innovation Design Tool', description: 'Quickly generate innovative research ideas! Input research background to rapidly obtain research direction design, experimental ideas and preliminary concepts to help launch research projects', keywords: 'Research Design,Innovation Inspiration,Project Direction,Experimental Design,Research Assistant,Research Concepts' }
        },
        'novax-pro': {
          zh: { title: '科研灵感引擎 Pro | 高级创新思路设计工具', description: '快速生成科研创新思路！输入研究背景，快速获取科研方向设计、实验思路与初步构想，助力启动研究项目', keywords: '科研思路设计,创新灵感,课题方向,实验设计,科研助手,研究构想' },
          en: { title: 'Research Inspiration Engine Pro | Advanced Innovation Design Tool', description: 'Quickly generate innovative research ideas! Input research background to rapidly obtain research direction design, experimental ideas and preliminary concepts to help launch research projects', keywords: 'Research Design,Innovation Inspiration,Project Direction,Experimental Design,Research Assistant,Research Concepts' }
        },
        'elavax-base': {
          zh: { title: '科研机遇洞察平台 | 学术发展智能匹配', description: '科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展', keywords: '科研机遇洞察,学术平台匹配,期刊推荐,研究转化,AI学术专家' },
          en: { title: 'Research Opportunity Insight Platform | Academic Development Intelligence Matching', description: 'Intelligent research development opportunity analysis tool! Deep interpretation of research results, precise matching of target journals/platforms, mining academic collaboration and transformation opportunities to boost career development', keywords: 'Research Opportunity Insight,Academic Platform Matching,Journal Recommendation,Research Translation,AI Academic Expert' }
        },
        'elavax-pro': {
          zh: { title: '科研机遇洞察平台 Pro | 高级学术发展智能匹配', description: '科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展', keywords: '科研机遇洞察,学术平台匹配,期刊推荐,研究转化,AI学术专家' },
          en: { title: 'Research Opportunity Insight Platform Pro | Advanced Academic Development Intelligence Matching', description: 'Intelligent research development opportunity analysis tool! Deep interpretation of research results, precise matching of target journals/platforms, mining academic collaboration and transformation opportunities to boost career development', keywords: 'Research Opportunity Insight,Academic Platform Matching,Journal Recommendation,Research Translation,AI Academic Expert' }
        },
        'datascore-base': {
          zh: { title: 'DataScore Base - 数据评分 | Medxy AI', description: 'DataScore Base提供专业的数据质量评分和分析服务。', keywords: 'DataScore Base,数据评分,数据质量,医学AI' },
          en: { title: 'DataScore Base - Data Scoring | Medxy AI', description: 'DataScore Base provides professional data quality scoring and analysis services.', keywords: 'DataScore Base,Data Scoring,Data Quality,Medical AI' }
        }
      };

      // 生成路由配置
      const routes: Array<{path: string, title: string, description: string, keywords: string}> = [];
      Object.keys(appConfigs).forEach(appName => {
        ['zh', 'en'].forEach(lang => {
          const config = appConfigs[appName as keyof typeof appConfigs][lang as 'zh' | 'en'];
          routes.push({
            path: `/${lang}/${appName}`,
            title: config.title,
            description: config.description,
            keywords: config.keywords
          });
        });
      });
      
      // 读取模板文件
      const template = fs.readFileSync(templatePath, 'utf-8');
      
      routes.forEach(route => {
        try {
          // 创建目录
          const routeDir = path.join(distDir, route.path);
          fs.mkdirSync(routeDir, { recursive: true });
          
          // 修改 HTML 内容
          let html = template;

          // 计算相对路径深度
          const pathSegments = route.path.split('/').filter(Boolean);
          const depth = pathSegments.length;
          const relativePath = depth > 0 ? '../'.repeat(depth) : './';

          // 修复资源路径为相对路径
          // 处理所有以 ./ 开头的资源路径
          html = html.replace(/src="\.\/([^"]+)"/g, (_, path) => {
            // 如果是 js 文件夹下的文件，使用绝对路径
            if (path.startsWith('js/')) {
              return `src="${route.path}/${path}"`;
            }
            // 其他资源使用相对路径
            return `src="${relativePath}${path}"`;
          });

          html = html.replace(/href="\.\/([^"]+)"/g, `href="${relativePath}$1"`);
          // 处理可能的其他属性
          html = html.replace(/url\(\.\/([^)]+)\)/g, `url(${relativePath}$1)`);

          // 更新 title
          html = html.replace(/<title>.*?<\/title>/, `<title>${route.title}</title>`);

          // 更新或添加 meta 标签
          html = html.replace(/<meta name="keywords".*?>/, `<meta name="keywords" content="${route.keywords}">`);
          html = html.replace(/<meta name="description".*?>/, `<meta name="description" content="${route.description}">`);

          // 更新 Open Graph 标签
          html = html.replace(/<meta property="og:title".*?>/, `<meta property="og:title" content="${route.title}">`);
          html = html.replace(/<meta property="og:description".*?>/, `<meta property="og:description" content="${route.description}">`);

          // 更新 Twitter Card 标签
          html = html.replace(/<meta name="twitter:title".*?>/, `<meta name="twitter:title" content="${route.title}">`);
          html = html.replace(/<meta name="twitter:description".*?>/, `<meta name="twitter:description" content="${route.description}">`);

          // 添加预渲染标记
          html = html.replace(/<div id="root"><\/div>/, `<div id="root" data-prerendered="true"></div>`);
          
          // 写入文件
          const outputPath = path.join(routeDir, 'index.html');
          fs.writeFileSync(outputPath, html, 'utf-8');
          
          console.log(`✅ 生成预渲染文件: ${route.path}`);
          
        } catch (error) {
          console.error(`❌ 生成预渲染文件失败 ${route.path}:`, (error as Error).message);
        }
      });
      
      console.log('🎉 预渲染文件处理完成！');
    }
  };
}
